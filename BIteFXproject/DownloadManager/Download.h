//
//  Download.h
//  TestingPlatform
//
//  Created by <PERSON> on 11/13/12.
//
//  Permission is hereby granted, free of charge, to any person obtaining a copy
//  of this software and associated documentation files (the "Software"), to deal
//  in the Software without restriction, including without limitation the rights
//  to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
//  copies of the Software, and to permit persons to whom the Software is
//  furnished to do so, subject to the following conditions:
//
//  The above copyright notice and this permission notice shall be included in
//  all copies or substantial portions of the Software.
//
//  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
//  IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
//  FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
//  AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
//  LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
//  OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
//  THE SOFTWARE.

#import <Foundation/Foundation.h>

@class Download;

@protocol DownloadDelegate <NSObject>

@optional

- (void)downloadDidFinishLoading:(Download *)download;
- (void)downloadDidFail:(Download *)download;
- (void)downloadDidReceiveData:(Download *)download;

@end

@interface Download : NSObject <NSURLConnectionDelegate>

@property (nonatomic, copy) NSString *filename;
@property (nonatomic, copy) NSURL *url;
@property (nonatomic, retain) id<DownloadDelegate> delegate;
@property (getter = isDownloading) BOOL downloading;
@property long long expectedContentLength;
@property long long progressContentLength;
@property (nonatomic, strong) NSError *error;

//- (id)initWithFilename:(NSString *)filename URL:(NSURL *)url delegate:(id<DownloadDelegate>)delegate;
- (void)start;
- (void)cancel;

@end
