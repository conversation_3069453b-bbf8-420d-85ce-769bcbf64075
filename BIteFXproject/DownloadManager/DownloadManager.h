//
//  DownloadManager.h
//  TestingPlatform
//
//  Created by <PERSON> on 11/21/12.
//
//  Permission is hereby granted, free of charge, to any person obtaining a copy
//  of this software and associated documentation files (the "Software"), to deal
//  in the Software without restriction, including without limitation the rights
//  to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
//  copies of the Software, and to permit persons to whom the Software is
//  furnished to do so, subject to the following conditions:
//
//  The above copyright notice and this permission notice shall be included in
//  all copies or substantial portions of the Software.
//
//  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
//  IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
//  FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
//  AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
//  LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
//  OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
//  THE SOFTWARE.

#import <Foundation/Foundation.h>
#import "Download.h"

@class DownloadManager;
@class Download;


@protocol DownloadManagerDelegate <NSObject>
@optional

- (void) didReceiveData: (NSMutableData *) theData;
- (void) reTryDownload: (NSString *) reason;
- (void) didReceiveFilename: (NSString *) aName;
- (void) dataDownloadFailed: (NSString *) reason;
- (void) dataDownloadAtPercent: (NSNumber *) aPercent;

- (void) removeDownloadView;

@end


@interface DownloadManager : NSObject <NSURLConnectionDelegate> {
    
    int isDownloadFailed;
    BOOL isUpdatePath;
    
    NSString *aStrSuggetedFileName;
    
}

@property (nonatomic, assign) int isDownloadFailed;
@property (nonatomic, copy) NSString *filename;
@property (nonatomic, copy) NSString *filePath;
@property (nonatomic, copy) NSURL *url;
@property (strong, nonatomic) NSOutputStream *downloadStream;
@property (strong, nonatomic) NSURLConnection *connection;
@property (strong, nonatomic) NSString *tempFilename;
@property (strong, nonatomic) NSMutableData *data;
@property (nonatomic, assign) BOOL isDownloading;
@property long long expectedContentLength;
@property long long progressContentLength;
@property (nonatomic, strong) NSError *error;
@property (nonatomic, assign) BOOL isUpdatePath;

@property NSInteger maxConcurrentDownloads;
@property (nonatomic, strong) NSMutableArray *downloads;
@property (nonatomic, strong) id<DownloadManagerDelegate> delegate;

- (instancetype)initWithDelegate:(id<DownloadManagerDelegate>)delegate;
- (void)cancelAll;
- (void)start;
- (void)cancel;

- (void)cleanupConnectionSuccessful:(BOOL)success;
- (void)cleanupConnectionFinish:(BOOL)success withData:(NSMutableData *)data1 ;

- (void) cleanup;
+ (DownloadManager *) sharedInstance;
- (void) download:(NSString *)fileName path:(NSString *)filePath withURL:(NSURL *)fileURL;

@end
