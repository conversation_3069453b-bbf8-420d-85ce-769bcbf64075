//
//  DownloadManager.m
//  TestingPlatform
//
//  Created by <PERSON> on 11/21/12.
//


#import "DownloadManager.h"
#import "Download.h"
#import "Database.h"

#define DELEGATE_CALLBACK(X, Y) if (sharedInstance.delegate && [sharedInstance.delegate respondsToSelector:@selector(X)]) [sharedInstance.delegate performSelector:@selector(X) withObject:Y];
#define NUMBER(X) [NSNumber numberWithFloat:X]

static DownloadManager *sharedInstance = nil;

@interface DownloadManager () <DownloadDelegate>

@property (nonatomic) BOOL cancelAllInProgress;

@end

@implementation DownloadManager

@synthesize isDownloadFailed, isUpdatePath;
@synthesize filename, url, data, filePath;
@synthesize downloadStream, connection, tempFilename, isDownloading;
@synthesize expectedContentLength, progressContentLength, error;
@synthesize delegate, downloads, maxConcurrentDownloads;

- (instancetype)init {
    
    self = [super init];
    
    if (self) {
        
        downloads = [[NSMutableArray alloc] init];
        maxConcurrentDownloads = 12;
        
    }
    
    return self;
}


- (void) cleanup {
    
    //NSLog(@"download data is clean");
    if (self.connection != nil) {
        
//        self.connection;

    } else {
        
        self.connection = nil;
    }
	self.url = nil;
	self.isDownloading = NO;    
}
/*
#pragma mark - Alert Methos
- (void)alertView:(UIAlertView *)alertView clickedButtonAtIndex:(NSInteger)buttonIndex
{
    if(!appDelegate.isDownloadManually)
    {
        if(alertView.tag == 1122)
        {
            if(buttonIndex == 0)
            {
                NSString *sql = @"";
                
                if(appDelegate.isFullversionDownloading)
                {
                    //NSString *aTempStrURL = [DownloadFileURL stringByAppendingString:aStrSuggetedFileName];
                    NSString *videoFileURL = [NSString stringWithFormat:@"%@/%@",DownloadFileURL,aStrSuggetedFileName];
                    sql = [NSString stringWithFormat:@"update FileDownloadList set isSkipFile = 1 where FileURL = '%@'",videoFileURL];
                }
                else
                {
                    //videoFileURL = [NSString stringWithFormat:@"%@/%@",DownloadFileURL,aStrSuggetedFileName];
                    //sql = [NSString stringWithFormat:@"update FileUpdateList set isSkipFile = 1  where FileName = '%@'",aStrSuggetedFileName];
                    sql = [NSString stringWithFormat:@"update FileUpdateList set isSkipFile = 1, IsDownloaded = 0  where FileURL = '%@'",aStrSuggetedFileName];
                }
                
                NSLog(@"is skipped file ==> %@",aStrSuggetedFileName);
                [[Database shareDatabase] Update:sql];
                
                [self cleanupConnectionFinish:NO withData:self.data];
                
            }
        }
    }
    else
    {
        [self cancel];
        [self cleanup];
        [self.delegate removeDownloadView];
    }
}
*/


#pragma mark - DownloadManager public methods

- (void) download:(NSString *)fileName path:(NSString *)filePath1 withURL:(NSURL *)fileURL {
 
    if (self.isDownloading) {
        
		NSLog(@"Error: Cannot start new download until current download finishes");
		//DELEGATE_CALLBACK(dataDownloadFailed:, @"");  // Enble this
		return;
	}  
    
    /*
    NSString *documentsPath = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES)[0];
    NSString *downloadFolder = [documentsPath stringByAppendingPathComponent:@"BiteFXiPadFull/"];
    NSString *downloadFilename = [downloadFolder stringByAppendingPathComponent:[strURL lastPathComponent]];
    
    NSURL *url1 = [NSURL URLWithString:strURL];
     */
    
    self.filename = fileName;
    self.url = fileURL;
    self.filePath = filePath1;
    [self start];
}

- (void)addDownloadWithFilename:(NSString *)filename1 URL:(NSURL *)url1 {
    
    if (self.isDownloading) {
        
		NSLog(@"Error: Cannot start new download until current download finishes");
		//DELEGATE_CALLBACK(dataDownloadFailed:, @"");  // Enble this
		return;
	}
    
    self.filename = filename1;
    self.url = url1;
    [self start];
}


- (instancetype)initWithDelegate:(id<DownloadManagerDelegate>)delegate1 {
    
    self = [self init];
    
    if (self) {
        
        delegate = delegate1;
    }
    
    return self;
}

+ (DownloadManager *) sharedInstance {
    
	if(!sharedInstance)  {
        
        sharedInstance = [[self alloc] init];
    }
    return sharedInstance;
}


- (void)cancel {
    
    
    if (self.connection != nil)
    {
        [self.connection cancel];
        self.connection = nil;
    }
    self.isDownloading = NO;
    
    if(self.data)
    {
//        self.data;
        self.data = nil;
    }
    
    //[self cleanupConnectionSuccessful:NO];
}

- (void)start {
    
    //[self tryDownloading];
    
    // initialize progress variables
    
    self.isDownloading = YES;
    self.expectedContentLength = -1;
    self.progressContentLength = 0;
    
    // create the download file stream (so we can write the file as we download it
    /*
    //self.tempFilename = [self pathForTemporaryFileWithPrefix:@"downloads"];
    self.tempFilename = [self pathForTemporaryFileWithPrefix:@"BiteFXiPadFull/"];
    self.downloadStream = [NSOutputStream outputStreamToFileAtPath:self.tempFilename append:NO];
    
    if (!self.downloadStream)
    {
        self.error = [NSError errorWithDomain:[NSBundle mainBundle].bundleIdentifier
                                         code:-1
                                     userInfo:@{@"message": @"Unable to create NSOutputStream", @"function" : @(__FUNCTION__), @"path" : self.tempFilename}];
        
        [self cleanupConnectionSuccessful:NO];
        return;
    }
    [self.downloadStream open];
     */
    
    self.data = [[NSMutableData alloc] init];
    NSURLRequest *request = [NSURLRequest requestWithURL:self.url cachePolicy:NSURLRequestReloadIgnoringLocalCacheData timeoutInterval:10000];
    
    if (!request) {
        
        self.error = [NSError errorWithDomain:[NSBundle mainBundle].bundleIdentifier
                                         code:-1
                                     userInfo:@{@"message": @"Unable to create URL", @"function": @(__FUNCTION__), @"URL" : self.url}];
        
        [self cleanupConnectionSuccessful:NO];
        return;
    }
    
    self.connection = [NSURLConnection connectionWithRequest:request delegate:self];
    if (!self.connection)
    {
        self.error = [NSError errorWithDomain:[NSBundle mainBundle].bundleIdentifier
                                         code:-1
                                     userInfo:@{@"message": @"Unable to create NSURLConnection", @"function" : @(__FUNCTION__), @"NSURLRequest" : request}];
        
        [self cleanupConnectionSuccessful:NO];
    }
}

- (NSString *)pathForTemporaryFileWithPrefix:(NSString *)prefix {
    
    NSString *  result;
    CFUUIDRef   uuid;
    CFStringRef uuidStr;
    
    uuid = CFUUIDCreate(NULL);
    assert(uuid != NULL);
    
    uuidStr = CFUUIDCreateString(NULL, uuid);
    assert(uuidStr != NULL);
    
    result = [NSTemporaryDirectory() stringByAppendingPathComponent:[NSString stringWithFormat:@"%@-%@", prefix, uuidStr]];
    assert(result != nil);
    
    CFRelease(uuidStr);
    CFRelease(uuid);
    
    return result;
}


- (void)cancelAll {
    
    self.cancelAllInProgress = YES;
    
    while ((self.downloads).count > 0) {
        
        [(self.downloads)[0] cancel];
    }
    
    self.cancelAllInProgress = NO;
    //[self informDelegateThatDownloadsAreDone];
}


- (void)cleanupConnectionSuccessful:(BOOL)success {
    
    NSFileManager *fileManager = [NSFileManager defaultManager];
    NSError *error1;
    
    // clean up connection and download steam
    
    if (self.connection != nil)
    {
        if (!success)
            [self.connection cancel];
        self.connection = nil;
    }
    if (self.downloadStream != nil)
    {
        [self.downloadStream close];
        self.downloadStream = nil;
    }
    
    self.isDownloading = NO;
    
    // if successful, move file and clean up, otherwise just cleanup
    if (self.tempFilename)
        if ([fileManager fileExistsAtPath:self.tempFilename])
            [fileManager removeItemAtPath:self.tempFilename error:&error1];
    
    //[self.delegate downloadDidFail:self];
    if ([self.delegate respondsToSelector:@selector(dataDownloadFailed:)]) {
        [self.delegate dataDownloadFailed:@"Failed Connection"];
    }
}


- (void)cleanupConnectionFinish:(BOOL)success withData:(NSMutableData *)data1 {
    
    NSFileManager *fileManager = [NSFileManager defaultManager];
    NSError *error1;
    
    // clean up connection and download steam
    if (self.connection != nil)
    {
        if (!success)
            [self.connection cancel];
        self.connection = nil;
    }
    if (self.downloadStream != nil)
    {
        [self.downloadStream close];
        self.downloadStream = nil;
    }
    
    self.isDownloading = NO;
    
    // if successful, move file and clean up, otherwise just cleanup

    if (success)
    {
        if ([self.delegate respondsToSelector:@selector(didReceiveData:)]) {
            [self.delegate didReceiveData:data1];
            //[self per]
        }
        
    } else {
        
        if (self.tempFilename)
            if ([fileManager fileExistsAtPath:self.tempFilename])
                [fileManager removeItemAtPath:self.tempFilename error:&error1];
        
        //[self.delegate downloadDidFail:self];
        if ([self.delegate respondsToSelector:@selector(reTryDownload:)]) {
            [self.delegate reTryDownload:@"Failed Connection"];
        }
    }
    
}




#pragma mark - NSURLConnectionDataDelegate methods

- (void)connection:(NSURLConnection*)connection didReceiveResponse:(NSURLResponse *)response {
    
    (self.data).length = 0;
    if ([response isKindOfClass:[NSHTTPURLResponse class]]) {
        
        NSHTTPURLResponse *httpResponse = (id)response;
        NSInteger statusCode = httpResponse.statusCode;
        
        if (statusCode == 200) {

            self.expectedContentLength = response.expectedContentLength;
            
            aStrSuggetedFileName = response.URL.absoluteString;

            
            if(appDelegate.isUpdateDownloading)
            {
                NSLog(@"isUpdateDownloading ==> YES");
            }
            if(appDelegate.isFullversionDownloading)
            {
                NSLog(@"isFullversionDownloading ==>YES");
            }
            
//            NSString *aTempiPadString = [appDelegate platform];
            //if((![aTempiPadString isEqualToString:@"iPad1,1"] && appDelegate.isFullversionDownloading) || appDelegate.isUpdateDownloading) {
            //if((![aTempiPadString isEqualToString:@"iPad1,1"] && appDelegate.isFullversionDownloading) || (![aTempiPadString isEqualToString:@"iPad1,1"] && appDelegate.isUpdateDownloading)) {
            /*
            if((![aTempiPadString isEqualToString:@"iPad1,1"] && appDelegate.isUpdateDownloading)) {
                //if(((self.expectedContentLength/1024)/1024 + 1) >= ([[aTempArray objectAtIndex:1]intValue]/1024)/1024) {
                if(((self.expectedContentLength/1024)/1024) >= ([[aTempArray objectAtIndex:1]intValue]/1024)/1024) {
                    
                    NSLog(@"Inside Skip Alert");
                    UIAlertView *aTempAlert = [[UIAlertView alloc]initWithTitle:@"BiteFX" message:@"Your iPad does not have enough memory to download the animation, so it is not going to be downloaded. You can try downloading it again from the 'Skipped Animations' panel from Menu." delegate:self cancelButtonTitle:@"Skip" otherButtonTitles:nil];
                    aTempAlert.tag = 1122;
                    [aTempAlert show];
                    
                    [self cancel];
                    [self cleanup];
                }
            
            } else {
                
                //self.expectedContentLength = [response expectedContentLength];
                NSLog(@"Initial Expected Length:%llu",self.expectedContentLength);
                if ([response suggestedFilename]) {
                    
                    if ([delegate respondsToSelector:@selector(didReceiveFilename:)]) {
                        [delegate didReceiveFilename:[response suggestedFilename]];
                    }
                }
            }
            //Code End..
            */
            NSLog(@"Initial Expected Length:%llu",self.expectedContentLength);
            if (response.suggestedFilename) {
                
                if ([delegate respondsToSelector:@selector(didReceiveFilename:)]) {
                    [delegate didReceiveFilename:response.suggestedFilename];
                }
            }
      
        } else if (statusCode >= 400) {
            
            self.error = [NSError errorWithDomain:[NSBundle mainBundle].bundleIdentifier
                                             code:statusCode
                                         userInfo:@{
                          @"message" : @"bad HTTP response status code",
                          @"function": @(__FUNCTION__),
                          @"NSHTTPURLResponse" : response
                          }];
            
            [self cleanupConnectionSuccessful:NO];
            return;
        }
   
    } else {
        
        self.expectedContentLength = -1;
    }
}

- (void)connection:(NSURLConnection *)connection didReceiveData:(NSData *)data1 {
    
    if(!appDelegate.isDownloadCancelled) {
        
        if (!self.data) {
            
            self.data = [[NSMutableData alloc] init];
        }
        [self.data appendData:data1];
        self.progressContentLength = (self.data).length;
        
        float percent = (float)self.progressContentLength / (float)self.expectedContentLength;
        //DELEGATE_CALLBACK(dataDownloadAtPercent:, NUMBER(percent));
        if ([delegate respondsToSelector:@selector(dataDownloadAtPercent:)]) {
            [delegate dataDownloadAtPercent:@(percent)];
        }
        
    } else {
        
        [self.connection cancel];
        self.isDownloading = NO;
    }
}

- (void)connectionDidFinishLoading:(NSURLConnection *)connection {
    
    //NSLog(@"ExpectedLength:%llu",self.expectedContentLength);
    //NSLog(@"RecievedLength:%llu",self.progressContentLength);
    
    if (self.expectedContentLength == self.progressContentLength) {
        
        NSLog(@"Same Length");
        //NSLog(@"%@ download Completed", self.filename);
        
        [self cleanupConnectionFinish:YES withData:self.data];
        
    } else {
        
        NSLog(@"Different Length");
        NSLog(@"Skip File %@", self.filename);
    
        [self cleanupConnectionFinish:NO withData:self.data];
    }
}

- (void)connection:(NSURLConnection *)connection didFailWithError:(NSError *)error1 {
    
    self.error = error1;
    self.isDownloadFailed = 1;
    [self cleanupConnectionSuccessful:NO];
}


@end
