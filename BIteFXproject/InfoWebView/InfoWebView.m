//
//  InfoWebView.m
//  BIteFXproject
//
//  Created by indianic on 11/01/1938 SAKA.
//
//

#import "InfoWebView.h"
#import "AlwaysOpaqueImageView.h"
@implementation InfoWebView

/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/
- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        // Initialization code
        // initilize all your UIView components
        self.backgroundColor = [UIColor whiteColor];
//        webView = [[UIWebView alloc]initWithFrame:self.bounds];
        
        WKWebViewConfiguration *configuration = [[WKWebViewConfiguration alloc] init];
        
//        if (@available(iOS 14.0, *)) {
//            WKUserContentController *cntrl = [WKUserContentController new];
//            NSString *source = @"<header><meta name='viewport' content='width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no'></header>";
//            WKUserScript *scrp = [[WKUserScript init] initWithSource:source injectionTime:WKUserScriptInjectionTimeAtDocumentStart forMainFrameOnly:NO inContentWorld:[WKContentWorld defaultClientWorld]];
//
//            configuration.userContentController = cntrl;
//            [cntrl addUserScript:scrp];
//        }
        webView = [[WKWebView alloc] initWithFrame:CGRectMake(0, 0, self.frame.size.width, self.frame.size.height) configuration:configuration];
        webView.backgroundColor = [UIColor blackColor];
        if (@available(iOS 14.0, *)) {
            webView.pageZoom = 1;
        }
        [self addSubview:webView];
    }
    return self;
}
-(void)loadRequestWithURL:(NSString *)strUrl {

    //===================
    // Version 3.0 Changes
    //===================
    // UIWebView is replaced with WKWebView.
    
//    NSURL *url = [NSURL URLWithString:strUrl];
//    NSURLRequest *request = [NSURLRequest requestWithURL:url];
//    [webView loadRequest:request];
    
    // Version 3.0 Changes
    // When we load html files from document directory, give read access to document directory url to include css files...
    // Reference link: https://developer.apple.com/forums/thread/100125
    NSURL *aDocumentURL = [[NSFileManager defaultManager] URLsForDirectory:NSDocumentDirectory inDomains:NSUserDomainMask].lastObject;

    // Version 3.0 Changes
    // Condition is added to solve crash issue...
    [webView loadHTMLString:@"" baseURL:nil];
    
    if ([[NSFileManager defaultManager] fileExistsAtPath:strUrl]) {    
        NSURL *url = [NSURL fileURLWithPath:strUrl];
        
//        int Fulldownload = [[NSUserDefaults standardUserDefaults] boolForKey:@"FullDownloaded"];
        NSString *aStrFileName = strUrl.lastPathComponent;
        aStrFileName = aStrFileName.stringByDeletingPathExtension;
          
        NSString *aStrInfoPath = [[NSBundle mainBundle]pathForResource:aStrFileName ofType:@"html"];
          
        // Identify URL is Bundle URL or Document Directory URL...
        // First check in Bundle...
        if ([[NSFileManager defaultManager] fileExistsAtPath:aStrInfoPath]) {
            [webView loadFileURL:url allowingReadAccessToURL:url];
        } else {
            [webView loadFileURL:url allowingReadAccessToURL:aDocumentURL];
        }
        
    }
    
}
@end
