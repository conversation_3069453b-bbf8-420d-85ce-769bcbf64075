//
//  InAppPurchase.h
//  WalkInsBook
//
//  Created by IndiaNIC 05 on 7/21/11.
//  Copyright 2011 __MyCompanyName__. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <StoreKit/StoreKit.h>

@protocol InAppPurchaseDelegate;

@interface InAppPurchase : NSObject <SKProductsRequestDelegate,SKPaymentTransactionObserver> {
    
	NSString *productId;
	NSInteger tag;
	
	id <InAppPurchaseDelegate> delegate;
	NSString *receiptString;
	NSString *localPrice;
	BOOL isGettingPrice;
	
//	SKMutablePayment *payment;
    SKPayment *payment;
	NSMutableDictionary *bookInfo;
}

@property (nonatomic,strong) 	NSString *productId;
@property (readwrite)			NSInteger tag;
@property (nonatomic,strong) 	id <InAppPurchaseDelegate> delegate;
@property (nonatomic,strong) 	NSString *receiptString;
@property (readwrite)			BOOL isGettingPrice;
@property (nonatomic,strong,readonly) 	NSString *localPrice;
@property (nonatomic,strong) 	SKProduct *product;
@property (nonatomic,strong) 	NSMutableDictionary *bookInfo;

-(void)restorePurchase;
-(void) startInAppPurchase;
-(void)requestForStore;// Paresh //
-(NSString *) getCurrncyForProduct:(SKProduct *)product;

- (void)completeTransactionAfterVarification:(SKPaymentTransaction *)atransaction;

@end

@protocol InAppPurchaseDelegate  <NSObject>
@optional
//-(void) localPriceDidRecevied:(InAppPurchase  *) inAppPurchase;
-(void) localPriceDidRecevied:(InAppPurchase *) inAppPurchase1;
-(void) purchaseDidFinish:(InAppPurchase  *) inAppPurchase;
-(void) purchaseDidFail:(InAppPurchase  *) inAppPurchase;
-(void) localPriceDidFail:(InAppPurchase  *) inAppPurchase;
-(void) restoreDidFinish:(NSString  *) inAppPurchase;

- (void)callBeforeCompeleTransaction:(SKPaymentTransaction *)atransaction;

- (void)InsertReceiptIntoiCloud:(SKPaymentTransaction *)atransaction;

- (NSDate *)dateFromDate:(NSDate *)date afterNumberOfDays:(int)aInt;

@end

