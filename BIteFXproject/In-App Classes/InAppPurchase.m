//
//  InAppPurchase.m
//  
//
//  Created by IndiaNIC 05 on 7/21/11.
//  Copyright 2011 __MyCompanyName__. All rights reserved.
//

#import "InAppPurchase.h"
#import "Database.h"
@implementation InAppPurchase


@synthesize productId;
@synthesize tag;
@synthesize delegate;
@synthesize receiptString;
@synthesize isGettingPrice;
@synthesize localPrice;
@synthesize bookInfo;

-(void) startInAppPurchase {
	
    NSLog(@"Start InApp Purchase");
	if (!isGettingPrice) 
		[[UIApplication sharedApplication] beginIgnoringInteractionEvents];
    
	SKProductsRequest *request= [[SKProductsRequest alloc] initWithProductIdentifiers: [NSSet setWithObjects:productId,nil]];
	request.delegate = self;
//    [self requestForStore];
//    [[SKPaymentQueue defaultQueue] removeTransactionObserver:self];
//    [[SKPaymentQueue defaultQueue] addTransactionObserver:self];

	[request start];
    //	[request release];
}

-(void)restorePurchase
{
    [self requestForStore];
}



#pragma mark -
#pragma mark InAppPurchase delegate 

- (void)productsRequest:(SKProductsRequest *)request didReceiveResponse:(SKProductsResponse *)response {
	
    NSLog(@"DidReceive Response");
	if (!isGettingPrice) 
		[[UIApplication sharedApplication] endIgnoringInteractionEvents];
    
	//[[SKPaymentQueue defaultQueue] addTransactionObserver:self];
	
	NSArray *allProduct = response.products;
	
	if (allProduct.count == 0) 
    {
        
		NSLog(@"There is no product with this bundle identifier");
        if (isGettingPrice)
        {
            NSString *price = bookInfo[@"Price"];
            [bookInfo setValue:price  forKey:@"BookLocalPrice"];
            [delegate localPriceDidFail:self];
            return;
        }
	
    } else {
        
		if (isGettingPrice)  {
			
//            for (int i=0; i < [allProduct count]; i++) {

				SKProduct *product = allProduct[0];
				localPrice = [self getCurrncyForProduct:product];
                self.product = product;
                
                NSLog(@"str currncy %@ self = %@",localPrice,self);
				bookInfo[@"BookLocalPrice"] = localPrice;
				[delegate localPriceDidRecevied:self];
                
                [[NSNotificationCenter defaultCenter] postNotificationName:@"AppStorePriceUpdate" object:nil];
				[[SKPaymentQueue defaultQueue] removeTransactionObserver:self];
//                return ;

//            }

        }else{
            [[SKPaymentQueue defaultQueue] addTransactionObserver:self];
//            payment = [SKMutablePayment paymentWithProductIdentifier:productId];
            payment = [SKPayment paymentWithProduct:self.product];
            [[SKPaymentQueue defaultQueue] addPayment:payment];
            [[UIApplication sharedApplication] beginIgnoringInteractionEvents];
        }

	}
	
	
	//	SKMutablePayment *payment = [SKMutablePayment
	//                                 
	//								 paymentWithProductIdentifier:productId];
	
    
	
	//payment = [SKMutablePayment paymentWithProductIdentifier:productId];
	//[[SKPaymentQueue defaultQueue] addPayment:payment];
    
    
	// Commented due to touch issue on price button...
//    [[UIApplication sharedApplication] beginIgnoringInteractionEvents];
    
}
- (void)paymentQueue:(SKPaymentQueue *)queue restoreCompletedTransactionsFailedWithError:(NSError *)error
{
    NSLog(@"Error--> %@", error);
    
    [delegate purchaseDidFail:self];
    
}

- (void)paymentQueueRestoreCompletedTransactionsFinished:(SKPaymentQueue *)queue
{
    
    NSMutableArray *purchasedItemIDs = [[NSMutableArray alloc] init];
    
    for (SKPaymentTransaction *transaction in queue.transactions)
    {
        NSString *productID = transaction.payment.productIdentifier;
        [purchasedItemIDs addObject:productID];
        NSLog (@"product id is %@" , productID);
        //[delegate callBeforeCompeleTransaction:transaction];
    
        NSString *strReceipt = [Utility getInAppPurchaseReceipt];
        NSLog(@"Receipt ---- %@", strReceipt);
        
        //[self restoreTransaction:transaction];
        // here put an if/then statement to write files based on previously purchased items
        // example if ([productID isequaltostring: @"youruniqueproductidentifier]){write files} else { nslog sorry}
    }  
    if (purchasedItemIDs.count>0)
    {
    
        [delegate restoreDidFinish:purchasedItemIDs[0]];
        
    }
    else if(purchasedItemIDs.count==0)
    {
        [delegate purchaseDidFail:self];
        
    }
//    else if([purchasedItemIDs count]==1)
//    {
//        NSString *aStrBundleToRestore=[purchasedItemIDs objectAtIndex:0];
//        [delegate restoreDidFinish:aStrBundleToRestore];
//    }
    else
    {
        
    }
}

-(void)requestForStore{
    [[SKPaymentQueue defaultQueue] addTransactionObserver:self];
    [[SKPaymentQueue defaultQueue] restoreCompletedTransactions];
}
// paymentQueueRestoreCompletedTransactionsFinished:

// Paresh //
- (void) completeTransaction: (SKPaymentTransaction *)transaction {
    //                                 	transactionReceipt

//    NSLog(@"transactionReceipt    ------> %@",transaction.transactionReceipt);
//    NSLog(@"transactionDate       ------> %@",transaction.transactionDate);
//    NSLog(@"transactionIdentifier ------> %@",transaction.transactionIdentifier);
    
   
    //
	[self recordTransaction:transaction];
    [self provideContent:transaction.payment.productIdentifier];
    
    [[UIApplication sharedApplication] endIgnoringInteractionEvents];
    
    if(transaction)
        [[SKPaymentQueue defaultQueue] finishTransaction: transaction];
	
    //    
    //    -(NSDictionary*) verifiedReceiptDictionary {
    //        
    //        return [self.receipt objectFromJSONData];
    //    }
    
    /* New Change 07-05-18 Trying to convert ARC...
    NSDictionary *aDictTemp=[transaction.transactionReceipt objectFromJSONData];
    NSLog(@"==> %@",aDictTemp);
    */
    
    NSString *strReceipt = [Utility getInAppPurchaseReceipt];
    [[NSUserDefaults standardUserDefaults] setObject:strReceipt forKey:@"Receipt"];
    [[NSUserDefaults standardUserDefaults] synchronize];
    
    //	[delegate purchaseDidFinish:self.tag withReceipt:strReceipt];
	self.receiptString = strReceipt;
//	NSLog(@"completed delegate - %@  and self = %@",delegate,self);
	//[delegate purchaseDidFinish:self];
	[[SKPaymentQueue defaultQueue] removeTransactionObserver:self];
}


-(void)updateProgress {
	
}


- (void) restoreTransaction: (SKPaymentTransaction *)transaction {
    
    
    [self recordTransaction:transaction.originalTransaction];
    [self provideContent:transaction.originalTransaction.payment.productIdentifier];
    
    
	[[UIApplication sharedApplication] endIgnoringInteractionEvents];
    
	[[SKPaymentQueue defaultQueue] finishTransaction: transaction];
}


- (void) failedTransaction: (SKPaymentTransaction *)transaction {
    
    NSLog(@"transatcion failed :: %@",transaction.error.description);
    
    if (transaction.error.code != SKErrorPaymentCancelled)
    {
        // error!
        
        // [UIAlertView showAlertViewWithTitle:@"ClickAndMove" message:@"Trasaction failed. Please try again."];
    }
    else
    {
        
		
    }
    
    [[UIApplication sharedApplication] endIgnoringInteractionEvents];
    //  [appDelegate showIndicator:NO];
//    [[UIApplication sharedApplication] endIgnoringInteractionEvents];
	NSLog(@"transcation is fail %@",transaction.error);
	
	[[SKPaymentQueue defaultQueue] finishTransaction: transaction];
	//	[delegate purchaseDidFail:self.tag withError:transaction.error];
//	NSLog(@"fail delegate - %@  and self = %@",delegate,self);
	[delegate purchaseDidFail:self];
	[[SKPaymentQueue defaultQueue] removeTransactionObserver:self];
	
}

- (void)paymentQueue:(SKPaymentQueue *)queue updatedTransactions:(NSArray *)transactions {
	
	for (SKPaymentTransaction *transaction in transactions) {
		switch (transaction.transactionState) {
				
			case SKPaymentTransactionStatePurchased:{
                
                NSString *strReceipt = [Utility getInAppPurchaseReceipt];
                NSLog(@" Receipt :: %@",strReceipt);
                
                /* New Change 07-05-18 Trying to convert ARC...
                NSDictionary *aDictTemp=[transaction.transactionReceipt objectFromJSONData];
                
                NSLog(@"==> %@",aDictTemp);
                */
                
                [[UIApplication sharedApplication] endIgnoringInteractionEvents];

                [delegate callBeforeCompeleTransaction:transaction];
                                
                // CR Change ----
                //[self completeTransactionAfterVarification:transaction];
				[self completeTransaction:transaction];
                [appDelegate stopIndicator];
                [[SKPaymentQueue defaultQueue] removeTransactionObserver:self];
                
                [[SKPaymentQueue defaultQueue] finishTransaction:transaction];
                // --------
			}
               
				break;
				
			case SKPaymentTransactionStateFailed:
                
//                [self InsertReceiptIntoiCloud:transaction];
				[self failedTransaction:transaction];
                 [[SKPaymentQueue defaultQueue] finishTransaction:transaction];
				break;
				
			case SKPaymentTransactionStateRestored:
				[self restoreTransaction:transaction];
                 [[SKPaymentQueue defaultQueue] finishTransaction:transaction];
				
			default:
				
				break;
				
		}		
	}
}


#pragma mark -
#pragma mark other mehtof


-(NSString *) getCurrncyForProduct:(SKProduct *)product{
	
	NSLog(@"Get Currency for Product");
	NSNumberFormatter *numberFormatter = [[NSNumberFormatter alloc] init];
	numberFormatter.formatterBehavior = NSNumberFormatterBehavior10_4;
	numberFormatter.numberStyle = NSNumberFormatterCurrencyStyle;
	
	numberFormatter.locale = product.priceLocale;
    NSString *formattedString = [numberFormatter stringFromNumber:product.price];
    
    BOOL isEligible = [[NSUserDefaults standardUserDefaults] boolForKey: UD_KEY_ISINTRODUCT_ELIGIBLE];
    if (isEligible) {
        if (@available(iOS 11.2, *)) {
            formattedString = [numberFormatter stringFromNumber:product.introductoryPrice.price];
        }
    }
    
    if([formattedString isEqualToString:@""] || [formattedString.lowercaseString containsString:@"null"]) {
        formattedString = [numberFormatter stringFromNumber:product.price];
    }
    
	return formattedString;
}


#pragma -
#pragma Purchase helpers

//
// saves a record of the transaction by storing the receipt to disk
//
- (void)recordTransaction:(SKPaymentTransaction *)transaction
{
    
    if ([transaction.payment.productIdentifier isEqualToString:productId])
    {
        // save the transaction receipt to disk
        
        NSString *strReceipt = [Utility getInAppPurchaseReceipt];
        NSLog(@"Receipt :: %@",strReceipt);
        NSLog(@"State :: %ld",(long)transaction.transactionState);
        [[NSUserDefaults standardUserDefaults] setValue:strReceipt forKey:@"proUpgradeTransactionReceipt" ];
        [[NSUserDefaults standardUserDefaults] synchronize];
    }
    
    
}

//
// enable pro features
//
- (void)provideContent:(NSString *)idOfProduct
{
    
    if ([idOfProduct isEqualToString:@""])
    {
        // enable the pro features
        [[NSUserDefaults standardUserDefaults] setBool:YES forKey:@"isProUpgradePurchased" ];
        [[NSUserDefaults standardUserDefaults] synchronize];
    }
    
    
}


- (void)completeTransactionAfterVarification:(SKPaymentTransaction *)atransaction
{
        [self InsertReceiptIntoiCloud:atransaction];
        
        [self completeTransaction:atransaction];
}

- (void)InsertReceiptIntoiCloud:(SKPaymentTransaction *)atransaction
{
    NSString *strReceipt = [Utility getInAppPurchaseReceipt];
    NSDate *dt;
    int days;
    
    if ([[NSUserDefaults standardUserDefaults] integerForKey:@"Plan"]==1)
    {
        dt = [self dateFromDate:[NSDate date] afterNumberOfDays:180];
        days = 180;
    }
    else
    {
        dt = [self dateFromDate:[NSDate date] afterNumberOfDays:365];
        days = 365;
    }
    
        
    NSDateFormatter *dtFrmt = [[NSDateFormatter alloc] init];
    
    dtFrmt.dateFormat = @"dd/MM/yyyy";
        
    // for 6 months
    
    NSString *aStrEndDate = [dtFrmt stringFromDate:dt];
    
    NSString *aStrNameQuery = [NSString stringWithFormat:@"select * from InAppTbl"];
    
    NSMutableArray *aMutArrNames = [[Database sharedDatabase] getAllDataForQuery:aStrNameQuery];
    
    if (aMutArrNames.count > 0)
    {
        NSString *aStrUpdateSql = [NSString stringWithFormat:@"Update InAppTbl set Receipt = \'%@\', Purchase_Date = \'%@\', End_Date = \'%@\', Created_Date = \'%@\', Plan = \'%d\', Kind = \'%@\', where  ID='1'",strReceipt, [dtFrmt stringFromDate:[NSDate date]], aStrEndDate, [dtFrmt stringFromDate:[NSDate date]], days, @"InApp"];
        [[Database sharedDatabase]Update:aStrUpdateSql];
    }
    else
    {
        NSString *aStrInsert = [NSString stringWithFormat:@"insert into  InAppTbl ('Receipt', 'Purchase_Date', 'End_Date', 'Created_Date', 'Plan', 'Kind') values (\'%@\', \'%@\', \'%@\', \'%@\', \'%d\', \'%@\')", strReceipt, [dtFrmt stringFromDate:[NSDate date]], aStrEndDate, [dtFrmt stringFromDate:[NSDate date]], days, @"InApp"];
        
        NSLog(@"Insert String :: %@",aStrInsert);
        
        [[Database sharedDatabase] Insert:aStrInsert];
        
        [[NSUserDefaults standardUserDefaults] setObject:strReceipt forKey:@"Receipt"];
        [[NSUserDefaults standardUserDefaults] synchronize];
        
        [[NSUserDefaults standardUserDefaults] setObject:aStrInsert forKey:@"InsertQuery"];
        [[NSUserDefaults standardUserDefaults] synchronize];
    }
    
    
    aStrNameQuery = [NSString stringWithFormat:@"select * from InAppTbl"];
    
    NSMutableArray *aMutArrNames1 = [[Database sharedDatabase] getAllDataForQuery:aStrNameQuery];
    
    NSLog(@"Data : %@",aMutArrNames1);

    
}

- (NSDate *)dateFromDate:(NSDate *)date afterNumberOfDays:(int)aInt{
    
    NSTimeInterval time = date.timeIntervalSince1970;
    
    // for 6 months

    double dble = 60 * 60 * 24 * aInt;
    
    time = time + dble;
    
    
    NSDate *newDt = [[NSDate alloc] initWithTimeIntervalSince1970:time];
    
    return newDt;
}



@end
