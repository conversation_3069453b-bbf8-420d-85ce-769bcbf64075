//
//  IAPManager.h
//  BIteFXproject
//
//  Created by indianic on 25/03/20.
//

#import <Foundation/Foundation.h>
@import StoreKit;

NS_ASSUME_NONNULL_BEGIN

typedef void(^productsRequestCompletion)(NSMutableArray* products, NSError* _Nullable aError);
typedef void(^productPurchaseCompletion)(BOOL success,SKPaymentTransaction* _Nullable transaction);

@interface IAPManager : NSObject <SKPaymentTransactionObserver>

// Get singlton object of this class...
+ (IAPManager *)sharedInstance;

/// Keeps track of all purchases.
@property (strong) NSMutableArray *productsPurchased;

/// Starts the product request with the specified identifiers.
-(void)requestProducts:(NSArray *)identifiers completion:(productsRequestCompletion)completionHandler;

@property (NS_NONATOMIC_IOSONLY, readonly) BOOL canMakePayments;

/// Implements the purchase of a product.
-(void)buy:(SKProduct *)product completion:(productPurchaseCompletion)completionHandler;

/// Implements the restoration of previously completed purchases.
-(void)restorePurchase:(productPurchaseCompletion)completionHandler;

- (NSString *)getLocalPriceForProduct:(SKProduct *)product;

@end

NS_ASSUME_NONNULL_END
