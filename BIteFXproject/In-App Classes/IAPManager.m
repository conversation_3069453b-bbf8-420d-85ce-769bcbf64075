//
//  IAPManager.m
//  BIteFXproject
//
//  Created by indianic on 25/03/20.
//

#import "IAPManager.h"

@interface IAPManager() <SKRequestDelegate, SKProductsRequestDelegate>

/// Keeps track of all valid products. These products are available for sale in the App Store.
@property (strong) NSMutableArray *availableProducts;

/// Keeps a strong reference to the product request.
@property (strong) SKProductsRequest *productRequest;

@property (nonatomic, strong) productsRequestCompletion productsRequestCompletionHandler;
@property (nonatomic, strong) productPurchaseCompletion productPurchaseCompletionHandler;

@end

@implementation IAPManager

// Get singlton object of this class...
+ (IAPManager *)sharedInstance {
    
    static dispatch_once_t onceToken;
    static IAPManager *sharedInstance;
    dispatch_once(&onceToken, ^{
        
        sharedInstance = [[self alloc] init];
        
    });
    
    return sharedInstance;
    
}

- (instancetype)init {
    self = [super init];

    if (self) {
        _availableProducts = [[NSMutableArray alloc] initWithCapacity:0];
        _productsPurchased = [[NSMutableArray alloc] initWithCapacity:0];
    }
    
    return self;
}
/**
     Indicates whether the user is allowed to make payments.
     - returns: true if the user is allowed to make payments and false, otherwise. Tell StoreManager to query the App Store when the user is allowed to
                make payments and there are product identifiers to be queried.
 */
-(BOOL)canMakePayments {
    return [SKPaymentQueue canMakePayments];
}

#pragma mark - Request Information

/// Starts the product request with the specified identifiers.
-(void)requestProducts:(NSArray *)identifiers completion:(productsRequestCompletion)completionHandler {
    
    // Create a set for the product identifiers.
    self.productsRequestCompletionHandler = completionHandler;
    
    NSSet *productIdentifiers = [NSSet setWithArray:identifiers];

    // Initialize the product request with the above identifiers.
    self.productRequest = [[SKProductsRequest alloc] initWithProductIdentifiers:productIdentifiers];
    self.productRequest.delegate = self;

    // Send the request to the App Store.
    [self.productRequest start];

}

#pragma mark - SKProductsRequestDelegate

/// Used to get the App Store's response to your request and notify your observer.
- (void)productsRequest:(SKProductsRequest *)request didReceiveResponse:(SKProductsResponse *)response {
    
    if ((response.products).count > 0) {
        self.availableProducts = [NSMutableArray arrayWithArray:response.products];
    }
    
    if (self.productsRequestCompletionHandler != nil) {
        self.productsRequestCompletionHandler(self.availableProducts, nil);
    }
    
    [self clearRequestAndHandler];
    
}

#pragma mark - SKRequestDelegate

/// Called when the product request failed.
- (void)request:(SKRequest *)request didFailWithError:(NSError *)error {
    
    if (self.productsRequestCompletionHandler != nil) {
        self.productsRequestCompletionHandler(self.availableProducts, error);
    }
    
    [self clearRequestAndHandler];
}

- (void)clearRequestAndHandler {
    self.productRequest = nil;
    self.productsRequestCompletionHandler = nil;
}

#pragma mark - Submit Payment Request

/// Create and add a payment request to the payment queue.
-(void)buy:(SKProduct *)product completion:(productPurchaseCompletion)completionHandler {
    
    self.productPurchaseCompletionHandler = completionHandler;
    SKMutablePayment *payment = [SKMutablePayment paymentWithProduct:product];
    [[SKPaymentQueue defaultQueue] addPayment:payment];
}

#pragma mark - Restore All Restorable Purchases

/// Restores all previously completed purchases.
-(void)restorePurchase:(productPurchaseCompletion)completionHandler {
    
    self.productPurchaseCompletionHandler = completionHandler;
    [[SKPaymentQueue defaultQueue] restoreCompletedTransactions];
}

#pragma mark - SKPaymentTransactionObserver Methods

/// Called when there are transactions in the payment queue.
- (void)paymentQueue:(SKPaymentQueue *)queue updatedTransactions:(NSArray *)transactions {
    
    for(SKPaymentTransaction *transaction in transactions) {
        switch (transaction.transactionState) {
            case SKPaymentTransactionStatePurchasing: break;
            // Do not block your UI. Allow the user to continue using your app.
            case SKPaymentTransactionStateDeferred:
                break;
            // The purchase was successful.
            case SKPaymentTransactionStatePurchased: [self handlePurchasedTransaction:transaction];
                break;
            // The transaction failed.
            case SKPaymentTransactionStateFailed: [self handleFailedTransaction:transaction];
                break;
            // There are restored products.
            case SKPaymentTransactionStateRestored: [self handleRestoredTransaction:transaction];
                break;
            default: break;
        }
    }
}

/// Logs all transactions that have been removed from the payment queue.
//- (void)paymentQueue:(SKPaymentQueue *)queue removedTransactions:(NSArray *)transactions {
//    for(SKPaymentTransaction *transaction in transactions) {
//        NSLog(@"%@", transaction.payment.productIdentifier);
//    }
//}
//
///// Called when an error occur while restoring purchases. Notify the user about the error.
//- (void)paymentQueue:(SKPaymentQueue *)queue restoreCompletedTransactionsFailedWithError:(NSError *)error {
//    if (error.code != SKErrorPaymentCancelled) {
//
//
//    }
//}
//
///// Called when all restorable transactions have been processed by the payment queue.
//- (void)paymentQueueRestoreCompletedTransactionsFinished:(SKPaymentQueue *)queue {
//
//}

#pragma mark - Handle Payment Transactions

/// Handles successful purchase transactions.
-(void)handlePurchasedTransaction:(SKPaymentTransaction *)transaction {
    [self.productsPurchased addObject:transaction];
    if (self.productPurchaseCompletionHandler != nil) {
        self.productPurchaseCompletionHandler(true, transaction);
    }
    
    NSString *aStrReceipt = [Utility getInAppPurchaseReceipt];
    NSLog(@"Receipt :: %@",aStrReceipt);
    
    [[NSUserDefaults standardUserDefaults] setObject:aStrReceipt forKey:@"Receipt"];
    [[NSUserDefaults standardUserDefaults] synchronize];
    
    // Finish the successful transaction.
    [[SKPaymentQueue defaultQueue] finishTransaction:transaction];
    [self clearHandler];
}

/// Handles failed purchase transactions.
-(void)handleFailedTransaction:(SKPaymentTransaction*)transaction {

    if (transaction.error.code != SKErrorPaymentCancelled) {
        
    }
    
    if (self.productPurchaseCompletionHandler != nil) {
        self.productPurchaseCompletionHandler(false, transaction);
    }

    // Finish the failed transaction.
    [[SKPaymentQueue defaultQueue] finishTransaction:transaction];
    [self clearHandler];
    
//    // Do not send any notifications when the user cancels the purchase.
//    if (transaction.error.code != SKErrorPaymentCancelled) {
//        dispatch_async(dispatch_get_main_queue(), ^{
////            [[NSNotificationCenter defaultCenter] postNotificationName:PCSPurchaseNotification object:self];
//        });
//    }

   
    
}

/// Handles restored purchase transactions.
-(void)handleRestoredTransaction:(SKPaymentTransaction*)transaction {
    
    if (self.productPurchaseCompletionHandler != nil) {
        self.productPurchaseCompletionHandler(true, transaction);
    }
    
    // Finishes the restored transaction.
    [[SKPaymentQueue defaultQueue] finishTransaction:transaction];
    [self clearHandler];
}

- (void)clearHandler {
    self.productPurchaseCompletionHandler = nil;
}

#pragma mark --------------------------------
#pragma mark Other Methods

- (NSString *)getLocalPriceForProduct:(SKProduct *)product {
    
    NSLog(@"Get Currency for Product");
    NSNumberFormatter *numberFormatter = [[NSNumberFormatter alloc] init];
    numberFormatter.formatterBehavior = NSNumberFormatterBehavior10_4;
    numberFormatter.numberStyle = NSNumberFormatterCurrencyStyle;
    
    numberFormatter.locale = product.priceLocale;
    NSString *formattedString = [numberFormatter stringFromNumber:product.price];
    
    BOOL isEligible = [[NSUserDefaults standardUserDefaults] boolForKey: UD_KEY_ISINTRODUCT_ELIGIBLE];
    if (isEligible) {
        if (@available(iOS 11.2, *)) {
            formattedString = [numberFormatter stringFromNumber:product.introductoryPrice.price];
        }
    }
    
    if(formattedString == nil || [formattedString isEqualToString:@""] || [formattedString.lowercaseString containsString:@"null"]) {
        formattedString = [numberFormatter stringFromNumber:product.price];
    }
    
    return formattedString;
}

/*
#pragma mark --------------------------------
#pragma mark InApp Purchase Receipt Methods

+ (NSString *)getInAppPurchaseReceipt {
    
    NSString *aStrReceipt = @"";
    NSURL *aUrlReceipt = [[NSBundle mainBundle] appStoreReceiptURL];
    if ([[NSFileManager defaultManager] fileExistsAtPath:[aUrlReceipt path]])
    {
        NSData *aDataReceipt = [NSData dataWithContentsOfURL:aUrlReceipt];
        aStrReceipt = [aDataReceipt base64EncodedStringWithOptions:0];
    }
    
    return aStrReceipt;
}
*/

@end


