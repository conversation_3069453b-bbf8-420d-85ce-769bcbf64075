<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="4514" systemVersion="13B42" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none">
    <dependencies>
        <deployment defaultVersion="1072" identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="3747"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="blue" indentationWidth="10" rowHeight="92" id="wM0-79-NZx" customClass="skipCell">
            <rect key="frame" x="0.0" y="0.0" width="748" height="55"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="wM0-79-NZx" id="gkA-kr-5I3">
                <rect key="frame" x="0.0" y="0.0" width="748" height="54"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" text="" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumFontSize="10" id="Ry1-Uv-CmQ">
                        <rect key="frame" x="13" y="12" width="52" height="28"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <color key="textColor" cocoaTouchSystemColor="darkTextColor"/>
                        <color key="highlightedColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                    </label>
                    <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" text="" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumFontSize="10" id="js7-41-Tdd">
                        <rect key="frame" x="99" y="9" width="107" height="36"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <color key="textColor" cocoaTouchSystemColor="darkTextColor"/>
                        <color key="highlightedColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                    </label>
                    <imageView userInteractionEnabled="NO" contentMode="scaleToFill" image="Line2.png" id="L4P-0k-eXa">
                        <rect key="frame" x="78" y="0.0" width="2" height="55"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    </imageView>
                    <imageView userInteractionEnabled="NO" contentMode="scaleToFill" image="Line2.png" id="LVH-6y-o6R">
                        <rect key="frame" x="631" y="0.0" width="2" height="55"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    </imageView>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" text="" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="3" minimumFontSize="10" id="zLP-64-95n">
                        <rect key="frame" x="101" y="9" width="508" height="36"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <color key="textColor" cocoaTouchSystemColor="darkTextColor"/>
                        <color key="highlightedColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                    </label>
                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" id="662-LI-4he">
                        <rect key="frame" x="639" y="9" width="103" height="36"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                        <fontDescription key="fontDescription" type="system" pointSize="12"/>
                        <state key="normal" image="btnDownloadNrml.png">
                            <color key="titleShadowColor" white="0.5" alpha="1" colorSpace="calibratedWhite"/>
                        </state>
                        <state key="selected" image="btnDownloadslt.png"/>
                    </button>
                </subviews>
            </tableViewCellContentView>
            <connections>
                <outlet property="btnDownload" destination="662-LI-4he" id="tsU-Kh-r3z"/>
                <outlet property="lblTxtdescription" destination="zLP-64-95n" id="H8T-Vy-QsQ"/>
                <outlet property="lblVersion" destination="Ry1-Uv-CmQ" id="Vl4-J4-nWv"/>
            </connections>
        </tableViewCell>
    </objects>
    <resources>
        <image name="Line2.png" width="2" height="92"/>
        <image name="btnDownloadNrml.png" width="103" height="36"/>
        <image name="btnDownloadslt.png" width="103" height="36"/>
    </resources>
</document>