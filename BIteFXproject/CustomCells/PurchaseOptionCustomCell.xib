<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.iPad.XIB" version="3.0" toolsVersion="22155" targetRuntime="iOS.CocoaTouch.iPad" propertyAccessControl="none" useAutolayout="YES" colorMatched="YES">
    <device id="ipad9_7" orientation="portrait" layout="fullscreen" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22131"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="blue" indentationWidth="10" rowHeight="269" id="3" customClass="PurchaseOptionCustomCell">
            <rect key="frame" x="0.0" y="0.0" width="748" height="252"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="3" id="q4S-I3-z23">
                <rect key="frame" x="0.0" y="0.0" width="748" height="252"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="bhN-ZJ-e7A">
                        <rect key="frame" x="0.0" y="0.0" width="748" height="252"/>
                        <subviews>
                            <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" text="BiteFX on iPad Subscription for 6 months" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumFontSize="10" translatesAutoresizingMaskIntoConstraints="NO" id="4">
                                <rect key="frame" x="10" y="8" width="336.5" height="19"/>
                                <fontDescription key="fontDescription" name="Arial-BoldMT" family="Arial" pointSize="17"/>
                                <color key="textColor" systemColor="darkTextColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="fx8-Fn-nNZ">
                                <rect key="frame" x="10" y="37" width="670" height="57"/>
                                <subviews>
                                    <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="giG-A1-1wa">
                                        <rect key="frame" x="0.0" y="0.0" width="670" height="57"/>
                                        <subviews>
                                            <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" text="Special Offer" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" minimumFontSize="10" translatesAutoresizingMaskIntoConstraints="NO" id="OqG-W8-Krs">
                                                <rect key="frame" x="0.0" y="0.0" width="670" height="52"/>
                                                <fontDescription key="fontDescription" name="Arial-BoldMT" family="Arial" pointSize="15"/>
                                                <color key="textColor" red="0.33333334329999997" green="0.33333334329999997" blue="0.33333334329999997" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                <color key="highlightedColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstItem="OqG-W8-Krs" firstAttribute="top" secondItem="giG-A1-1wa" secondAttribute="top" id="D3M-cq-ndj"/>
                                            <constraint firstAttribute="bottom" secondItem="OqG-W8-Krs" secondAttribute="bottom" constant="5" id="jOD-M1-J5t"/>
                                            <constraint firstAttribute="trailing" secondItem="OqG-W8-Krs" secondAttribute="trailing" id="vuu-nf-Bau"/>
                                            <constraint firstItem="OqG-W8-Krs" firstAttribute="leading" secondItem="giG-A1-1wa" secondAttribute="leading" id="wDU-wu-nH1"/>
                                        </constraints>
                                    </view>
                                </subviews>
                            </stackView>
                            <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" text="Provides:" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumFontSize="10" translatesAutoresizingMaskIntoConstraints="NO" id="6">
                                <rect key="frame" x="10" y="94" width="77.5" height="19"/>
                                <fontDescription key="fontDescription" name="Arial-BoldMT" family="Arial" pointSize="17"/>
                                <color key="textColor" red="0.3333333432674408" green="0.3333333432674408" blue="0.3333333432674408" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <color key="highlightedColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            </label>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="1EM-G4-bP3" userLabel="Purchase View">
                                <rect key="frame" x="460" y="8" width="270" height="37"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="7">
                                        <rect key="frame" x="0.0" y="0.0" width="270" height="37"/>
                                        <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="270" id="0LU-mz-s2T"/>
                                            <constraint firstAttribute="height" constant="37" id="qWE-Ls-EhF"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" name="Arial-BoldMT" family="Arial" pointSize="17"/>
                                        <state key="normal" backgroundImage="PurchasePrice-normal.png">
                                            <color key="titleColor" red="0.19607843459999999" green="0.30980393290000002" blue="0.52156865600000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                            <color key="titleShadowColor" red="0.5" green="0.5" blue="0.5" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        </state>
                                        <state key="highlighted" backgroundImage="PurchasePrice-pressed.png">
                                            <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        </state>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                <integer key="value" value="5"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="onClickPurchase:" destination="3" eventType="touchUpInside" id="12"/>
                                        </connections>
                                    </button>
                                    <activityIndicatorView opaque="NO" contentMode="scaleToFill" style="white" translatesAutoresizingMaskIntoConstraints="NO" id="16">
                                        <rect key="frame" x="125" y="8.5" width="20" height="20"/>
                                        <constraints>
                                            <constraint firstAttribute="width" secondItem="16" secondAttribute="height" multiplier="1:1" id="8yq-PI-21y"/>
                                            <constraint firstAttribute="width" constant="20" id="bxp-2E-Omf"/>
                                        </constraints>
                                    </activityIndicatorView>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="7" firstAttribute="leading" secondItem="1EM-G4-bP3" secondAttribute="leading" id="Fo2-KN-ZRA" userLabel="Btn Purchase.leading = leading"/>
                                    <constraint firstAttribute="bottom" secondItem="7" secondAttribute="bottom" id="O0q-8F-eMP" userLabel="bottom = Btn Purchase.bottom"/>
                                    <constraint firstItem="16" firstAttribute="centerX" secondItem="1EM-G4-bP3" secondAttribute="centerX" id="Qv4-0r-S8H"/>
                                    <constraint firstAttribute="trailing" secondItem="7" secondAttribute="trailing" id="WK3-F5-eik" userLabel="trailing = Btn Purchase.trailing"/>
                                    <constraint firstItem="7" firstAttribute="top" secondItem="1EM-G4-bP3" secondAttribute="top" id="Yaf-hC-8tZ" userLabel="Btn Purchase.top = top"/>
                                    <constraint firstItem="16" firstAttribute="centerY" secondItem="1EM-G4-bP3" secondAttribute="centerY" id="mmv-VK-cbO"/>
                                </constraints>
                            </view>
                            <textView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" showsHorizontalScrollIndicator="NO" editable="NO" text="- Use of full set of 100+ occlussion - focused animation." translatesAutoresizingMaskIntoConstraints="NO" id="5">
                                <rect key="frame" x="16" y="115" width="662" height="90"/>
                                <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <color key="textColor" red="0.3333333432674408" green="0.3333333432674408" blue="0.3333333432674408" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <fontDescription key="fontDescription" name="Arial-BoldMT" family="Arial" pointSize="14"/>
                                <textInputTraits key="textInputTraits" autocapitalizationType="sentences"/>
                            </textView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="qSQ-JS-pXy">
                                <rect key="frame" x="10" y="212" width="600" height="35"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="13">
                                        <rect key="frame" x="0.0" y="0.0" width="79" height="35"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="79" id="Xvk-aC-5Er"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" name="Arial-BoldMT" family="Arial" pointSize="17"/>
                                        <state key="normal" image="more.png">
                                            <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                            <color key="titleShadowColor" red="0.5" green="0.5" blue="0.5" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        </state>
                                        <state key="highlighted" backgroundImage="PurchasePrice-pressed.png">
                                            <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        </state>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                <integer key="value" value="5"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="onClickMore:" destination="3" eventType="touchUpInside" id="15"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" tag="1" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="zmV-wQ-d3X">
                                        <rect key="frame" x="110" y="0.0" width="180" height="35"/>
                                        <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="180" id="uAi-fQ-oK5"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" name="Arial-BoldMT" family="Arial" pointSize="16"/>
                                        <state key="normal" title="Terms &amp; Conditions">
                                            <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <color key="titleShadowColor" red="0.5" green="0.5" blue="0.5" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        </state>
                                        <state key="highlighted" backgroundImage="PurchasePrice-pressed.png">
                                            <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        </state>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                <integer key="value" value="5"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="onClickTerms:" destination="3" eventType="touchUpInside" id="nD9-So-Pka"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" tag="2" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="QjG-dT-TDl">
                                        <rect key="frame" x="325" y="0.0" width="140" height="35"/>
                                        <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="140" id="9ct-TD-oPA"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" name="Arial-BoldMT" family="Arial" pointSize="16"/>
                                        <state key="normal" title="Privacy Policy">
                                            <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <color key="titleShadowColor" red="0.5" green="0.5" blue="0.5" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        </state>
                                        <state key="highlighted" backgroundImage="PurchasePrice-pressed.png">
                                            <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        </state>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                <integer key="value" value="5"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="onClickTerms:" destination="3" eventType="touchUpInside" id="jTW-SG-4SN"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" tag="3" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="LK3-Li-0M6">
                                        <rect key="frame" x="500" y="0.0" width="100" height="35"/>
                                        <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="100" id="5Ld-bq-D4J"/>
                                        </constraints>
                                        <state key="normal" title="Support"/>
                                        <state key="highlighted" backgroundImage="PurchasePrice-pressed.png"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                <integer key="value" value="5"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="onClickTerms:" destination="3" eventType="touchUpInside" id="cl9-BI-L0S"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="LK3-Li-0M6" firstAttribute="leading" secondItem="QjG-dT-TDl" secondAttribute="trailing" constant="35" id="1g9-IF-3Xe"/>
                                    <constraint firstAttribute="bottom" secondItem="LK3-Li-0M6" secondAttribute="bottom" id="28f-Qk-517"/>
                                    <constraint firstItem="13" firstAttribute="top" secondItem="qSQ-JS-pXy" secondAttribute="top" id="5ca-LS-5WY"/>
                                    <constraint firstAttribute="trailing" secondItem="LK3-Li-0M6" secondAttribute="trailing" id="6M4-eU-ObM"/>
                                    <constraint firstAttribute="bottom" secondItem="QjG-dT-TDl" secondAttribute="bottom" id="6d0-zv-WAV"/>
                                    <constraint firstItem="13" firstAttribute="leading" secondItem="qSQ-JS-pXy" secondAttribute="leading" id="8tf-tI-KS5"/>
                                    <constraint firstItem="zmV-wQ-d3X" firstAttribute="leading" secondItem="13" secondAttribute="trailing" constant="31" id="9Hp-4W-U2N"/>
                                    <constraint firstItem="zmV-wQ-d3X" firstAttribute="top" secondItem="qSQ-JS-pXy" secondAttribute="top" id="CA6-ec-dLv"/>
                                    <constraint firstAttribute="bottom" secondItem="zmV-wQ-d3X" secondAttribute="bottom" id="JoH-Qo-WBR"/>
                                    <constraint firstAttribute="bottom" secondItem="13" secondAttribute="bottom" id="KkR-WG-FvF"/>
                                    <constraint firstItem="LK3-Li-0M6" firstAttribute="top" secondItem="qSQ-JS-pXy" secondAttribute="top" id="MtG-7f-to2"/>
                                    <constraint firstItem="QjG-dT-TDl" firstAttribute="leading" secondItem="zmV-wQ-d3X" secondAttribute="trailing" constant="35" id="Xhv-GD-AwC"/>
                                    <constraint firstAttribute="height" constant="35" id="aRx-Vt-XPd"/>
                                    <constraint firstItem="QjG-dT-TDl" firstAttribute="top" secondItem="qSQ-JS-pXy" secondAttribute="top" id="p7O-T1-028"/>
                                </constraints>
                            </view>
                        </subviews>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="6" firstAttribute="top" secondItem="fx8-Fn-nNZ" secondAttribute="bottom" id="020-3q-Duj"/>
                            <constraint firstAttribute="trailing" secondItem="5" secondAttribute="trailing" constant="70" id="201-gU-no8"/>
                            <constraint firstItem="qSQ-JS-pXy" firstAttribute="top" secondItem="5" secondAttribute="bottom" constant="7" id="9g6-ou-448"/>
                            <constraint firstItem="4" firstAttribute="leading" secondItem="bhN-ZJ-e7A" secondAttribute="leading" constant="10" id="BR6-AU-3Tf"/>
                            <constraint firstItem="6" firstAttribute="leading" secondItem="bhN-ZJ-e7A" secondAttribute="leading" constant="10" id="CqG-SD-1Km"/>
                            <constraint firstItem="1EM-G4-bP3" firstAttribute="leading" secondItem="fx8-Fn-nNZ" secondAttribute="trailing" constant="-220" id="OXg-xv-bbR"/>
                            <constraint firstItem="qSQ-JS-pXy" firstAttribute="leading" secondItem="bhN-ZJ-e7A" secondAttribute="leading" constant="10" id="RPL-XK-gTa"/>
                            <constraint firstAttribute="bottom" secondItem="qSQ-JS-pXy" secondAttribute="bottom" constant="5" id="RbT-1Z-arQ"/>
                            <constraint firstItem="5" firstAttribute="top" secondItem="6" secondAttribute="bottom" constant="2" id="Ve8-EI-vct"/>
                            <constraint firstItem="4" firstAttribute="top" secondItem="bhN-ZJ-e7A" secondAttribute="top" constant="8" id="Y6T-CC-WrR"/>
                            <constraint firstAttribute="trailing" secondItem="1EM-G4-bP3" secondAttribute="trailing" constant="18" id="aTt-3U-Vgd"/>
                            <constraint firstItem="5" firstAttribute="leading" secondItem="bhN-ZJ-e7A" secondAttribute="leading" constant="16" id="fHc-ZO-fY4"/>
                            <constraint firstItem="fx8-Fn-nNZ" firstAttribute="top" secondItem="4" secondAttribute="bottom" constant="10" id="hdz-Lb-DK8"/>
                            <constraint firstItem="fx8-Fn-nNZ" firstAttribute="leading" secondItem="bhN-ZJ-e7A" secondAttribute="leading" constant="10" id="ndf-L9-YcA"/>
                            <constraint firstItem="1EM-G4-bP3" firstAttribute="top" secondItem="bhN-ZJ-e7A" secondAttribute="top" constant="8" id="vJo-Mu-rec"/>
                        </constraints>
                    </view>
                </subviews>
                <constraints>
                    <constraint firstItem="bhN-ZJ-e7A" firstAttribute="leading" secondItem="q4S-I3-z23" secondAttribute="leading" id="CVe-tf-Kxm"/>
                    <constraint firstItem="bhN-ZJ-e7A" firstAttribute="top" secondItem="q4S-I3-z23" secondAttribute="top" id="KtG-dK-N6v"/>
                    <constraint firstAttribute="trailing" secondItem="bhN-ZJ-e7A" secondAttribute="trailing" id="lh1-eX-OTn"/>
                    <constraint firstAttribute="bottom" secondItem="bhN-ZJ-e7A" secondAttribute="bottom" id="oXd-GM-Ptw"/>
                </constraints>
            </tableViewCellContentView>
            <color key="backgroundColor" systemColor="groupTableViewBackgroundColor"/>
            <connections>
                <outlet property="actViewpurchase" destination="16" id="17"/>
                <outlet property="btnPurchase" destination="7" id="8"/>
                <outlet property="lblIntroductoryOffer" destination="OqG-W8-Krs" id="YtY-4V-SMX"/>
                <outlet property="lblPurchaseTitle" destination="4" id="9"/>
                <outlet property="txtViewPurchaseView" destination="5" id="10"/>
                <outlet property="viewIntroductoryOffer" destination="giG-A1-1wa" id="d2f-lj-GI2"/>
            </connections>
            <point key="canvasLocation" x="139" y="141"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="PurchasePrice-normal.png" width="147.27272033691406" height="19.636363983154297"/>
        <image name="PurchasePrice-pressed.png" width="147.27272033691406" height="19.636363983154297"/>
        <image name="more.png" width="79" height="30"/>
        <systemColor name="darkTextColor">
            <color white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
        <systemColor name="groupTableViewBackgroundColor">
            <color red="0.94901960784313721" green="0.94901960784313721" blue="0.96862745098039216" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
    </resources>
</document>
