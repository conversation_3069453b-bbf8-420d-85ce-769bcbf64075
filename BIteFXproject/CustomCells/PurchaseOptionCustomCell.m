//
//  PurchaseOptionCustomCell.m
//  BIteFXproject
//
//  Created by indianic on 02/10/12.
//  Copyright (c) 2012 __MyCompanyName__. All rights reserved.
//

#import "PurchaseOptionCustomCell.h"

@implementation PurchaseOptionCustomCell
@synthesize delegate;
@synthesize btnPurchase;
@synthesize lblPurchaseTitle;
@synthesize txtViewPurchaseView;
@synthesize actViewpurchase;
@synthesize viewIntroductoryOffer;
@synthesize lblIntroductoryOffer;

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        // Initialization code
    }
    return self;
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated
{
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}


#pragma mark - User Define Methods

- (IBAction)onClickPurchase:(id)sender {
    
    if(![appDelegate isNetWorkAvailable]) {
        
        [UIAlertController showAlertInViewController:AppDelegateobj.window.rootViewController withTitle:@"BiteFX" message:@"No internet connection found. Please try again!" cancelButtonTitle:@"OK" destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
        return;
    }
    
    UIButton *btnSender = (UIButton *)sender;
        
    [delegate clickedPurchase:btnSender.tag];
}

- (IBAction)onClickMore:(id)sender {
    
    [delegate moreClicked];
}

- (IBAction)onClickTerms:(id)sender {
    
    UIButton *btnSender = (UIButton *)sender;
    [delegate termsClicked:btnSender.tag];
}

@end
