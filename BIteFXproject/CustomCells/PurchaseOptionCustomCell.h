//
//  PurchaseOptionCustomCell.h
//  BIteFXproject
//
//  Created by indianic on 02/10/12.
//  Copyright (c) 2012 __MyCompanyName__. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "UITableViewCell+NIB.h"

@protocol PurchaseOptionCustomCellDelegate;

@interface PurchaseOptionCustomCell : UITableViewCell
{
    IBOutlet UIButton *btnPurchase;
    
    IBOutlet UILabel *lblPurchaseTitle;
    IBOutlet UITextView *txtViewPurchaseView;
    
    IBOutlet UIActivityIndicatorView *actViewpurchase;
    
    id <PurchaseOptionCustomCellDelegate> delegate;
    
    IBOutlet UIButton *btnTermsCondtions;
    IBOutlet UIButton *btnPrivacy;
    
    // Version 3.0 Changes, Introductory offer is added with auto renwable subscription...
    IBOutlet UIView *viewIntroductoryOffer;
    IBOutlet UILabel *lblIntroductoryOffer;
}

@property (nonatomic,strong) id <PurchaseOptionCustomCellDelegate> delegate;
@property (nonatomic, strong)  IBOutlet UIButton *btnPurchase;
@property (nonatomic, strong)  IBOutlet UILabel *lblPurchaseTitle;
@property (nonatomic, strong)  IBOutlet UITextView *txtViewPurchaseView;
@property (nonatomic, strong)  IBOutlet UIActivityIndicatorView *actViewpurchase;
@property (nonatomic, strong)  IBOutlet UIView *viewIntroductoryOffer;
@property (nonatomic, strong)  IBOutlet UILabel *lblIntroductoryOffer;

- (IBAction)onClickPurchase:(id)sender;

- (IBAction)onClickMore:(id)sender;

- (IBAction)onClickTerms:(id)sender;

@end

@protocol PurchaseOptionCustomCellDelegate

- (void)clickedPurchase:(NSInteger)aIntTag;

- (void)moreClicked;

- (void)termsClicked:(NSInteger)aIntTag;

@end
