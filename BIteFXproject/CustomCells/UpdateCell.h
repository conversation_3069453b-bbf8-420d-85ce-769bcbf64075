//
//  UpdateCell.h
//  BIteFXproject
//
//  Created by <PERSON><PERSON> zala on 03/05/12.
//  Copyright (c) 2012 __MyCompanyName__. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "UITableViewCell+NIB.h"
#import "BiteFXMainScreenVC.h"
@interface UpdateCell : UITableViewCell
{
    
    IBOutlet UILabel *lblVersion;
    IBOutlet UILabel *lblDate;
    IBOutlet UITextView *txtVdescription;
    IBOutlet UIImageView *imgLine1;
    IBOutlet UIImageView *imgLine2;
    IBOutlet UILabel *lblTxtdescription;
    
    IBOutlet UIButton *btnDownload;
    
    
}
@property (nonatomic,strong)IBOutlet UIButton *btnDownload;

@property (nonatomic,strong)IBOutlet UILabel *lblTxtdescription;
@property (nonatomic,strong)IBOutlet UILabel *lblVersion;
@property (nonatomic,strong)IBOutlet UILabel *lblDate;
@property (nonatomic,strong)IBOutlet UITextView *txtVdescription;
@property (nonatomic,strong)IBOutlet UIImageView *imgLine1;
@property (nonatomic,strong)IBOutlet UIImageView *imgLine2;
@end
