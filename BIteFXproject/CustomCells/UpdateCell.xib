<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="9531" systemVersion="14F27" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none">
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="9529"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="UpdateCell"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="blue" indentationWidth="10" rowHeight="92" id="3" customClass="UpdateCell">
            <rect key="frame" x="0.0" y="0.0" width="748" height="55"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="3" id="EYK-mk-app">
                <rect key="frame" x="0.0" y="0.0" width="748" height="54"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" text="" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumFontSize="10" id="4">
                        <rect key="frame" x="13" y="12" width="52" height="28"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <color key="textColor" cocoaTouchSystemColor="darkTextColor"/>
                        <color key="highlightedColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                    </label>
                    <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" text="" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumFontSize="10" id="5">
                        <rect key="frame" x="99" y="9" width="107" height="36"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <color key="textColor" cocoaTouchSystemColor="darkTextColor"/>
                        <color key="highlightedColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                    </label>
                    <imageView userInteractionEnabled="NO" contentMode="scaleToFill" image="Line2.png" id="11">
                        <rect key="frame" x="78" y="0.0" width="2" height="55"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    </imageView>
                    <imageView userInteractionEnabled="NO" contentMode="scaleToFill" image="Line2.png" id="12">
                        <rect key="frame" x="229" y="0.0" width="2" height="55"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    </imageView>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" text="" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="3" minimumFontSize="10" id="17">
                        <rect key="frame" x="234" y="9" width="508" height="36"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <color key="textColor" cocoaTouchSystemColor="darkTextColor"/>
                        <color key="highlightedColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                    </label>
                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" id="WVr-cQ-9AT">
                        <rect key="frame" x="685" y="12" width="59" height="30"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                        <fontDescription key="fontDescription" type="system" pointSize="12"/>
                        <state key="normal" title="Download">
                            <color key="titleShadowColor" white="0.5" alpha="1" colorSpace="calibratedWhite"/>
                        </state>
                    </button>
                </subviews>
            </tableViewCellContentView>
            <connections>
                <outlet property="btnDownload" destination="WVr-cQ-9AT" id="lM5-IF-c0P"/>
                <outlet property="imgLine1" destination="11" id="13"/>
                <outlet property="imgLine2" destination="12" id="14"/>
                <outlet property="lblDate" destination="5" id="8"/>
                <outlet property="lblTxtdescription" destination="17" id="18"/>
                <outlet property="lblVersion" destination="4" id="7"/>
            </connections>
            <point key="canvasLocation" x="532" y="367.5"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="Line2.png" width="2" height="92"/>
    </resources>
</document>
