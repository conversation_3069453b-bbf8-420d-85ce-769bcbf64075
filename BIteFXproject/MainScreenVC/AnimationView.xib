<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.iPad.XIB" version="3.0" toolsVersion="24093.7" targetRuntime="iOS.CocoaTouch.iPad" propertyAccessControl="none" useAutolayout="YES" colorMatched="YES">
    <device id="ipad9_7" orientation="portrait" layout="fullscreen" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="24053.1"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="AnimationView">
            <connections>
                <outlet property="btnCopySequence" destination="f7p-MC-WbR" id="8kb-u0-SUL"/>
                <outlet property="btnCopyUDpresentation" destination="oyJ-Fc-f7x" id="kFr-AV-gNk"/>
                <outlet property="btnDelete" destination="eNO-kY-U53" id="JmQ-ZL-268"/>
                <outlet property="btnFav" destination="z1T-JJ-u96" id="6Pa-af-7DJ"/>
                <outlet property="btnFavLarge" destination="2LP-yD-VTR" id="hJg-Gk-291"/>
                <outlet property="btnFavMedium" destination="HRr-Z2-Mb1" id="Btd-3h-gPv"/>
                <outlet property="btnFavSequence" destination="aYa-wN-nNv" id="tbR-jI-cIo"/>
                <outlet property="btnInfoSequence" destination="Apt-si-PaL" id="pOr-bO-CaC"/>
                <outlet property="btnLeftCorner" destination="oS2-x0-h8p" id="ARL-Kp-iWz"/>
                <outlet property="btnVid" destination="5" id="13"/>
                <outlet property="imgLeftCorner" destination="upV-Mv-cjT" id="j1a-oV-dK2"/>
                <outlet property="lblName" destination="12" id="14"/>
                <outlet property="lblTitle" destination="gAH-GB-eAN" id="aiL-8g-1P3"/>
                <outlet property="view" destination="4" id="6"/>
                <outlet property="viewLeftCorner" destination="GeT-Kc-Zmb" id="MxV-KS-i90"/>
                <outlet property="vwFav" destination="gvY-G9-320" id="otT-Pn-NDL"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <imageView contentMode="scaleToFill" image="playimg.png" id="15">
            <rect key="frame" x="0.0" y="0.0" width="35" height="30"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <point key="canvasLocation" x="-164.453125" y="254.88281249999997"/>
        </imageView>
        <view clipsSubviews="YES" contentMode="scaleToFill" id="4">
            <rect key="frame" x="0.0" y="0.0" width="70" height="62"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <subviews>
                <button contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="5">
                    <rect key="frame" x="1" y="1" width="68" height="62"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <gestureRecognizers/>
                    <fontDescription key="fontDescription" type="boldSystem" pointSize="15"/>
                    <state key="normal" image="playimg.png">
                        <color key="titleColor" red="0.19607843459999999" green="0.30980393290000002" blue="0.52156865600000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <color key="titleShadowColor" red="0.5" green="0.5" blue="0.5" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <state key="highlighted">
                        <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                </button>
                <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" fixedFrame="YES" text="" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="3" minimumFontSize="10" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="12">
                    <rect key="frame" x="2" y="33" width="66" height="25"/>
                    <autoresizingMask key="autoresizingMask" flexibleMinX="YES" widthSizable="YES" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <color key="backgroundColor" red="1" green="1" blue="1" alpha="0.5" colorSpace="custom" customColorSpace="sRGB"/>
                    <fontDescription key="fontDescription" type="system" pointSize="10"/>
                    <nil key="highlightedColor"/>
                </label>
                <view hidden="YES" contentMode="scaleToFill" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="LdV-eQ-qDk">
                    <rect key="frame" x="1" y="1" width="68" height="62"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.29999999999999999" colorSpace="calibratedRGB"/>
                </view>
                <button hidden="YES" opaque="NO" contentMode="redraw" fixedFrame="YES" contentHorizontalAlignment="trailing" contentVerticalAlignment="top" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="2LP-yD-VTR">
                    <rect key="frame" x="30" y="1" width="40" height="40"/>
                    <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
                    <state key="normal" image="unFavMedium.png"/>
                    <state key="selected" image="favMedium.png"/>
                </button>
                <button hidden="YES" opaque="NO" contentMode="redraw" fixedFrame="YES" contentHorizontalAlignment="trailing" contentVerticalAlignment="top" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="HRr-Z2-Mb1">
                    <rect key="frame" x="35" y="1" width="30" height="30"/>
                    <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
                    <state key="normal" image="unFavMedium.png"/>
                    <state key="selected" image="favMedium.png"/>
                </button>
                <button opaque="NO" contentMode="redraw" fixedFrame="YES" contentHorizontalAlignment="trailing" contentVerticalAlignment="top" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="z1T-JJ-u96">
                    <rect key="frame" x="40" y="1" width="25" height="25"/>
                    <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
                    <state key="normal" image="unFavSmall.png"/>
                    <state key="selected" image="favSmall.png"/>
                </button>
                <button hidden="YES" opaque="NO" contentMode="left" fixedFrame="YES" contentHorizontalAlignment="left" contentVerticalAlignment="top" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="eNO-kY-U53">
                    <rect key="frame" x="2" y="2" width="25" height="25"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <state key="normal" image="btnDeleteImage.png"/>
                </button>
                <view hidden="YES" contentMode="scaleToFill" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="gvY-G9-320">
                    <rect key="frame" x="60" y="0.0" width="10" height="10"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <color key="backgroundColor" systemColor="systemGreenColor"/>
                </view>
            </subviews>
            <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.0" colorSpace="custom" customColorSpace="sRGB"/>
            <gestureRecognizers/>
            <nil key="simulatedStatusBarMetrics"/>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <point key="canvasLocation" x="-94.53125" y="46.875"/>
        </view>
        <label opaque="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="3" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" id="gAH-GB-eAN" customClass="CustomLabel">
            <rect key="frame" x="0.0" y="0.0" width="100" height="60"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.0" colorSpace="custom" customColorSpace="sRGB"/>
            <fontDescription key="fontDescription" type="system" weight="medium" pointSize="14"/>
            <color key="textColor" red="0.66666668653488159" green="0.66666668653488159" blue="0.66666668653488159" alpha="0.59999999999999998" colorSpace="custom" customColorSpace="sRGB"/>
            <nil key="highlightedColor"/>
            <point key="canvasLocation" x="-139.0625" y="148.2421875"/>
        </label>
        <view contentMode="scaleToFill" id="GeT-Kc-Zmb">
            <rect key="frame" x="0.0" y="0.0" width="100" height="7"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <subviews>
                <imageView opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" fixedFrame="YES" image="leftCorner.png" translatesAutoresizingMaskIntoConstraints="NO" id="upV-Mv-cjT">
                    <rect key="frame" x="0.0" y="0.0" width="100" height="10"/>
                    <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMaxY="YES"/>
                </imageView>
                <button opaque="NO" contentMode="scaleAspectFit" fixedFrame="YES" contentHorizontalAlignment="left" contentVerticalAlignment="top" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="oS2-x0-h8p">
                    <rect key="frame" x="0.0" y="0.0" width="33" height="7"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" heightSizable="YES"/>
                    <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.0" colorSpace="custom" customColorSpace="sRGB"/>
                    <state key="normal" image="Red.png"/>
                </button>
                <button opaque="NO" contentMode="scaleAspectFit" fixedFrame="YES" contentHorizontalAlignment="left" contentVerticalAlignment="top" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="oyJ-Fc-f7x">
                    <rect key="frame" x="80" y="0.0" width="15" height="22"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" heightSizable="YES"/>
                    <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.0" colorSpace="custom" customColorSpace="sRGB"/>
                    <state key="normal" image="btnCopy.png"/>
                </button>
            </subviews>
            <color key="backgroundColor" red="0.10588235294117647" green="0.13333333333333333" blue="0.15294117647058825" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
            <nil key="simulatedStatusBarMetrics"/>
            <nil key="simulatedTopBarMetrics"/>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <point key="canvasLocation" x="-139.0625" y="210.64453125"/>
        </view>
        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" id="Apt-si-PaL">
            <rect key="frame" x="0.0" y="0.0" width="25" height="25"/>
            <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
            <state key="normal" image="btnInfo.png"/>
            <point key="canvasLocation" x="104" y="114"/>
        </button>
        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" id="f7p-MC-WbR">
            <rect key="frame" x="0.0" y="0.0" width="25" height="25"/>
            <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
            <state key="normal" image="btnCopy.png"/>
            <point key="canvasLocation" x="104" y="61"/>
        </button>
        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" id="aYa-wN-nNv">
            <rect key="frame" x="0.0" y="0.0" width="25" height="25"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
            <state key="normal" image="unFavSmall.png"/>
            <state key="selected" image="favSmall.png"/>
            <point key="canvasLocation" x="3.515625" y="111.62109374999999"/>
        </button>
    </objects>
    <resources>
        <image name="Red.png" width="11.5" height="13.5"/>
        <image name="btnCopy.png" width="15" height="14.5"/>
        <image name="btnDeleteImage.png" width="15" height="15"/>
        <image name="btnInfo.png" width="15" height="15"/>
        <image name="favMedium.png" width="40" height="40"/>
        <image name="favSmall.png" width="20" height="20"/>
        <image name="leftCorner.png" width="313" height="48"/>
        <image name="playimg.png" width="51" height="51"/>
        <image name="unFavMedium.png" width="40" height="40"/>
        <image name="unFavSmall.png" width="20" height="20"/>
        <systemColor name="systemGreenColor">
            <color red="0.20392156859999999" green="0.78039215689999997" blue="0.34901960780000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
    </resources>
</document>
