//
//  AnimationView.m
//  BIteFXproject
//
//  Created by IndiaNIC Infotech Ltd on 19/12/11.
//  Copyright (c) 2011 __MyCompanyName__. All rights reserved.
//

#import "AnimationView.h"

@implementation AnimationView

@synthesize lblName,btnVid,animDelegate;

- (instancetype)initWithNibName:(NSString *)nibNameOrNil bundle:(NSBundle *)nibBundleOrNil
{
    self = [super initWithNibName:nibNameOrNil bundle:nibBundleOrNil];
    if (self) {
        // Custom initialization
    }
    return self;
}

- (void)didReceiveMemoryWarning
{
    // Releases the view if it doesn't have a superview.
    [super didReceiveMemoryWarning];
    
    // Release any cached data, images, etc that aren't in use.
}

#pragma mark - View lifecycle

- (void)viewDidLoad
{
    [super viewDidLoad];
    // Do any additional setup after loading the view from its nib.
}


//- (BOOL)shouldAutorotateToInterfaceOrientation:(UIInterfaceOrientation)interfaceOrientation
//{
//    // Return YES for supported orientations
//    return YES;
//}



#pragma mark - User Define Methods

- (IBAction)onClickPlay:(id)sender
{
    [animDelegate playVideo:sender];
}

- (IBAction)onClickSelect:(id)sender
{
    [animDelegate selectVideo:sender];
}

@end
