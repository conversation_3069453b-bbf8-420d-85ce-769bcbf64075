<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.iPad.XIB" version="3.0" toolsVersion="24093.7" targetRuntime="iOS.CocoaTouch.iPad" propertyAccessControl="none" useAutolayout="YES" colorMatched="YES">
    <device id="ipad9_7" orientation="portrait" layout="fullscreen" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="24053.1"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="BiteFXMainScreenVC">
            <connections>
                <outlet property="BtnRegistration" destination="173" id="352"/>
                <outlet property="SliderView" destination="88" id="91"/>
                <outlet property="btnAnimationPanel" destination="105" id="146"/>
                <outlet property="btnCheckUpdate" destination="200" id="216"/>
                <outlet property="btnDownloadUpdate" destination="201" id="217"/>
                <outlet property="btnFavUnfav" destination="pMD-RO-yOD" id="ndL-V0-mSF"/>
                <outlet property="btnFlip" destination="Ea9-xr-Ix1" id="xUE-q2-vUd"/>
                <outlet property="btnHelp" destination="104" id="132"/>
                <outlet property="btnHelpClose" destination="JaQ-Nc-1gv" id="Ddb-Tz-1nP"/>
                <outlet property="btnHelpView" destination="165" id="166"/>
                <outlet property="btnInfo" destination="103" id="130"/>
                <outlet property="btnMenu" destination="102" id="131"/>
                <outlet property="btnNextFrame" destination="107" id="134"/>
                <outlet property="btnPreviousFrame" destination="106" id="135"/>
                <outlet property="btnProducts" destination="382" id="383"/>
                <outlet property="btnPurchase" destination="172" id="378"/>
                <outlet property="btnRegister" destination="190" id="241"/>
                <outlet property="btnRemainDownload" destination="390" id="392"/>
                <outlet property="btnRestore" destination="385" id="386"/>
                <outlet property="btnSkip" destination="TXp-8p-Uoi" id="NwE-Oi-H73"/>
                <outlet property="btnUninstall" destination="U5O-E3-ZCY" id="cUC-hf-TF4"/>
                <outlet property="btnUpdate" destination="174" id="353"/>
                <outlet property="btn_SpeedControl" destination="81H-SR-vOM" id="TYX-3Q-ACt"/>
                <outlet property="imgAboutBiteFX" destination="185" id="206"/>
                <outlet property="imgFramecounter" destination="263" id="265"/>
                <outlet property="imgMenuUpdates" destination="343" id="351"/>
                <outlet property="imgUpdatesAvailable" destination="341" id="350"/>
                <outlet property="img_SliderArea" destination="113" id="OPy-r4-RX2"/>
                <outlet property="imgview_fullImage" destination="PkQ-qv-wf1" id="08P-rI-jpR"/>
                <outlet property="lblAboutBasic" destination="8v1-Fj-OGP" id="ipV-M9-Sl0"/>
                <outlet property="lblAppVersion" destination="261" id="rZw-65-pmb"/>
                <outlet property="lblDownloadMsg" destination="309" id="312"/>
                <outlet property="lblFramcounter" destination="264" id="266"/>
                <outlet property="lblLicenceEmail" destination="237" id="244"/>
                <outlet property="lblMain" destination="202" id="214"/>
                <outlet property="lblMenuUpdates" destination="344" id="349"/>
                <outlet property="lblNoofVideoCompleted" destination="394" id="397"/>
                <outlet property="lblNoupDates" destination="335" id="336"/>
                <outlet property="lblProgresspercent" destination="305" id="306"/>
                <outlet property="lblPurchaseOptions" destination="362" id="uaU-t2-xYB"/>
                <outlet property="lblSubtitle" destination="203" id="218"/>
                <outlet property="lblThanksRegistration" destination="337" id="346"/>
                <outlet property="lblUpdatesAvailble" destination="273" id="Aul-WB-Enf"/>
                <outlet property="lblUpdatesavailable" destination="268" id="348"/>
                <outlet property="lblVersion" destination="IUl-KC-rCa" id="b4R-Qz-elg"/>
                <outlet property="lblfullorupdate" destination="301" id="311"/>
                <outlet property="loopBtn" destination="111" id="136"/>
                <outlet property="m_playerView" destination="6" id="7"/>
                <outlet property="playPauseBtn" destination="110" id="133"/>
                <outlet property="progressBar" destination="303" id="307"/>
                <outlet property="sliderSpeedControl" destination="256" id="257"/>
                <outlet property="sliderTimeForVideo" destination="114" id="116"/>
                <outlet property="tblViewPurchaseOption" destination="358" id="376"/>
                <outlet property="tblViewSkipUpdate" destination="n7u-Ys-h0l" id="QwC-nC-1lv"/>
                <outlet property="tblViewUpdates" destination="314" id="317"/>
                <outlet property="txtFieldEmail" destination="209" id="234"/>
                <outlet property="txtFieldSerialnumber1" destination="210" id="230"/>
                <outlet property="verticalToolbarView" destination="96" id="98"/>
                <outlet property="view" destination="2" id="3"/>
                <outlet property="viewAboutBasic" destination="v5G-Gn-5H6" id="fmC-Jl-liV"/>
                <outlet property="viewAboutBitefx" destination="184" id="186"/>
                <outlet property="viewAboutLogin" destination="Fz7-N0-7lH" id="az6-o5-Eso"/>
                <outlet property="viewAboutSubscription" destination="T3e-td-ynQ" id="2DJ-aY-cKc"/>
                <outlet property="viewDownloadingData" destination="295" id="296"/>
                <outlet property="viewFortableHeader" destination="327" id="328"/>
                <outlet property="viewPopup" destination="170" id="176"/>
                <outlet property="viewPurchase" destination="354" id="375"/>
                <outlet property="viewPurchaseDetails" destination="lEC-EY-R82" id="Q0O-M6-FCw"/>
                <outlet property="viewRegistration" destination="187" id="189"/>
                <outlet property="viewSkipList" destination="8Ps-8h-7LW" id="Moq-oK-FtV"/>
                <outlet property="viewSubscriptionOptions" destination="RUA-tl-uXx" id="3ng-PB-u1o"/>
                <outlet property="viewUpdates" destination="198" id="205"/>
                <outlet property="view_Back" destination="248" id="249"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="295" userLabel="ViewDownloadingdata">
            <rect key="frame" x="0.0" y="0.0" width="1024" height="768"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <subviews>
                <imageView userInteractionEnabled="NO" contentMode="scaleToFill" fixedFrame="YES" image="DialogNewDownload.png" translatesAutoresizingMaskIntoConstraints="NO" id="297">
                    <rect key="frame" x="68" y="28" width="794" height="637"/>
                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                </imageView>
                <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" fixedFrame="YES" text="" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumFontSize="10" translatesAutoresizingMaskIntoConstraints="NO" id="305">
                    <rect key="frame" x="192" y="562" width="547" height="33"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <fontDescription key="fontDescription" type="boldSystem" pointSize="18"/>
                    <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <nil key="highlightedColor"/>
                </label>
                <progressView opaque="NO" contentMode="scaleToFill" fixedFrame="YES" progress="0.10000000149011612" translatesAutoresizingMaskIntoConstraints="NO" id="303">
                    <rect key="frame" x="328" y="613" width="274" height="2"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.0" colorSpace="custom" customColorSpace="sRGB"/>
                    <color key="progressTintColor" red="0.98073577880859375" green="0.50451958179473877" blue="0.11826214194297791" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <color key="trackTintColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                </progressView>
                <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="298">
                    <rect key="frame" x="814" y="43" width="26" height="26"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <fontDescription key="fontDescription" type="boldSystem" pointSize="15"/>
                    <state key="normal" image="close-download.png">
                        <color key="titleColor" red="0.19607843459999999" green="0.30980393290000002" blue="0.52156865600000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <color key="titleShadowColor" red="0.5" green="0.5" blue="0.5" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <state key="highlighted">
                        <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                </button>
                <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="299">
                    <rect key="frame" x="784" y="39" width="72" height="37"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <fontDescription key="fontDescription" type="boldSystem" pointSize="15"/>
                    <state key="normal">
                        <color key="titleColor" red="0.19607843459999999" green="0.30980393290000002" blue="0.52156865600000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <color key="titleShadowColor" red="0.5" green="0.5" blue="0.5" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <state key="highlighted">
                        <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <connections>
                        <action selector="btnStopDownload:" destination="-1" eventType="touchUpInside" id="308"/>
                    </connections>
                </button>
                <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" fixedFrame="YES" text="" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumFontSize="10" translatesAutoresizingMaskIntoConstraints="NO" id="301">
                    <rect key="frame" x="90" y="39" width="347" height="37"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <fontDescription key="fontDescription" type="boldSystem" pointSize="18"/>
                    <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <nil key="highlightedColor"/>
                </label>
                <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" fixedFrame="YES" text="" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="19" baselineAdjustment="none" minimumFontSize="10" translatesAutoresizingMaskIntoConstraints="NO" id="309">
                    <rect key="frame" x="100" y="283" width="730" height="260"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                    <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <nil key="highlightedColor"/>
                </label>
                <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" fixedFrame="YES" text="" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="10" baselineAdjustment="none" minimumFontSize="10" translatesAutoresizingMaskIntoConstraints="NO" id="394">
                    <rect key="frame" x="192" y="520" width="547" height="37"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <fontDescription key="fontDescription" type="boldSystem" pointSize="18"/>
                    <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <nil key="highlightedColor"/>
                </label>
                <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" fixedFrame="YES" text="" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumFontSize="10" translatesAutoresizingMaskIntoConstraints="NO" id="337">
                    <rect key="frame" x="100" y="237" width="730" height="38"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <fontDescription key="fontDescription" type="boldSystem" pointSize="20"/>
                    <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <nil key="highlightedColor"/>
                </label>
            </subviews>
            <color key="backgroundColor" red="0.0039215686269999999" green="0.0" blue="0.0" alpha="0.69999999999999996" colorSpace="custom" customColorSpace="sRGB"/>
            <nil key="simulatedStatusBarMetrics"/>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <point key="canvasLocation" x="416.40625" y="220.8984375"/>
        </view>
        <view contentMode="scaleToFill" id="248">
            <rect key="frame" x="0.0" y="0.0" width="929" height="768"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
            <nil key="simulatedStatusBarMetrics"/>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <point key="canvasLocation" x="475" y="-267"/>
        </view>
        <view contentMode="scaleToFill" id="198" userLabel="ViewUpdates">
            <rect key="frame" x="0.0" y="0.0" width="1024" height="768"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <subviews>
                <imageView contentMode="scaleToFill" fixedFrame="YES" image="updates-bg.png" translatesAutoresizingMaskIntoConstraints="NO" id="199">
                    <rect key="frame" x="68" y="28" width="794" height="637"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                </imageView>
                <view contentMode="scaleToFill" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="327">
                    <rect key="frame" x="91" y="194" width="748" height="47"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <subviews>
                        <imageView userInteractionEnabled="NO" contentMode="scaleToFill" fixedFrame="YES" image="Line1.png" translatesAutoresizingMaskIntoConstraints="NO" id="331">
                            <rect key="frame" x="79" y="0.0" width="2" height="47"/>
                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                        </imageView>
                        <imageView userInteractionEnabled="NO" contentMode="scaleToFill" fixedFrame="YES" image="Line1.png" translatesAutoresizingMaskIntoConstraints="NO" id="332">
                            <rect key="frame" x="230" y="0.0" width="2" height="47"/>
                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                        </imageView>
                        <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" fixedFrame="YES" text="Number" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumFontSize="10" translatesAutoresizingMaskIntoConstraints="NO" id="320">
                            <rect key="frame" x="12" y="10" width="56" height="28"/>
                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" fixedFrame="YES" text="Description" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumFontSize="10" translatesAutoresizingMaskIntoConstraints="NO" id="322">
                            <rect key="frame" x="414" y="10" width="142" height="27"/>
                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" fixedFrame="YES" text="Date" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumFontSize="10" translatesAutoresizingMaskIntoConstraints="NO" id="321">
                            <rect key="frame" x="119" y="10" width="68" height="27"/>
                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="highlightedColor"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" red="0.66666666666666663" green="0.66666666666666663" blue="0.66666666666666663" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                </view>
                <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" fixedFrame="YES" text="Last check for updates:" lineBreakMode="tailTruncation" minimumFontSize="10" translatesAutoresizingMaskIntoConstraints="NO" id="203">
                    <rect key="frame" x="91" y="83" width="291" height="30"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <fontDescription key="fontDescription" name="ArialMT" family="Arial" pointSize="14"/>
                    <color key="textColor" red="0.17254901959999999" green="0.2156862745" blue="0.25098039220000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <nil key="highlightedColor"/>
                </label>
                <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" fixedFrame="YES" text="Updates available:" lineBreakMode="tailTruncation" minimumFontSize="10" translatesAutoresizingMaskIntoConstraints="NO" id="273">
                    <rect key="frame" x="93" y="170" width="561" height="30"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <fontDescription key="fontDescription" name="ArialMT" family="Arial" pointSize="14"/>
                    <color key="textColor" red="0.17254901959999999" green="0.2156862745" blue="0.25098039220000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <nil key="highlightedColor"/>
                </label>
                <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" fixedFrame="YES" text="BiteFX Update" lineBreakMode="tailTruncation" minimumFontSize="10" translatesAutoresizingMaskIntoConstraints="NO" id="202">
                    <rect key="frame" x="93" y="35" width="435" height="40"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <fontDescription key="fontDescription" name="Arial-BoldMT" family="Arial" pointSize="18"/>
                    <color key="textColor" red="0.54117647059999996" green="0.57254901960000004" blue="0.59999999999999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <nil key="highlightedColor"/>
                </label>
                <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="201">
                    <rect key="frame" x="91" y="596" width="748" height="36"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <fontDescription key="fontDescription" type="boldSystem" pointSize="15"/>
                    <state key="normal" image="download.png">
                        <color key="titleColor" red="0.19607843459999999" green="0.30980393290000002" blue="0.52156865600000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <color key="titleShadowColor" red="0.5" green="0.5" blue="0.5" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <state key="selected" image="downloadPRessed.png"/>
                    <state key="highlighted" image="downloadPRessed.png">
                        <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <connections>
                        <action selector="btnDownloadUpdate:" destination="-1" eventType="touchUpInside" id="243"/>
                    </connections>
                </button>
                <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="200">
                    <rect key="frame" x="91" y="112" width="748" height="36"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <fontDescription key="fontDescription" type="boldSystem" pointSize="15"/>
                    <state key="normal" image="checkForUpdates.png">
                        <color key="titleColor" red="0.19607843459999999" green="0.30980393290000002" blue="0.52156865600000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <color key="titleShadowColor" red="0.5" green="0.5" blue="0.5" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <state key="selected" image="checkForUpdatesPressed.png"/>
                    <state key="highlighted" image="checkForUpdatesPressed.png">
                        <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <connections>
                        <action selector="onClickCheckForUpdate:" destination="-1" eventType="touchUpInside" id="389"/>
                    </connections>
                </button>
                <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="259">
                    <rect key="frame" x="815" y="42" width="26" height="26"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <fontDescription key="fontDescription" type="boldSystem" pointSize="15"/>
                    <state key="normal" image="X.png">
                        <color key="titleColor" red="0.19607843459999999" green="0.30980393290000002" blue="0.52156865600000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <color key="titleShadowColor" red="0.5" green="0.5" blue="0.5" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <state key="highlighted">
                        <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <connections>
                        <action selector="CancelDownloadUpdate:" destination="-1" eventType="touchUpInside" id="260"/>
                    </connections>
                </button>
                <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" fixedFrame="YES" text="" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumFontSize="10" translatesAutoresizingMaskIntoConstraints="NO" id="335">
                    <rect key="frame" x="95" y="264" width="744" height="43"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                    <color key="textColor" red="0.01630434783" green="0.01630434783" blue="0.01630434783" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <nil key="highlightedColor"/>
                </label>
                <tableView clipsSubviews="YES" clearsContextBeforeDrawing="NO" contentMode="scaleToFill" fixedFrame="YES" bounces="NO" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" style="plain" separatorStyle="default" rowHeight="44" sectionHeaderHeight="22" sectionFooterHeight="22" translatesAutoresizingMaskIntoConstraints="NO" id="314">
                    <rect key="frame" x="91" y="241" width="748" height="336"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.0" colorSpace="custom" customColorSpace="sRGB"/>
                    <color key="separatorColor" red="0.66666666666666663" green="0.66666666666666663" blue="0.66666666666666663" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <connections>
                        <outlet property="dataSource" destination="-1" id="318"/>
                        <outlet property="delegate" destination="-1" id="319"/>
                    </connections>
                </tableView>
                <label hidden="YES" opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" fixedFrame="YES" text="Latest version () : Current version ()" lineBreakMode="tailTruncation" minimumFontSize="10" translatesAutoresizingMaskIntoConstraints="NO" id="IUl-KC-rCa">
                    <rect key="frame" x="548" y="83" width="291" height="30"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <fontDescription key="fontDescription" name="ArialMT" family="Arial" pointSize="14"/>
                    <color key="textColor" red="0.17254901959999999" green="0.2156862745" blue="0.25098039220000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <nil key="highlightedColor"/>
                </label>
            </subviews>
            <color key="backgroundColor" red="0.0039215686269999999" green="0.0" blue="0.0" alpha="0.69999999999999996" colorSpace="custom" customColorSpace="sRGB"/>
            <nil key="simulatedStatusBarMetrics"/>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <point key="canvasLocation" x="474" y="-755"/>
        </view>
        <view contentMode="scaleToFill" id="354" userLabel="ViewPurchase">
            <rect key="frame" x="0.0" y="0.0" width="1024" height="768"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <subviews>
                <imageView contentMode="scaleToFill" fixedFrame="YES" image="download-bg.png" translatesAutoresizingMaskIntoConstraints="NO" id="357">
                    <rect key="frame" x="68" y="28" width="794" height="637"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                </imageView>
                <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" fixedFrame="YES" text="Note: Current BiteFX Members should use the Login option to obtain the full BiteFX content" lineBreakMode="tailTruncation" minimumFontSize="10" translatesAutoresizingMaskIntoConstraints="NO" id="361">
                    <rect key="frame" x="91" y="96" width="600" height="30"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <fontDescription key="fontDescription" name="Arial-BoldMT" family="Arial" pointSize="13"/>
                    <color key="textColor" red="0.45098039220000002" green="0.47058823529999999" blue="0.49019607840000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <nil key="highlightedColor"/>
                </label>
                <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" fixedFrame="YES" text="BiteFX Purchase Options" lineBreakMode="tailTruncation" minimumFontSize="10" translatesAutoresizingMaskIntoConstraints="NO" id="362">
                    <rect key="frame" x="93" y="35" width="435" height="40"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <fontDescription key="fontDescription" name="Arial-BoldMT" family="Arial" pointSize="18"/>
                    <color key="textColor" red="0.54117647059999996" green="0.57254901960000004" blue="0.59999999999999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <nil key="highlightedColor"/>
                </label>
                <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="360">
                    <rect key="frame" x="815" y="42" width="26" height="26"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <fontDescription key="fontDescription" type="boldSystem" pointSize="15"/>
                    <state key="normal" image="X.png">
                        <color key="titleColor" red="0.19607843459999999" green="0.30980393290000002" blue="0.52156865600000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <color key="titleShadowColor" red="0.5" green="0.5" blue="0.5" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <state key="highlighted">
                        <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <connections>
                        <action selector="onClickPurchaseClose:" destination="-1" eventType="touchUpInside" id="377"/>
                    </connections>
                </button>
                <tableView clipsSubviews="YES" clearsContextBeforeDrawing="NO" tag="6" contentMode="scaleToFill" fixedFrame="YES" bounces="NO" style="plain" separatorStyle="default" rowHeight="44" sectionHeaderHeight="22" sectionFooterHeight="22" translatesAutoresizingMaskIntoConstraints="NO" id="358">
                    <rect key="frame" x="91" y="139" width="748" height="354"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <color key="backgroundColor" systemColor="groupTableViewBackgroundColor"/>
                    <color key="separatorColor" red="0.66666666666666663" green="0.66666666666666663" blue="0.66666666666666663" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <connections>
                        <outlet property="dataSource" destination="-1" id="371"/>
                        <outlet property="delegate" destination="-1" id="370"/>
                    </connections>
                </tableView>
                <view contentMode="scaleToFill" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="RUA-tl-uXx">
                    <rect key="frame" x="91" y="493" width="748" height="150"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <subviews>
                        <view contentMode="scaleToFill" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="OiW-Lq-GJG" userLabel="ViewRestore">
                            <rect key="frame" x="20" y="10" width="344" height="130"/>
                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                            <subviews>
                                <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" fixedFrame="YES" text="If you have purchased a BiteFX subscription on another iPad, use Restore Purchase to obtain the full content on this device:" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="4" minimumFontSize="10" translatesAutoresizingMaskIntoConstraints="NO" id="5sY-13-7NQ">
                                    <rect key="frame" x="0.0" y="0.0" width="344" height="75"/>
                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                    <fontDescription key="fontDescription" name="Arial-BoldMT" family="Arial" pointSize="13"/>
                                    <color key="textColor" red="0.45098039220000002" green="0.47058823529999999" blue="0.49019607840000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    <nil key="highlightedColor"/>
                                </label>
                                <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="385">
                                    <rect key="frame" x="92" y="80" width="150" height="40"/>
                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                    <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <fontDescription key="fontDescription" type="boldSystem" pointSize="15"/>
                                    <state key="normal" title="Restore Purchase">
                                        <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <color key="titleShadowColor" red="0.5" green="0.5" blue="0.5" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </state>
                                    <state key="highlighted">
                                        <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </state>
                                    <userDefinedRuntimeAttributes>
                                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                            <integer key="value" value="5"/>
                                        </userDefinedRuntimeAttribute>
                                    </userDefinedRuntimeAttributes>
                                    <connections>
                                        <action selector="onClickRestore:" destination="-1" eventType="touchUpInside" id="387"/>
                                    </connections>
                                </button>
                            </subviews>
                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        </view>
                        <view contentMode="scaleToFill" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Wak-Hm-RRZ" userLabel="ViewManage">
                            <rect key="frame" x="384" y="10" width="344" height="130"/>
                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                            <subviews>
                                <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" fixedFrame="YES" text="To manage your subscription including payment informaton or to cancel your subscription, use this button:" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="4" minimumFontSize="10" translatesAutoresizingMaskIntoConstraints="NO" id="bdc-OZ-o0X">
                                    <rect key="frame" x="0.0" y="0.0" width="344" height="75"/>
                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                    <fontDescription key="fontDescription" name="Arial-BoldMT" family="Arial" pointSize="13"/>
                                    <color key="textColor" red="0.45098039220000002" green="0.47058823529999999" blue="0.49019607840000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    <nil key="highlightedColor"/>
                                </label>
                                <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="cgS-Rk-zb2">
                                    <rect key="frame" x="57" y="80" width="230" height="40"/>
                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                    <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <fontDescription key="fontDescription" type="boldSystem" pointSize="15"/>
                                    <state key="normal" title="Manage Your Subscription">
                                        <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <color key="titleShadowColor" red="0.5" green="0.5" blue="0.5" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </state>
                                    <state key="highlighted">
                                        <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </state>
                                    <userDefinedRuntimeAttributes>
                                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                            <integer key="value" value="5"/>
                                        </userDefinedRuntimeAttribute>
                                    </userDefinedRuntimeAttributes>
                                    <connections>
                                        <action selector="btnManageSubscription:" destination="-1" eventType="touchUpInside" id="ZuV-7n-Nku"/>
                                    </connections>
                                </button>
                            </subviews>
                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        </view>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                </view>
                <view contentMode="scaleToFill" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="lEC-EY-R82">
                    <rect key="frame" x="91" y="139" width="748" height="504"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                </view>
                <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="382">
                    <rect key="frame" x="699" y="96" width="140" height="30"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <fontDescription key="fontDescription" type="boldSystem" pointSize="15"/>
                    <state key="normal" image="products1.png">
                        <color key="titleColor" red="0.19607843459999999" green="0.30980393290000002" blue="0.52156865600000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <color key="titleShadowColor" red="0.5" green="0.5" blue="0.5" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <state key="highlighted">
                        <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <connections>
                        <action selector="onClickProducts:" destination="-1" eventType="touchUpInside" id="384"/>
                    </connections>
                </button>
            </subviews>
            <color key="backgroundColor" red="0.0039215686269999999" green="0.0" blue="0.0" alpha="0.69999999999999996" colorSpace="custom" customColorSpace="sRGB"/>
            <nil key="simulatedStatusBarMetrics"/>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <point key="canvasLocation" x="-491" y="-1243"/>
        </view>
        <view contentMode="scaleToFill" id="187" userLabel="ViewRegistration">
            <rect key="frame" x="0.0" y="0.0" width="1024" height="768"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <subviews>
                <imageView contentMode="scaleToFill" fixedFrame="YES" image="register.png" translatesAutoresizingMaskIntoConstraints="NO" id="188">
                    <rect key="frame" x="317" y="143" width="390" height="286"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                </imageView>
                <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="190">
                    <rect key="frame" x="325" y="375" width="89" height="36"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <fontDescription key="fontDescription" type="boldSystem" pointSize="15"/>
                    <state key="normal" image="login-normal.png">
                        <color key="titleColor" red="0.19607843459999999" green="0.30980393290000002" blue="0.52156865600000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <color key="titleShadowColor" red="0.5" green="0.5" blue="0.5" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <state key="selected" image="Register-pressed.PNG"/>
                    <state key="highlighted" image="login-pressed.png">
                        <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <connections>
                        <action selector="btnRegisterAction:" destination="-1" eventType="touchUpInside" id="236"/>
                    </connections>
                </button>
                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="left" contentVerticalAlignment="center" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="209">
                    <rect key="frame" x="336" y="264" width="352" height="31"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <color key="textColor" red="0.36078431370000003" green="0.3803921569" blue="0.40000000000000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <fontDescription key="fontDescription" name="Arial-BoldMT" family="Arial" pointSize="16"/>
                    <textInputTraits key="textInputTraits" autocorrectionType="no" keyboardType="emailAddress" returnKeyType="next" enablesReturnKeyAutomatically="YES"/>
                    <connections>
                        <outlet property="delegate" destination="-1" id="235"/>
                    </connections>
                </textField>
                <textField opaque="NO" clipsSubviews="YES" tag="1" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="left" contentVerticalAlignment="center" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="210">
                    <rect key="frame" x="336" y="332" width="352" height="31"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <color key="textColor" red="0.36078431370000003" green="0.3803921569" blue="0.40000000000000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <fontDescription key="fontDescription" name="Arial-BoldMT" family="Arial" pointSize="16"/>
                    <textInputTraits key="textInputTraits" keyboardType="numberPad" secureTextEntry="YES"/>
                    <connections>
                        <outlet property="delegate" destination="-1" id="226"/>
                    </connections>
                </textField>
                <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="250">
                    <rect key="frame" x="667" y="156" width="26" height="26"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <fontDescription key="fontDescription" type="boldSystem" pointSize="15"/>
                    <state key="normal" image="X.png">
                        <color key="titleColor" red="0.19607843459999999" green="0.30980393290000002" blue="0.52156865600000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <color key="titleShadowColor" red="0.5" green="0.5" blue="0.5" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <state key="highlighted">
                        <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <connections>
                        <action selector="CancelRegistrationView:" destination="-1" eventType="touchUpInside" id="251"/>
                    </connections>
                </button>
            </subviews>
            <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.69999999999999996" colorSpace="custom" customColorSpace="sRGB"/>
            <nil key="simulatedStatusBarMetrics"/>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <point key="canvasLocation" x="473" y="-1731"/>
        </view>
        <view contentMode="scaleToFill" id="184" userLabel="ViewAboutBiteFX">
            <rect key="frame" x="0.0" y="0.0" width="1024" height="768"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <subviews>
                <imageView contentMode="scaleToFill" fixedFrame="YES" image="About.png" translatesAutoresizingMaskIntoConstraints="NO" id="185">
                    <rect key="frame" x="138" y="135" width="747" height="499"/>
                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                </imageView>
                <view contentMode="scaleToFill" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="lgm-Jm-rmz">
                    <rect key="frame" x="159" y="290" width="244" height="21"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <subviews>
                        <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" fixedFrame="YES" text="BiteFX for iPad®" lineBreakMode="tailTruncation" minimumFontSize="10" translatesAutoresizingMaskIntoConstraints="NO" id="kcS-zT-6ce">
                            <rect key="frame" x="0.0" y="0.0" width="155" height="21"/>
                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                            <fontDescription key="fontDescription" type="boldSystem" pointSize="19"/>
                            <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" fixedFrame="YES" text="0.0" lineBreakMode="tailTruncation" minimumFontSize="10" translatesAutoresizingMaskIntoConstraints="NO" id="261">
                            <rect key="frame" x="168" y="0.0" width="38" height="21"/>
                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                            <fontDescription key="fontDescription" type="boldSystem" pointSize="17"/>
                            <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                </view>
                <view hidden="YES" contentMode="scaleToFill" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Fz7-N0-7lH">
                    <rect key="frame" x="159" y="330" width="300" height="21"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <subviews>
                        <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" fixedFrame="YES" text="" lineBreakMode="tailTruncation" minimumFontSize="10" translatesAutoresizingMaskIntoConstraints="NO" id="237">
                            <rect key="frame" x="0.0" y="0.0" width="300" height="21"/>
                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                            <fontDescription key="fontDescription" name="Arial-BoldMT" family="Arial" pointSize="14"/>
                            <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                </view>
                <view hidden="YES" contentMode="scaleToFill" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="T3e-td-ynQ">
                    <rect key="frame" x="159" y="330" width="370" height="60"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <subviews>
                        <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" fixedFrame="YES" text="Active autorenewable subscription with iTunes Store." lineBreakMode="tailTruncation" minimumFontSize="10" translatesAutoresizingMaskIntoConstraints="NO" id="a7e-YV-2Rd">
                            <rect key="frame" x="0.0" y="0.0" width="370" height="21"/>
                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                            <fontDescription key="fontDescription" name="Arial-BoldMT" family="Arial" pointSize="14"/>
                            <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="left" contentVerticalAlignment="center" lineBreakMode="middleTruncation" hasAttributedTitle="YES" translatesAutoresizingMaskIntoConstraints="NO" id="tb2-zn-OMT">
                            <rect key="frame" x="0.0" y="21" width="370" height="35"/>
                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <state key="normal">
                                <attributedString key="attributedTitle">
                                    <fragment content="Tap here to manage or cancel your subscription.">
                                        <attributes>
                                            <color key="NSColor" red="0.99999600649999998" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                            <font key="NSFont" size="14" name="Arial-BoldMT"/>
                                            <paragraphStyle key="NSParagraphStyle" alignment="left" lineBreakMode="wordWrapping" baseWritingDirection="leftToRight" lineSpacing="1" defaultTabInterval="28" tighteningFactorForTruncation="0.0" allowsDefaultTighteningForTruncation="NO">
                                                <tabStops/>
                                            </paragraphStyle>
                                            <integer key="NSUnderline" value="1"/>
                                            <color key="NSUnderlineColor" white="0.0" alpha="1" colorSpace="calibratedWhite"/>
                                        </attributes>
                                    </fragment>
                                </attributedString>
                            </state>
                            <connections>
                                <action selector="btnManageSubscription:" destination="-1" eventType="touchUpInside" id="CR6-6B-iCg"/>
                            </connections>
                        </button>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                </view>
                <view contentMode="scaleToFill" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="v5G-Gn-5H6">
                    <rect key="frame" x="159" y="330" width="650" height="210"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <subviews>
                        <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" fixedFrame="YES" lineBreakMode="wordWrap" numberOfLines="0" minimumFontSize="10" translatesAutoresizingMaskIntoConstraints="NO" id="4W2-yu-5Fg">
                            <rect key="frame" x="0.0" y="0.0" width="650" height="85"/>
                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                            <string key="text">This is the free demo version of BiteFX.
It contains just a few sample animations, pictures and one example presentation template.
The full version contains over 140 animations, 140 pictures, and several presentation templates.

You can upgrade to the full version by:</string>
                            <fontDescription key="fontDescription" name="Arial-BoldMT" family="Arial" pointSize="14"/>
                            <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" fixedFrame="YES" text="" lineBreakMode="wordWrap" numberOfLines="0" minimumFontSize="10" translatesAutoresizingMaskIntoConstraints="NO" id="8v1-Fj-OGP">
                            <rect key="frame" x="25" y="90" width="625" height="120"/>
                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                            <fontDescription key="fontDescription" name="Arial-BoldMT" family="Arial" pointSize="14"/>
                            <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                </view>
                <view contentMode="scaleToFill" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="gVL-JX-tKK">
                    <rect key="frame" x="159" y="545" width="500" height="40"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <subviews>
                        <button opaque="NO" tag="1" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="left" contentVerticalAlignment="center" lineBreakMode="middleTruncation" hasAttributedTitle="YES" translatesAutoresizingMaskIntoConstraints="NO" id="9lI-EY-8FO">
                            <rect key="frame" x="0.0" y="0.0" width="155" height="40"/>
                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <state key="normal">
                                <attributedString key="attributedTitle">
                                    <fragment content="Terms and Conditions">
                                        <attributes>
                                            <color key="NSColor" red="0.99999600649999998" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                            <font key="NSFont" size="14" name="Arial-BoldMT"/>
                                            <paragraphStyle key="NSParagraphStyle" alignment="left" lineBreakMode="wordWrapping" baseWritingDirection="leftToRight" lineSpacing="1" defaultTabInterval="28" tighteningFactorForTruncation="0.0" allowsDefaultTighteningForTruncation="NO">
                                                <tabStops/>
                                            </paragraphStyle>
                                            <integer key="NSUnderline" value="1"/>
                                            <color key="NSUnderlineColor" white="0.0" alpha="1" colorSpace="calibratedWhite"/>
                                        </attributes>
                                    </fragment>
                                </attributedString>
                            </state>
                            <connections>
                                <action selector="btnTermsnPrivacyClick:" destination="-1" eventType="touchUpInside" id="jwb-iE-oHt"/>
                            </connections>
                        </button>
                        <button opaque="NO" tag="2" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="left" contentVerticalAlignment="center" lineBreakMode="middleTruncation" hasAttributedTitle="YES" translatesAutoresizingMaskIntoConstraints="NO" id="wtL-le-qYr">
                            <rect key="frame" x="170" y="0.0" width="100" height="40"/>
                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <state key="normal">
                                <attributedString key="attributedTitle">
                                    <fragment content="Privacy Policy">
                                        <attributes>
                                            <color key="NSColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                            <font key="NSFont" size="14" name="Arial-BoldMT"/>
                                            <paragraphStyle key="NSParagraphStyle" alignment="left" lineBreakMode="wordWrapping" baseWritingDirection="leftToRight" lineSpacing="1" defaultTabInterval="28" tighteningFactorForTruncation="0.0" allowsDefaultTighteningForTruncation="NO">
                                                <tabStops/>
                                            </paragraphStyle>
                                            <integer key="NSUnderline" value="1"/>
                                            <color key="NSUnderlineColor" white="0.0" alpha="1" colorSpace="calibratedWhite"/>
                                        </attributes>
                                    </fragment>
                                </attributedString>
                            </state>
                            <connections>
                                <action selector="btnTermsnPrivacyClick:" destination="-1" eventType="touchUpInside" id="cJ2-o1-Abo"/>
                            </connections>
                        </button>
                        <button opaque="NO" tag="3" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="left" contentVerticalAlignment="center" lineBreakMode="middleTruncation" hasAttributedTitle="YES" translatesAutoresizingMaskIntoConstraints="NO" id="BDz-t7-Ndn">
                            <rect key="frame" x="290" y="0.0" width="60" height="40"/>
                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <state key="normal">
                                <attributedString key="attributedTitle">
                                    <fragment content="Support">
                                        <attributes>
                                            <color key="NSColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                            <font key="NSFont" size="14" name="Arial-BoldMT"/>
                                            <paragraphStyle key="NSParagraphStyle" alignment="left" lineBreakMode="wordWrapping" baseWritingDirection="leftToRight" lineSpacing="1" defaultTabInterval="28" tighteningFactorForTruncation="0.0" allowsDefaultTighteningForTruncation="NO">
                                                <tabStops/>
                                            </paragraphStyle>
                                            <integer key="NSUnderline" value="1"/>
                                            <color key="NSUnderlineColor" white="0.0" alpha="1" colorSpace="calibratedWhite"/>
                                        </attributes>
                                    </fragment>
                                </attributedString>
                            </state>
                            <connections>
                                <action selector="btnTermsnPrivacyClick:" destination="-1" eventType="touchUpInside" id="Lh5-s8-NNm"/>
                            </connections>
                        </button>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                </view>
            </subviews>
            <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.69999999999999996" colorSpace="custom" customColorSpace="sRGB"/>
            <nil key="simulatedStatusBarMetrics"/>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <point key="canvasLocation" x="473" y="-2220"/>
        </view>
        <view contentMode="scaleToFill" id="170" userLabel="PopupView">
            <rect key="frame" x="0.0" y="0.0" width="1024" height="768"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <subviews>
                <imageView contentMode="scaleToFill" fixedFrame="YES" image="menuPopup.png" translatesAutoresizingMaskIntoConstraints="NO" id="171">
                    <rect key="frame" x="770" y="0.0" width="157" height="304"/>
                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                    <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.0" colorSpace="custom" customColorSpace="sRGB"/>
                </imageView>
                <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="172">
                    <rect key="frame" x="774" y="8" width="137" height="36"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <fontDescription key="fontDescription" type="boldSystem" pointSize="15"/>
                    <state key="normal" title="Purchase">
                        <color key="titleColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <color key="titleShadowColor" red="0.5" green="0.5" blue="0.5" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <state key="selected">
                        <color key="titleColor" red="1" green="0.50196078430000002" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <state key="highlighted">
                        <color key="titleColor" red="1" green="0.50196078430000002" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <connections>
                        <action selector="PurchaseAction:" destination="-1" eventType="touchUpInside" id="177"/>
                    </connections>
                </button>
                <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="173">
                    <rect key="frame" x="774" y="58" width="137" height="36"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <fontDescription key="fontDescription" type="boldSystem" pointSize="15"/>
                    <state key="normal" title="Login">
                        <color key="titleColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <color key="titleShadowColor" red="0.5" green="0.5" blue="0.5" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <state key="selected">
                        <color key="titleColor" red="1" green="0.50196078430000002" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <state key="highlighted">
                        <color key="titleColor" red="1" green="0.50196078430000002" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <connections>
                        <action selector="RegisterAction:" destination="-1" eventType="touchUpInside" id="178"/>
                    </connections>
                </button>
                <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="174">
                    <rect key="frame" x="774" y="107" width="137" height="36"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <fontDescription key="fontDescription" type="boldSystem" pointSize="15"/>
                    <state key="normal" title="Updates">
                        <color key="titleColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <color key="titleShadowColor" red="0.5" green="0.5" blue="0.5" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <state key="selected">
                        <color key="titleColor" red="1" green="0.50196078430000002" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <state key="highlighted">
                        <color key="titleColor" red="1" green="0.50196078430000002" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <connections>
                        <action selector="updatesAction:" destination="-1" eventType="touchUpInside" id="179"/>
                    </connections>
                </button>
                <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="175">
                    <rect key="frame" x="775" y="256" width="137" height="36"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <fontDescription key="fontDescription" type="boldSystem" pointSize="15"/>
                    <state key="normal" title="About BiteFX">
                        <color key="titleColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <color key="titleShadowColor" red="0.5" green="0.5" blue="0.5" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <state key="selected">
                        <color key="titleColor" red="1" green="0.50196078430000002" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <state key="highlighted">
                        <color key="titleColor" red="1" green="0.50196078430000002" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <connections>
                        <action selector="Aboutaction:" destination="-1" eventType="touchUpInside" id="180"/>
                    </connections>
                </button>
                <imageView userInteractionEnabled="NO" contentMode="scaleToFill" fixedFrame="YES" image="badge-big.png" translatesAutoresizingMaskIntoConstraints="NO" id="341">
                    <rect key="frame" x="868" y="106" width="25" height="18"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                </imageView>
                <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" fixedFrame="YES" text="" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumFontSize="10" translatesAutoresizingMaskIntoConstraints="NO" id="268">
                    <rect key="frame" x="867" y="107" width="25" height="16"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <fontDescription key="fontDescription" name="Arial-BoldMT" family="Arial" pointSize="14"/>
                    <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <nil key="highlightedColor"/>
                </label>
                <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" enabled="NO" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="wordWrap" translatesAutoresizingMaskIntoConstraints="NO" id="TXp-8p-Uoi">
                    <rect key="frame" x="775" y="202" width="136" height="49"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <fontDescription key="fontDescription" type="boldSystem" pointSize="15"/>
                    <state key="normal" title="Skipped Animations">
                        <color key="titleColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <color key="titleShadowColor" red="0.5" green="0.5" blue="0.5" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <state key="selected" title="Skipped Animations">
                        <color key="titleColor" red="1" green="0.50196078430000002" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <connections>
                        <action selector="btnSkipClicked:" destination="-1" eventType="touchUpInside" id="tz7-xX-Ak2"/>
                    </connections>
                </button>
                <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" enabled="NO" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="wordWrap" translatesAutoresizingMaskIntoConstraints="NO" id="U5O-E3-ZCY">
                    <rect key="frame" x="774" y="151" width="137" height="49"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <fontDescription key="fontDescription" type="boldSystem" pointSize="15"/>
                    <state key="normal" title="Uninstall Updates">
                        <color key="titleColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <color key="titleShadowColor" red="0.5" green="0.5" blue="0.5" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <state key="selected" title="Skipped Animations">
                        <color key="titleColor" red="1" green="0.50196078430000002" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <connections>
                        <action selector="btnUninstallUpdatesClicked:" destination="-1" eventType="touchUpInside" id="yt0-xE-cga"/>
                    </connections>
                </button>
            </subviews>
            <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.0" colorSpace="custom" customColorSpace="sRGB"/>
            <nil key="simulatedStatusBarMetrics"/>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <point key="canvasLocation" x="473" y="-2708"/>
        </view>
        <view tag="100" contentMode="scaleToFill" id="96" userLabel="VerticalToolbar">
            <rect key="frame" x="0.0" y="0.0" width="95" height="696"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <userGuides>
                <userLayoutGuide location="495" affinity="minY"/>
                <userLayoutGuide location="493" affinity="minY"/>
                <userLayoutGuide location="494" affinity="minY"/>
                <userLayoutGuide location="494" affinity="minY"/>
            </userGuides>
            <subviews>
                <imageView userInteractionEnabled="NO" contentMode="scaleToFill" fixedFrame="YES" image="toolbar.png" translatesAutoresizingMaskIntoConstraints="NO" id="101">
                    <rect key="frame" x="0.0" y="0.0" width="95" height="696"/>
                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                </imageView>
                <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="102">
                    <rect key="frame" x="0.0" y="0.0" width="96" height="52"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <fontDescription key="fontDescription" type="boldSystem" pointSize="15"/>
                    <state key="normal" image="Menu.png">
                        <color key="titleColor" red="0.19607843459999999" green="0.30980393290000002" blue="0.52156865600000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <color key="titleShadowColor" red="0.5" green="0.5" blue="0.5" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <state key="selected" image="MenuPressed.png"/>
                    <state key="highlighted" image="MenuPressed.png">
                        <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <connections>
                        <action selector="menu:" destination="-1" eventType="touchUpInside" id="121"/>
                    </connections>
                </button>
                <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="103">
                    <rect key="frame" x="0.0" y="52" width="96" height="52"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <fontDescription key="fontDescription" type="boldSystem" pointSize="15"/>
                    <state key="normal" image="Info.png">
                        <color key="titleColor" red="0.19607843459999999" green="0.30980393290000002" blue="0.52156865600000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <color key="titleShadowColor" red="0.5" green="0.5" blue="0.5" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <state key="selected" image="InfoPressed.png"/>
                    <state key="highlighted" image="InfoPressed.png">
                        <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <connections>
                        <action selector="info:" destination="-1" eventType="touchUpInside" id="122"/>
                    </connections>
                </button>
                <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="104">
                    <rect key="frame" x="0.0" y="104" width="96" height="52"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <fontDescription key="fontDescription" type="boldSystem" pointSize="15"/>
                    <state key="normal" image="Help.png">
                        <color key="titleColor" red="0.19607843459999999" green="0.30980393290000002" blue="0.52156865600000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <color key="titleShadowColor" red="0.5" green="0.5" blue="0.5" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <state key="selected" image="HelpPressed.png"/>
                    <state key="highlighted" image="HelpPressed.png">
                        <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <connections>
                        <action selector="help:" destination="-1" eventType="touchUpInside" id="123"/>
                    </connections>
                </button>
                <button opaque="NO" tag="20" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="105">
                    <rect key="frame" x="0.0" y="230" width="94" height="55"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <fontDescription key="fontDescription" type="boldSystem" pointSize="15"/>
                    <state key="normal" image="AnimationButton.png">
                        <color key="titleColor" red="0.19607843459999999" green="0.30980393290000002" blue="0.52156865600000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <color key="titleShadowColor" red="0.5" green="0.5" blue="0.5" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <state key="selected" image="AnimationPressed.png"/>
                    <state key="highlighted" image="AnimationPressed.png">
                        <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <connections>
                        <action selector="btnAnimationclick:" destination="-1" eventType="touchUpInside" id="124"/>
                    </connections>
                </button>
                <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="106">
                    <rect key="frame" x="6" y="297" width="84" height="60"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <fontDescription key="fontDescription" type="boldSystem" pointSize="15"/>
                    <state key="normal" image="PreviousButton.png">
                        <color key="titleColor" red="0.19607843459999999" green="0.30980393290000002" blue="0.52156865600000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <color key="titleShadowColor" red="0.5" green="0.5" blue="0.5" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <state key="selected" image="PreviousPressed.png"/>
                    <state key="highlighted" image="PreviousPressed.png">
                        <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <connections>
                        <action selector="clickPreviousFrame" destination="-1" eventType="touchUpInside" id="126"/>
                    </connections>
                </button>
                <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="107">
                    <rect key="frame" x="6" y="368" width="84" height="60"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <fontDescription key="fontDescription" type="boldSystem" pointSize="15"/>
                    <state key="normal" image="NextButtonImg.png">
                        <color key="titleColor" red="0.19607843459999999" green="0.30980393290000002" blue="0.52156865600000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <color key="titleShadowColor" red="0.5" green="0.5" blue="0.5" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <state key="selected" image="NextButtonPressedImg.png"/>
                    <state key="highlighted" image="NextButtonPressedImg.png">
                        <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <connections>
                        <action selector="clickNextFrame" destination="-1" eventType="touchUpInside" id="253"/>
                    </connections>
                </button>
                <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="110">
                    <rect key="frame" x="5" y="557" width="84" height="60"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <fontDescription key="fontDescription" type="boldSystem" pointSize="15"/>
                    <state key="normal" image="PlayButtonImg.png">
                        <color key="titleColor" red="0.19607843459999999" green="0.30980393290000002" blue="0.52156865600000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <color key="titleShadowColor" red="0.5" green="0.5" blue="0.5" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <state key="selected" image="PlayButtonPressed.png">
                        <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <state key="highlighted" image="PlayButtonPressed.png">
                        <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <connections>
                        <action selector="play:" destination="-1" eventType="touchUpInside" id="129"/>
                    </connections>
                </button>
                <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="111">
                    <rect key="frame" x="5" y="625" width="84" height="60"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <fontDescription key="fontDescription" type="boldSystem" pointSize="15"/>
                    <state key="normal" image="LoopButtonImg.png">
                        <color key="titleColor" red="0.19607843459999999" green="0.30980393290000002" blue="0.52156865600000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <color key="titleShadowColor" red="0.5" green="0.5" blue="0.5" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <state key="selected" image="LoopButtonPressedImg.png"/>
                    <state key="highlighted" image="LoopButtonPressedImg.png">
                        <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <connections>
                        <action selector="LoopBtnAction:" destination="-1" eventType="touchUpInside" id="143"/>
                    </connections>
                </button>
                <button opaque="NO" userInteractionEnabled="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" adjustsImageWhenHighlighted="NO" adjustsImageWhenDisabled="NO" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="81H-SR-vOM">
                    <rect key="frame" x="4" y="463" width="88" height="22"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <state key="normal" image="speed.png"/>
                </button>
                <imageView userInteractionEnabled="NO" contentMode="scaleToFill" fixedFrame="YES" image="speedslidernewimg.png" translatesAutoresizingMaskIntoConstraints="NO" id="255">
                    <rect key="frame" x="4" y="512" width="88" height="10"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.0" colorSpace="custom" customColorSpace="sRGB"/>
                </imageView>
                <slider opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" value="1" minValue="0.10000000149011612" maxValue="2" translatesAutoresizingMaskIntoConstraints="NO" id="256">
                    <rect key="frame" x="4" y="503" width="88" height="29"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <connections>
                        <action selector="valueChanged:" destination="-1" eventType="valueChanged" id="258"/>
                    </connections>
                </slider>
                <imageView userInteractionEnabled="NO" contentMode="scaleToFill" fixedFrame="YES" image="badge-big.png" translatesAutoresizingMaskIntoConstraints="NO" id="343">
                    <rect key="frame" x="70" y="0.0" width="25" height="18"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                </imageView>
                <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" fixedFrame="YES" text="" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumFontSize="10" translatesAutoresizingMaskIntoConstraints="NO" id="344">
                    <rect key="frame" x="70" y="2" width="25" height="16"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <fontDescription key="fontDescription" name="Arial-BoldMT" family="Arial" pointSize="14"/>
                    <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <nil key="highlightedColor"/>
                </label>
                <imageView hidden="YES" userInteractionEnabled="NO" contentMode="scaleToFill" fixedFrame="YES" image="badge-big.png" translatesAutoresizingMaskIntoConstraints="NO" id="MFO-gJ-hcn">
                    <rect key="frame" x="74" y="34" width="18" height="18"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                </imageView>
                <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="390">
                    <rect key="frame" x="0.0" y="172" width="96" height="50"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <fontDescription key="fontDescription" type="boldSystem" pointSize="15"/>
                    <state key="normal" image="resume-download.png">
                        <color key="titleColor" red="0.19607843459999999" green="0.30980393290000002" blue="0.52156865600000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <color key="titleShadowColor" red="0.5" green="0.5" blue="0.5" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <state key="highlighted">
                        <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <connections>
                        <action selector="btnCheckForRamain:" destination="-1" eventType="touchUpInside" id="391"/>
                    </connections>
                </button>
                <label hidden="YES" clipsSubviews="YES" userInteractionEnabled="NO" alpha="0.0" contentMode="left" fixedFrame="YES" text="" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumFontSize="10" translatesAutoresizingMaskIntoConstraints="NO" id="QCF-nW-zdq">
                    <rect key="frame" x="74" y="34" width="18" height="16"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <fontDescription key="fontDescription" name="Arial-BoldMT" family="Arial" pointSize="14"/>
                    <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <nil key="highlightedColor"/>
                </label>
            </subviews>
            <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.0" colorSpace="custom" customColorSpace="sRGB"/>
            <nil key="simulatedStatusBarMetrics"/>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <point key="canvasLocation" x="962" y="-2699"/>
        </view>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="2" userLabel="Playerview">
            <rect key="frame" x="0.0" y="0.0" width="1024" height="748"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <subviews>
                <imageView userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="PkQ-qv-wf1">
                    <rect key="frame" x="0.0" y="0.0" width="1024" height="768"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                </imageView>
                <view contentMode="scaleToFill" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="6" customClass="PlayerView">
                    <rect key="frame" x="0.0" y="0.0" width="1024" height="768"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <subviews>
                        <button opaque="NO" clearsContextBeforeDrawing="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" adjustsImageWhenHighlighted="NO" adjustsImageWhenDisabled="NO" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="165">
                            <rect key="frame" x="0.0" y="12" width="1024" height="748"/>
                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                            <fontDescription key="fontDescription" type="boldSystem" pointSize="20"/>
                            <state key="normal">
                                <color key="titleColor" red="0.5" green="0.5" blue="0.5" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <color key="titleShadowColor" red="0.5" green="0.5" blue="0.5" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            </state>
                            <state key="highlighted">
                                <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            </state>
                        </button>
                        <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="JaQ-Nc-1gv">
                            <rect key="frame" x="954" y="66" width="46" height="44"/>
                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                            <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.0" colorSpace="custom" customColorSpace="sRGB"/>
                            <connections>
                                <action selector="btnHelpClicked:" destination="-1" eventType="touchUpInside" id="N33-6w-EER"/>
                            </connections>
                        </button>
                    </subviews>
                    <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                </view>
                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" spacing="15" translatesAutoresizingMaskIntoConstraints="NO" id="SaB-uq-ugG">
                    <rect key="frame" x="862" y="10" width="40" height="150"/>
                    <subviews>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="1SC-5R-zq1">
                            <rect key="frame" x="0.0" y="0.0" width="40" height="40"/>
                            <constraints>
                                <constraint firstAttribute="width" secondItem="1SC-5R-zq1" secondAttribute="height" multiplier="1:1" id="W3p-EH-mIK"/>
                                <constraint firstAttribute="height" constant="40" id="zLg-Dl-9wP"/>
                            </constraints>
                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                            <state key="normal" image="crossSelected.png"/>
                            <state key="selected" image="fav.png"/>
                            <state key="highlighted" image="crossSelected.png"/>
                            <connections>
                                <action selector="btnCrossClick:" destination="-1" eventType="touchUpInside" id="iht-kI-Mmo"/>
                            </connections>
                        </button>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="pMD-RO-yOD">
                            <rect key="frame" x="0.0" y="55" width="40" height="40"/>
                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                            <state key="normal" image="unFav.png"/>
                            <state key="selected" image="fav.png"/>
                            <connections>
                                <action selector="btnFavClick:" destination="-1" eventType="touchUpInside" id="eqb-Xw-Zt9"/>
                            </connections>
                        </button>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Ea9-xr-Ix1">
                            <rect key="frame" x="0.0" y="110" width="40" height="40"/>
                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                            <state key="normal" image="rightFlip.png"/>
                            <state key="selected" image="leftFlip.png"/>
                            <connections>
                                <action selector="btnFlipImgClick:" destination="-1" eventType="touchUpInside" id="61x-IC-n4f"/>
                            </connections>
                        </button>
                    </subviews>
                </stackView>
                <view contentMode="scaleToFill" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="88">
                    <rect key="frame" x="0.0" y="696" width="1024" height="72"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <subviews>
                        <imageView userInteractionEnabled="NO" contentMode="scaleToFill" fixedFrame="YES" image="SliderContainer.png" translatesAutoresizingMaskIntoConstraints="NO" id="112">
                            <rect key="frame" x="0.0" y="0.0" width="1024" height="72"/>
                            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        </imageView>
                        <imageView userInteractionEnabled="NO" contentMode="scaleToFill" fixedFrame="YES" image="SliderArea.png" translatesAutoresizingMaskIntoConstraints="NO" id="113">
                            <rect key="frame" x="21" y="12" width="865" height="27"/>
                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                        </imageView>
                        <slider opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" minValue="0.0" maxValue="1" translatesAutoresizingMaskIntoConstraints="NO" id="114">
                            <rect key="frame" x="21" y="12" width="864" height="29"/>
                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                            <color key="minimumTrackTintColor" red="0.74901960779999999" green="0.0" blue="0.1764705882" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <connections>
                                <action selector="TouchupInsideSlider" destination="-1" eventType="touchUpInside" id="117"/>
                                <action selector="valueChangeSliderTime" destination="-1" eventType="valueChanged" id="118"/>
                            </connections>
                        </slider>
                        <imageView userInteractionEnabled="NO" contentMode="scaleToFill" fixedFrame="YES" image="BiteFXLogoSmall.png" translatesAutoresizingMaskIntoConstraints="NO" id="128">
                            <rect key="frame" x="945" y="12" width="74" height="25"/>
                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                        </imageView>
                        <imageView userInteractionEnabled="NO" contentMode="scaleToFill" fixedFrame="YES" image="FrameCounter.png" translatesAutoresizingMaskIntoConstraints="NO" id="263">
                            <rect key="frame" x="893" y="12" width="40" height="28"/>
                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                        </imageView>
                        <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" fixedFrame="YES" text="0" textAlignment="center" lineBreakMode="tailTruncation" minimumFontSize="10" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="264">
                            <rect key="frame" x="895" y="15" width="36" height="21"/>
                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                            <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.0" colorSpace="custom" customColorSpace="sRGB"/>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <color key="highlightedColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <color key="shadowColor" red="1" green="1" blue="1" alpha="0.59999999999999998" colorSpace="custom" customColorSpace="sRGB"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                </view>
            </subviews>
            <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
            <constraints>
                <constraint firstItem="SaB-uq-ugG" firstAttribute="top" secondItem="2" secondAttribute="top" constant="10" id="K3L-x3-53r"/>
                <constraint firstAttribute="trailing" secondItem="SaB-uq-ugG" secondAttribute="trailing" constant="122" id="fQt-N2-BFm"/>
            </constraints>
            <nil key="simulatedStatusBarMetrics"/>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <point key="canvasLocation" x="960.9375" y="-3190.4296875"/>
        </view>
        <view contentMode="scaleToFill" id="8Ps-8h-7LW" userLabel="ViewSkipList">
            <rect key="frame" x="0.0" y="0.0" width="1024" height="768"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <subviews>
                <imageView contentMode="scaleToFill" fixedFrame="YES" image="updates-bg.png" translatesAutoresizingMaskIntoConstraints="NO" id="M8t-j8-i5A">
                    <rect key="frame" x="68" y="28" width="794" height="637"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                </imageView>
                <view contentMode="scaleToFill" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="lqa-tM-pZv">
                    <rect key="frame" x="90" y="104" width="748" height="47"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <subviews>
                        <imageView userInteractionEnabled="NO" contentMode="scaleToFill" fixedFrame="YES" image="Line1.png" translatesAutoresizingMaskIntoConstraints="NO" id="BsT-Xy-28w">
                            <rect key="frame" x="79" y="0.0" width="2" height="47"/>
                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                        </imageView>
                        <imageView userInteractionEnabled="NO" contentMode="scaleToFill" fixedFrame="YES" image="Line1.png" translatesAutoresizingMaskIntoConstraints="NO" id="0aX-lI-dXC">
                            <rect key="frame" x="631" y="0.0" width="2" height="47"/>
                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                        </imageView>
                        <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" fixedFrame="YES" text="Number" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumFontSize="10" translatesAutoresizingMaskIntoConstraints="NO" id="6Z6-Ho-LfJ">
                            <rect key="frame" x="12" y="10" width="56" height="28"/>
                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" fixedFrame="YES" text="Description" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumFontSize="10" translatesAutoresizingMaskIntoConstraints="NO" id="5G9-dD-hEE">
                            <rect key="frame" x="302" y="10" width="142" height="27"/>
                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" fixedFrame="YES" text="Download" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumFontSize="10" translatesAutoresizingMaskIntoConstraints="NO" id="2KM-H3-wBb">
                            <rect key="frame" x="631" y="10" width="117" height="27"/>
                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="highlightedColor"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" red="0.66666666666666663" green="0.66666666666666663" blue="0.66666666666666663" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                </view>
                <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" fixedFrame="YES" text="BiteFX Skip Update" lineBreakMode="tailTruncation" minimumFontSize="10" translatesAutoresizingMaskIntoConstraints="NO" id="8LF-Yz-6aH">
                    <rect key="frame" x="93" y="35" width="435" height="40"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <fontDescription key="fontDescription" name="Arial-BoldMT" family="Arial" pointSize="18"/>
                    <color key="textColor" red="0.54117647059999996" green="0.57254901960000004" blue="0.59999999999999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <nil key="highlightedColor"/>
                </label>
                <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="1S4-FY-aeH">
                    <rect key="frame" x="815" y="42" width="26" height="26"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <fontDescription key="fontDescription" type="boldSystem" pointSize="15"/>
                    <state key="normal" image="X.png">
                        <color key="titleColor" red="0.19607843459999999" green="0.30980393290000002" blue="0.52156865600000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <color key="titleShadowColor" red="0.5" green="0.5" blue="0.5" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <state key="highlighted">
                        <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <connections>
                        <action selector="CancelDownloadUpdate:" destination="-1" eventType="touchUpInside" id="P8D-he-EWN"/>
                        <action selector="cancelViewSkipListClicked:" destination="-1" eventType="touchUpInside" id="7rZ-ez-2Sd"/>
                    </connections>
                </button>
                <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" fixedFrame="YES" text="" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumFontSize="10" translatesAutoresizingMaskIntoConstraints="NO" id="Lki-QV-c4U">
                    <rect key="frame" x="95" y="264" width="748" height="43"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                    <color key="textColor" red="0.01630434783" green="0.01630434783" blue="0.01630434783" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <nil key="highlightedColor"/>
                </label>
                <tableView clipsSubviews="YES" tag="81" contentMode="scaleToFill" fixedFrame="YES" alwaysBounceVertical="YES" style="plain" separatorStyle="default" rowHeight="44" sectionHeaderHeight="22" sectionFooterHeight="22" translatesAutoresizingMaskIntoConstraints="NO" id="n7u-Ys-h0l">
                    <rect key="frame" x="90" y="151" width="748" height="428"/>
                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                    <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <color key="sectionIndexColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <connections>
                        <outlet property="dataSource" destination="-1" id="S5Y-ep-TxW"/>
                        <outlet property="delegate" destination="-1" id="Cww-fd-bOE"/>
                    </connections>
                </tableView>
            </subviews>
            <color key="backgroundColor" red="0.0039215686269999999" green="0.0" blue="0.0" alpha="0.69999999999999996" colorSpace="custom" customColorSpace="sRGB"/>
            <nil key="simulatedStatusBarMetrics"/>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <point key="canvasLocation" x="961" y="-3673"/>
        </view>
        <view contentMode="scaleToFill" id="b83-e2-Mcb" userLabel="ViewTerms">
            <rect key="frame" x="0.0" y="0.0" width="1024" height="768"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <subviews>
                <imageView contentMode="scaleToFill" fixedFrame="YES" image="download-bg.png" translatesAutoresizingMaskIntoConstraints="NO" id="w75-Zm-hyN">
                    <rect key="frame" x="68" y="28" width="794" height="637"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                </imageView>
                <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" fixedFrame="YES" text="Terms and Conditions" lineBreakMode="tailTruncation" minimumFontSize="10" translatesAutoresizingMaskIntoConstraints="NO" id="din-SU-7Ws">
                    <rect key="frame" x="93" y="35" width="435" height="40"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <fontDescription key="fontDescription" name="Arial-BoldMT" family="Arial" pointSize="18"/>
                    <color key="textColor" red="0.54117647059999996" green="0.57254901960000004" blue="0.59999999999999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <nil key="highlightedColor"/>
                </label>
                <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="EOy-g1-0Jc">
                    <rect key="frame" x="815" y="42" width="26" height="26"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <fontDescription key="fontDescription" type="boldSystem" pointSize="15"/>
                    <state key="normal" image="X.png">
                        <color key="titleColor" red="0.19607843459999999" green="0.30980393290000002" blue="0.52156865600000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <color key="titleShadowColor" red="0.5" green="0.5" blue="0.5" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <state key="highlighted">
                        <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <connections>
                        <action selector="onClickPurchaseClose:" destination="-1" eventType="touchUpInside" id="b5B-Lw-Qb8"/>
                    </connections>
                </button>
                <view contentMode="scaleToFill" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Ts9-aJ-yUi">
                    <rect key="frame" x="91" y="100" width="748" height="545"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                </view>
            </subviews>
            <color key="backgroundColor" red="0.0039215686269999999" green="0.0" blue="0.0" alpha="0.69999999999999996" colorSpace="custom" customColorSpace="sRGB"/>
            <nil key="simulatedStatusBarMetrics"/>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <point key="canvasLocation" x="472.65625" y="-1243.359375"/>
        </view>
    </objects>
    <resources>
        <image name="About.png" width="747" height="499"/>
        <image name="AnimationButton.png" width="54" height="30"/>
        <image name="AnimationPressed.png" width="54" height="29.454545974731445"/>
        <image name="BiteFXLogoSmall.png" width="40.363636016845703" height="13.636363983154297"/>
        <image name="DialogNewDownload.png" width="433.09091186523438" height="347.45455932617188"/>
        <image name="FrameCounter.png" width="19.636363983154297" height="15.272727012634277"/>
        <image name="Help.png" width="52.363636016845703" height="28.363636016845703"/>
        <image name="HelpPressed.png" width="52.363636016845703" height="28.363636016845703"/>
        <image name="Info.png" width="52.363636016845703" height="28.363636016845703"/>
        <image name="InfoPressed.png" width="52.363636016845703" height="28.363636016845703"/>
        <image name="Line1.png" width="2" height="48"/>
        <image name="LoopButtonImg.png" width="45.818180084228516" height="32.727272033691406"/>
        <image name="LoopButtonPressedImg.png" width="45.272727966308594" height="31.636363983154297"/>
        <image name="Menu.png" width="52.363636016845703" height="28.363636016845703"/>
        <image name="MenuPressed.png" width="52.363636016845703" height="28.363636016845703"/>
        <image name="NextButtonImg.png" width="45.818180084228516" height="32.727272033691406"/>
        <image name="NextButtonPressedImg.png" width="45.272727966308594" height="31.636363983154297"/>
        <image name="PlayButtonImg.png" width="45.818180084228516" height="32.727272033691406"/>
        <image name="PlayButtonPressed.png" width="45.272727966308594" height="31.636363983154297"/>
        <image name="PreviousButton.png" width="45.818180084228516" height="32.727272033691406"/>
        <image name="PreviousPressed.png" width="45.272727966308594" height="31.636363983154297"/>
        <image name="Register-pressed.PNG" width="48.545455932617188" height="19.636363983154297"/>
        <image name="SliderArea.png" width="471.81817626953125" height="14.727272987365723"/>
        <image name="SliderContainer.png" width="558.54547119140625" height="27.272727966308594"/>
        <image name="X.png" width="14.181818008422852" height="14.181818008422852"/>
        <image name="badge-big.png" width="13.636363983154297" height="9.8181819915771484"/>
        <image name="checkForUpdates.png" width="408.54544067382812" height="19.636363983154297"/>
        <image name="checkForUpdatesPressed.png" width="408.54544067382812" height="19.636363983154297"/>
        <image name="close-download.png" width="15.272727012634277" height="15.272727012634277"/>
        <image name="crossSelected.png" width="72" height="72"/>
        <image name="download-bg.png" width="433.09091186523438" height="347.45455932617188"/>
        <image name="download.png" width="408" height="19.636363983154297"/>
        <image name="downloadPRessed.png" width="408.54544067382812" height="19.636363983154297"/>
        <image name="fav.png" width="72" height="72"/>
        <image name="leftFlip.png" width="72" height="72"/>
        <image name="login-normal.png" width="48.545455932617188" height="19.636363983154297"/>
        <image name="login-pressed.png" width="48.545455932617188" height="19.636363983154297"/>
        <image name="menuPopup.png" width="157" height="304"/>
        <image name="products1.png" width="140" height="30"/>
        <image name="register.png" width="212.72727966308594" height="156"/>
        <image name="resume-download.png" width="52.363636016845703" height="27.272727966308594"/>
        <image name="rightFlip.png" width="72" height="72"/>
        <image name="speed.png" width="48" height="8.7272729873657227"/>
        <image name="speedslidernewimg.png" width="48" height="6"/>
        <image name="toolbar.png" width="51.818180084228516" height="379.6363525390625"/>
        <image name="unFav.png" width="72" height="72"/>
        <image name="updates-bg.png" width="433.09091186523438" height="347.45455932617188"/>
        <systemColor name="groupTableViewBackgroundColor">
            <color red="0.94901960784313721" green="0.94901960784313721" blue="0.96862745098039216" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
    </resources>
</document>
