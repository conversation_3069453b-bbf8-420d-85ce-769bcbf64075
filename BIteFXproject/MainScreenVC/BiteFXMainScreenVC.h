//
//  BiteFXMainScreenVC.h
//  BIteFXproject
//
//  Created by IndiaNIC_08 on 13/12/11.
//  Copyright (c) 2011 __MyCompanyName__. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "PlayerView.h"
#import <AVFoundation/AVFoundation.h>
#import "AnimationPanelVC.h"
#import <QuartzCore/CoreAnimation.h>
#import "AppDelegate.h"
#import "JSON.h"
#import "CallWebService.h"
#import <AssetsLibrary/AssetsLibrary.h>
//#import "FTPUploadDownload.h"
#import "DownloadFile.h"
#import "UITableViewCell+NIB.h"
#import "UpdateCell.h"
#import "DownloadHelper.h"
#import "DownloadManager.h"
#import <StoreKit/StoreKit.h>
#import "PurchaseOptionCustomCell.h"
#import "InAppPurchase.h"
#import "IAPManager.h"
#import "iCloudDoc.h"
#import "InfoWebView.h"
#import <MessageUI/MessageUI.h>
#import "VideoPlay.h"
#import <StoreKit/StoreKit.h>
#import <SVProgressHUD.h>
#import <WebKit/WebKit.h>
#import <CoreImage/CoreImage.h>

#define kInAppPurchaseManagerProductsFetchedNotification @"kInAppPurchaseManagerProductsFetchedNotification"
// add a couple notifications sent out when the transaction completes
#define kInAppPurchaseManagerTransactionFailedNotification @"kInAppPurchaseManagerTransactionFailedNotification"
#define kInAppPurchaseManagerTransactionSucceededNotification @"kInAppPurchaseManagerTransactionSucceededNotification"

@class CustomisePopOverVC;
@class AnimationPanelVC;

@interface BiteFXMainScreenVC : UIViewController<UIWebViewDelegate,UIPopoverControllerDelegate,UIGestureRecognizerDelegate,AVAudioPlayerDelegate,CallWebServiceDelegate,UITextFieldDelegate,UITextViewDelegate,UIAlertViewDelegate,UINavigationBarDelegate,progressBar,UITableViewDataSource,UITableViewDelegate,DownloadHelperDelegate, PurchaseOptionCustomCellDelegate, InAppPurchaseDelegate,ICloudProtocol, DownloadManagerDelegate,MFMailComposeViewControllerDelegate,CAAnimationDelegate, VideoPlay, SKRequestDelegate, WKNavigationDelegate> {
    
    IBOutlet UIImageView *img_SliderArea;
    Reachability* hostReach;
    Reachability* internetReach;
    Reachability* wifiReach;
    DownloadManager *downloadManager;

    IBOutlet UIImageView *imgview_fullImage;
    IBOutlet PlayerView *m_playerView;
    IBOutlet UIView *SliderView;
    UILabel *lbl_view;
    UILabel *lbl_view_title;
    AVPlayer *playerVideoplay;
    AVQueuePlayer *m_queueplayer;
    IBOutlet UISlider *sliderTimeForVideo;
    UIView *m_indicaterView;
    float lastScrollValue;
    BOOL isPlaying;

    //Vertical Toolbar View
    IBOutlet UIView *verticalToolbarView;
    IBOutlet UIImageView *imgMenuUpdates;
    IBOutlet UIButton *btnNextFrame;
    IBOutlet UIButton *btnPreviousFrame;
    IBOutlet UIButton *btnHelp;
    IBOutlet UIButton *btnMenu;
    IBOutlet UIButton *btnUpdate;
    IBOutlet UIButton *btnInfo;
    IBOutlet UIButton *playPauseBtn;
    InfoWebView *wbView;
    CustomisePopOverVC *customPopoverController;
    AnimationPanelVC *animation;
    UISwipeGestureRecognizer *recognizer1;
    UISwipeGestureRecognizer *recognizer2,*recognizer3,*recognizer4;
    NSMutableArray *videoUrlArr;
    IBOutlet UIButton *loopBtn;
    BOOL isSlidertouchedDuringPlaying;
    IBOutlet UISlider *sliderSpeedControl;
    IBOutlet UIButton *btnAnimationPanel;
    
    //slider images and speed control images
    IBOutlet UIButton *btn_SpeedControl;
    
    IBOutlet UIButton *btnHelpView;
    IBOutlet UIButton *btnHelpClose;

    IBOutlet UIImageView *imgFramecounter;
    IBOutlet UILabel *lblFramcounter;

    
    //menu Views;
    IBOutlet UIView *viewPopup;
    IBOutlet UIImageView *imgUpdatesAvailable;
    IBOutlet UILabel *lblUpdatesavailable;
    IBOutlet UILabel *lblMenuUpdates;
    IBOutlet UIView *viewAboutBitefx;
    IBOutlet UIView *viewRegistration;
    IBOutlet UIView *viewPurchase;
    IBOutlet UIView *viewUpdates;

    IBOutlet UIImageView *imgAboutBiteFX;
    
    //Update view buttons;
    IBOutlet UIButton *btnCheckUpdate;
    IBOutlet UIButton *btnDownloadUpdate;
    IBOutlet UILabel *lblMain;
    IBOutlet UILabel *lblSubtitle;
    IBOutlet UILabel *lblVersion;
    
    //Registration  view
    BOOL isRegistered;
    BOOL isSubscribed;
    IBOutlet UITextField *txtFieldEmail;
    IBOutlet UITextField *txtFieldSerialnumber1;
    
    IBOutlet UIButton *btnRegister;
    IBOutlet UIButton *BtnRegistration;
    NSString *strSerialNumber;
    NSString *strEmail;
    
    IBOutlet UIButton *btnUninstall;
    
    //aboutBiteFX view
    IBOutlet UILabel *lblLicenceEmail;
    IBOutlet UILabel *lblAppVersion;

    int intCount;
    float floatSpeedMultiplier;
    int intDownloadCount;
    int intMaxDownloadCnt;
    
    NSMutableArray *mutArrUpdateDownload;
    NSInteger intcountofVideo;
    int intInitialCount;//unused
    IBOutlet UIView *view_Back;
    NSMutableArray *arrPlayerItems;
    NSString *selectedVideoToPlayOnFullScreen;
    
    NSInteger currentIndex;
    BOOL isnextClicked;
    BOOL isPreviousClicked;
    BOOL isinfoHtmlLoaded;
    BOOL isanimationFinished;
    
    AVAsset *asset_;
    NSTimer *timerFrameCounter;
    float floatCount;
    float Speed;
    int currentFrame;
    BOOL isPlayerfinished;
    
    BOOL isUpdateDownloaded;
    
    UIActivityIndicatorView *activityIndiForDownload;
    BOOL isUpdateDownloadedFromServer;
    BOOL isRegistrationSuccess;
    BOOL isFullVersionDownloaded;
    IBOutlet UIProgressView *progressBar;
    int FullorUpdate;
    BOOL isAlreadyDownloaded;

    IBOutlet UIView *viewFortableHeader;
    //FTPDownload class object
//    FTPUploadDownload *FTPObject;
    IBOutlet UILabel *lblProgresspercent;
    IBOutlet UIView *viewDownloadingData;
    IBOutlet UIButton *btnRemainDownload;
    
    //downloadingprogress view;
    IBOutlet UILabel *lblfullorupdate;
    BOOL success;
    BOOL UPDAtesComplted;
    IBOutlet UILabel *lblDownloadMsg;
    IBOutlet UITableView *tblViewUpdates;
    
    //Code start
    IBOutlet UITableView *tblViewSkipUpdate;
    IBOutlet UIView *viewSkipList;
    //Code End

    NSURL *webViewInfoURL;
    NSMutableURLRequest *webViewInfoRequest;
    
    
    NSMutableArray *MutArrUpList;
    NSMutableArray *ArrUpdatedownload;
    int intVideoNumberCounter;
    int intVideoCounter;
    IBOutlet UILabel *lblNoofVideoCompleted;
    NSMutableArray *mutArrFullVersion;
    NSMutableArray *arrFileDownloadList;
    int intUpdateCounter;
    int intHtmlFileCounter;
    int intJpgFileCounter;
    
    IBOutlet UILabel *lblThanksRegistration;
    IBOutlet UILabel *lblNoupDates;
    BOOL isUpdatesChecked;
    BOOL isHtmlDownload;
    BOOL isJpgDownoad;
    BOOL isUpdatedHtmldownloading;
    BOOL isUpdatedJpgDownloading;
    int inthtmlUpdateCounter;
    int intJpgUpdateCounter;
    
    //To checking that full-version downloaded or updates downloaded or user has unregistered.
    
    int intFullversiondownloadCompleted;
    int intUpdatesdownloadCompleted;
    int intUnregisterDone;
    int intupdate;
    
    BOOL isUpdateparsing2Done;
    BOOL isMviDownloading;
    BOOL UpdateCompleted;
    BOOL downloadingmviStarted;
    BOOL isFullVersionXML;

    NSMutableArray *arrMviFile;
    NSMutableArray *mutArrTotalmviFiles;

    int intMviCounter;
    int intParsingCounter;
    int intnumberOfMviFiles;
    int intVersionMVIFIles;
    int intVersiontoCheck;

    NSString *aStrMviFile;
    NSString *strFullPath;
    
    UserSubscribeStatus userSubStat;
    
    // In - App Purchase
    SKProduct *proUpgradeProduct;
    SKProductsRequest *productsRequest;
    NSString *strInAppID;
    
    
    IBOutlet UITableView *tblViewPurchaseOption;
    IBOutlet UIButton *btnPurchase;
    
    SKPaymentTransaction *skPaymentTrasCurrent;
    
    //===================
    // Version 3.0 Changes
    //===================
    // UIWebView is replaced with WKWebView.
//    IBOutlet UIWebView *webViewPurchaseDetails;
    IBOutlet UIView *viewPurchaseDetails;
    IBOutlet UIView *viewSubscriptionOptions;
    WKWebView *webViewPurchaseDetails;
    NSString *str1MonthPrice;
    NSString *str6MonthsPrice;
    NSString *str1YearPrice;
    
    IBOutlet UIButton *btnProducts;
    IBOutlet UIButton *btnRestore;
    IBOutlet UILabel *lblUpdatesAvailble;
    IBOutlet UIButton *btnSkip;
    
    
    IBOutlet UILabel *lblPurchaseOptions;
    IBOutlet UIView *viewAboutSubscription;
    IBOutlet UIView *viewAboutBasic;
    IBOutlet UILabel *lblAboutBasic;
    IBOutlet UIView *viewAboutLogin;
    
    NSString *strCurrentDate;
    int isUpdateInterrupted;
    int intSkipVideoCount;
    BOOL isManageArrayList,shouldStartDownload;
    
    BOOL isImageDisplay;
    InAppPurchase *inAppPurchase;
    
    NSDateFormatter *dateFormatter;
    UIButton *btnHideUnhideImages;

    UIAlertController *alertForConfirmation;
    UIAlertController *alertForUpdateMessage;
    
    BOOL isNewVersionDataDownloading;
    BOOL isPreserveUserDefineSequences;
    
    IBOutlet UIButton *btnFavUnfav;
    IBOutlet UIButton *btnFlip;
}

@property (nonatomic,strong) NSMutableArray *mutArrSkipVideoList;
@property (nonatomic,strong) NSMutableArray *mutArrSkipDownloadList;
@property (nonatomic,strong) DownloadManager *downloadManager;
@property (nonatomic,strong) UITextField *txtFieldEmail;
@property (nonatomic,strong) NSString *strEmail;
//@property (nonatomic,readwrite)int aCount;
@property (nonatomic,strong) IBOutlet PlayerView *m_playerView;
@property (nonatomic,strong) AVQueuePlayer *m_queueplayer;
@property (nonatomic,strong) NSMutableArray *videoUrlArr;
@property (nonatomic,strong) NSString *strSerialNumber;
@property (nonatomic,strong) NSMutableArray *arrMviFile;
@property (nonatomic,strong) NSMutableArray *mutArrTotalmviFiles;
@property (nonatomic, strong) NSMutableArray *mutArrUpdateDownload;
@property (nonatomic,strong) NSMutableArray *arrPlayerItems;
@property (nonatomic,strong) IBOutlet UISlider *sliderSpeedControl;
@property (nonatomic,strong) AVPlayer *playerVideoplay;
@property (nonatomic,strong) NSMutableArray *ArrUpdatedownload;
@property (nonatomic,strong) NSMutableArray *MutArrUpList;
@property (nonatomic,strong) NSMutableArray *mutArrFullVersion;
@property (nonatomic,strong) NSMutableDictionary *mutDictFullVersion;
@property (nonatomic,strong) NSString *aStrMviFile;
@property (nonatomic,strong) NSString *strFullPath;

@property (nonatomic,strong) UIButton *btnPurchase1Month;
@property (nonatomic,strong) UIButton *btnPurchase6Month;
@property (nonatomic,strong) UIButton *btnPurchase1Year;

@property (nonatomic,strong) SKProduct *product1Month;
@property (nonatomic,strong) SKProduct *product6Month;
@property (nonatomic,strong) SKProduct *product1Year;

@property (nonatomic, assign) CMTimeScale mediaTimeScale;

- (IBAction)btnUninstallUpdatesClicked:(id)sender;
- (IBAction)btnSkipClicked:(id)sender;
- (IBAction)cancelViewSkipListClicked:(id)sender;


-(void)showTimeOnScroll:(float)time;
-(void)hideFrameButton;
-(IBAction)clickNextFrame;

-(IBAction) clickPreviousFrame;
-(IBAction)valueChangeSliderTime;
-(IBAction)TouchupInsideSlider;
-(IBAction)play:(id)sender;
-(IBAction)info:(id)sender;
-(IBAction)help:(id)sender;
-(IBAction)menu:(id)sender;
-(IBAction)btnAnimationclick:(id)sender;

-(void)animation;
-(void)manageUiEvents;
-(BOOL) EmailValidation:(NSString *) testEmail;

//SpeedControlSlider
-(IBAction)valueChanged:(id)sender;

-(IBAction)LoopBtnAction:(id)sender;

//Help clicked
-(IBAction)btnHelpClicked:(UIButton*)sender;
//-(void)callDownloadWebservice;
- (void)callWSForCurrentDate;

// check Update method

-(void)callUpdateCheckWebservice:(id)sender;

//Down load updates
-(IBAction)CancelDownloadUpdate:(id)sender;
- (IBAction)btnDownloadUpdate:(id)sender;

//Menu popup actions
-(IBAction)PurchaseAction:(id)sender;
-(IBAction)RegisterAction:(id)sender;
-(IBAction)updatesAction:(id)sender;
-(IBAction)Aboutaction:(id)sender;
-(IBAction)btnCheckForRamain:(id)sender;

-(IBAction)btnRegisterAction:(id)sender;

-(IBAction)CancelRegistrationView:(id)sender;

-(IBAction)btnStopDownload:(id)sender;
//Register Action

-(IBAction)btnManageSubscription:(id)sender;

//Updates Action

//  Database Methods

- (void)insertNewFileWithList:(NSMutableArray *)aMutArrFiles;

- (void)getDataAboutUserFromDatabase;

- (void)insertUpdateCount:(int)aIntCount;

- (void)insertUpdateList:(NSMutableDictionary *)aMutDictList;

- (void)insertUpdateFiles:(NSMutableDictionary *)aMutDictFiles forVersion:(int)aIntVers;

@property (NS_NONATOMIC_IOSONLY, getter=getUpdateList, readonly, copy) NSMutableArray *updateList;

- (void)getDownloadUpdateList;

// Check Interval for Update

- (void)checkForIntervalUpdate;


// Get Maximum Version

@property (NS_NONATOMIC_IOSONLY, getter=getVersionToCheckUpdate, readonly) int versionToCheckUpdate;

//- (void)startDownloadUpdate;

// Save Video With Data

//- (void)saveVideoWithData:(NSData *)aDataFile;

//- (void)saveInfoFileToSystem;

//play video at index
-(void) playVideoAtIndex:(NSInteger)index;

// XML Parsing

- (void)startXMLParsing;
-(void)startXMLUpdatedParsing;
- (void)insertPlayListCollection;

// Delete Playlist Data Method

- (void)deletePlaylistData;


//info html method

-(NSString*)getHtmlString:(NSString*)aStrhtml;

// To get First Collection Video Details

- (void)getFirstCollectionVideoDetail;

//ZipunZip 

//-(void)unzipfile;

-(void)RegistrationMethodCalled;
//-(void) saveVideoInBGForData:(NSMutableData *)response_data;


//This function will remove all the downloaded data from document directory.
-(void)RemoveFromDocumentDirectory;

//Checking updates webservice
// Not in use...
//-(void)callingCheckingService;

-(void)callUpdateCheckForInAppWebservice:(id)sender;


//Full-Version Downloading
-(void)FullVersionDownloading:(NSString*)aStr;

-(void)downloadingUpdatedHtml;
-(void)downloadingUpdatedJpg;
-(void)ReceiveJpgdata:(NSData*)theData;

-(void)ReceiveHtmldata:(NSData*)theData;

//-(NSString*)filePathforVideo:(NSString*)aStr;

//Checking updateCounter

-(void)CheckingUpdates;

//Called when user has successfully registered and downloading will start.
-(void)StartDownloading;

//This will used for downloading mvi files from server.
-(void)DownloadingMviFiles;
-(void)DownloadFullVersionMVI;
@property (NS_NONATOMIC_IOSONLY, readonly) BOOL checkForRemainDownloadOnNetwork;

//-(void)subscriptionDetail;
-(void)subscribeProduct;

- (IBAction)onClickPurchaseClose:(id)sender;
- (void)callWSforInAppVarification;
- (void)callWSforInAppVarification:(NSString *)aStrReceipt;
-(void)inAppStart:(NSString*)aStrProductId ForPrice:(BOOL)aBoolForPrice;
- (void)callCompleteTransactoionAfterCompletion;
- (IBAction)onClickProducts:(id)sender;
- (IBAction)onClickRestore:(id)sender;

//Do not backupAttribset
-(void)setAttribDonotMarkiCloudSetting:(NSString*)str;
- (NSInteger) daysToDate:(NSString *) endDate;
- (void)checkForAvaibility;
- (NSString *)dateFromDate:(NSString *)date afterNumberOfDays:(int)aInt  isAfter:(BOOL)aBoolIsAfter;
- (NSDate *)dateFromDate:(NSDate *)date afterNumberOfDays:(int)aInt;

// iCloud Methods
- (void)moveToBasicVersion;
- (void)saveDBForInApp;
- (IBAction)onClickCheckForUpdate:(id)sender;

// Version 3.0 change
// New About Popup changes...
- (IBAction)btnTermsnPrivacyClick:(id)sender;

// Version 3.1.* change
- (IBAction)btnFavClick:(UIButton*)sender;
- (IBAction)btnCrossClick:(UIButton*)sender;
- (IBAction)btnFlipImgClick:(UIButton*)sender;

@end
