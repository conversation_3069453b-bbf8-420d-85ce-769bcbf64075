//
//  AppConstant.h
//  BIteFXproject
//
//  Created by indianic on 6/12/17.
//
//

#ifndef AppConstant_h
#define AppConstant_h

#define APP_NAME @"BiteFX"

#define SOURCE_TYPE_CAMERA 1
#define SOURCE_TYPE_PHOTO_LIBRARY 2

#define DOCUMENT_DIRECTORY_PATH [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) objectAtIndex:0]
#define BITEFXV2        @"BiteFXV2"
#define MOVIES          @"Movies"
#define IMAGES          @"Pictures"
#define PRESENTATIONS   @"Presentations"
#define THUMBNAILS      @"Thumbnails"

#define mediaMovie @"MOVIE"
#define mediaImage @"IMAGE"

// New change for h265 videos.
#define VIDEO_EXTENSION @"mp4"

#define mediaPresentation @"PRESENTATION"

#define PRESENTATION_INFO_VIEW_WIDTH 324

#define IS_NEW_DATA_DOWNLOADED_FOR_VERSION_24            @"IsNewDataDownloadedForVersion24"
#define IS_OLD_UPDATES_DATA_DELETED_FOR_VERSION_25       @"IsOldUpdatesDataDeletedForVersion25"

// App messages...
#define IMAGE_ALREADY_ADDED                      @"You have already added this image."
#define ENTER_IMAGE_ALBUM_NAME                   @"Please enter image album name."
#define INVALID_IMAGE_ALBUM_NAME                 @"Image album name should not contain \"."
#define INVALID_IMAGE_ALBUM_PICTURES_HIDDEN      @"Image album name may not contain \"Pictures hidden\"."
#define DELETE_IMAGE_ALBUM_CONFIRMATION          @"Are you sure you want to remove this album from BiteFX? (This will not affect the original pictures imported to BiteFX.)"
#define DELETE_IMAGE_CONFIRMATION                @"Are you sure you want to remove this picture from BiteFX? (This will not affect the original picture imported to BiteFX.)"
#define LOGIN_SUCCESS_MESSAGE                    @"Subscription confirmed. Please stand by while the files are preparing to download."
#define UPDATE_SUCCESS_MESSAGE                   @"Updating BiteFX database. This may take a while. Please wait!"
#define LOGIN_USER_SUBSCRPTION_REMAINING_DAYS    @"Your BiteFX subscription will expire in %d days. \nBiteFX will revert to its basic (free) form with only 6 animations if your subscription expires. \nPlease contact BiteFX immediately to ensure your subscription does not expire."
#define LOGIN_USER_SUBSCRPTION_EXPIRED           @"Your BiteFX subscription has expired. \nBiteFX will revert to its basic (free) form. \nPlease contact BiteFX immediately to renew your subscription."
#define INAPP_USER_SUBSCRPTION_REMAINING_DAYS    @"Your BiteFX subscription will expire in %d days. \nBiteFX will revert to its basic (free) form with only 6 animations if your subscription expires."
#define INAPP_USER_SUBSCRPTION_EXPIRED           @"Your BiteFX subscription has expired. \nBiteFX will revert to its basic (free) form."
#define APP_NEW_VERSION_AVAILABLE                @"An upgrade is available from the App Store. No further animation (and other) updates will be provided with this version. Please go to the App Store, search for BiteFX and install the new version of BiteFX."

#define SUBSCRIPTION_GRACE_PERIOD_MESSAGE        @"App Store has been unable to collect your latest payment. You have just 16 days from your payment date to correct this before your subscription expires."

#define SOME_THING_WENT_WRONG_ERROR              @"Something went wrong, please contact BiteFX."
#define REFRESH_RECEIPT_ERROR                    @"Unable to get the data. Please check internet connection and try again later."
#define TRANSACTION_FAIL_ERROR                   @"Error occurs in purchasing product. Please try again later."
#define RESTORE_FAIL_ERROR                       @"Error occurs in restoring product. Please try again later."

#define ABOUT_BASIC_VERSION                      @"This is the free demo version of BiteFX.\nIt contains just a few sample animations, pictures and one example presentation template.\nThe full version contains over 140 animations, 140 pictures, and several presentation templates.\n\nYou can upgrade to the full version by:\n"

#define PAY_AS_YOU_GO_TITLE                      @"Special Introductory Offer"
#define PAY_UP_FRONT_TITLE                       @"Special Introductory Offer"
#define FREE_TRIAL_TITLE                         @"Free Trial Offer (Only Available for a Limited Time)"
#define PAY_AS_YOU_GO_DESCRIPTION                @"BiteFX on iPad first %@ month(s) for %@ per month. At end of the introductory period, monthly subscription will be the regular price of %@ per month (unless you cancel)."
#define PAY_UP_FRONT_DESCRIPTION                 @"BiteFX on iPad first %@ month(s) for %@. At end of the introductory period, you will be charged a monthly subscription of the regular price of %@ per month (unless you cancel)."
#define FREE_TRIAL_DESCRIPTION                   @"BiteFX on iPad free trial of %@ month(s). At end of the trial period, you will be charged a monthly subscription of the regular price of %@ per month (unless you cancel)."

#define LOGIN                               @"loginV7"
#define UPDATESLIST                         @"updatesListV7"
#define CHECK_LOGIN_SUBSCRIPTION_STATUS     @"checkLoginSubscriptionStatusV7"
#define VERIFY_PURCHASE                     @"verifyPurchaseV7"
#define VERIFY_FOR_UPDATESLIST              @"verifyForUpdatesListV7"
#define CHECK_SUBSCRIPTION_STATUS           @"checkSubscriptionStatusV7"
#define VERIFY_ELIGIBILITY                  @"verifyEligibilityV7"

#define UD_KEY_ISINTRODUCT_ELIGIBLE         @"UD_KEY_ISINTRODUCT_ELIGIBLE"

#define MANAGE_SUBSCRPTION_URL              @"https://apps.apple.com/account/subscriptions"

// Notification names.
#define REFRESH_ANIMATIOM_PANEL_DATA        @"RefreshAnimationPanelData"

//===================
// Version 3.1 Changes
//===================
// Feature MVI file changes.
#define MVI_FILE_URL                        @"MVIFileURL"
#define FEATURES_MVI_FILE_URL               @"FeaturesMVIFileURL"
#define FEATURESETIMAGES                    @"FeatureSetImages"
#define SELECTED_FEATURE_SET_ID             @"SelectedFeatureSetID"

#define COLLECTION_NUMBER @"currentUserDefinedImageCollectionNumber"

typedef NS_ENUM(unsigned int, UserSubscribeStatus) {
    
    NotSubscribed = 0,
    InAppSubscribed,
    Subscribed,
};


#endif /* AppConstant_h */
