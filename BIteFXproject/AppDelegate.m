//
//  AppDelegate.m
//  BIteFXproject
//
//  Created by IndiaNIC_08 on 13/12/11.
//  Copyright (c) 2011 __MyCompanyName__. All rights reserved.
//

#import "AppDelegate.h"
#import "Reachability.h"
#import "BiteFXMainScreenVC.h"
#import "NetworkReachability.h"
#include <sys/xattr.h>
#import "Database.h"
#import "ICloud.h"
#import "UIDevice+IdentifierAddition.h"
#import "IAPManager.h"
@import StoreKit;

#define strFileName @"BITEFXDatabase.sqlite"

#import <mach/mach.h>
#import <mach/mach_host.h>
#define btnHideImage @"btnHide.png"
#define btnUnHideImage @"btnUnHide.png"
@implementation AppDelegate

@synthesize window = _window;
@synthesize viewController = _viewController;
@synthesize mutArrPlayVideo;
//@synthesize intSelectedVideo;
@synthesize intSelectedScrNo;
@synthesize boolSelectedPlaylist;
@synthesize isFirstScrollView;
@synthesize isUpdatedMVIfile;
@synthesize isRegistrationSuccess;
@synthesize isUpdateDownloading;
@synthesize isFullversionDownloading;
@synthesize isDownloadCancelled;
@synthesize arrlastCollectionRow;
@synthesize isFullZipcompleted;
@synthesize isUpdateZipcompleted;
@synthesize arrScrollViewCount;
@synthesize isUnregisteredDone;
@synthesize strDeviceId;
@synthesize wasDownloadinProgress;
@synthesize isChangedinPlayList;
@synthesize mutArrDataItem;
@synthesize isiCloudAvailble;
@synthesize intStatusCheckForiCloud;
@synthesize isSkipVideoFullVerison,isSkipVideoUpdateVerison,isDownloadManually;
@synthesize arrUserDefineSeq;
@synthesize intlastResizeViewYAxis;
@synthesize intLastScrollIndicatorHeight;
@synthesize intTabViewIndexPath;
//@synthesize isUpdateBtnClicked;

//@synthesize intUpdatesdownloadCompleted;

#pragma mark - detection device methods
- (NSString *) platform
{
    size_t size;
    sysctlbyname("hw.machine", NULL, &size, NULL, 0);
    char *machine = malloc(size);
    sysctlbyname("hw.machine", machine, &size, NULL, 0);
    NSString *platform = @(machine);
    free(machine);
    return platform;
}

#pragma mark -- memory test methods
+(NSArray*) print_free_memory {
    mach_port_t host_port;
    mach_msg_type_number_t host_size;
    vm_size_t pagesize;
    
    host_port = mach_host_self();
    host_size = sizeof(vm_statistics_data_t) / sizeof(integer_t);
    host_page_size(host_port, &pagesize);
    
    vm_statistics_data_t vm_stat;
    
    if (host_statistics(host_port, HOST_VM_INFO, (host_info_t)&vm_stat, &host_size) != KERN_SUCCESS)
        NSLog(@"Failed to fetch vm statistics");
    
    /* Stats in bytes */
    natural_t mem_used = (vm_stat.active_count +
                          vm_stat.inactive_count +
                          vm_stat.wire_count) * (natural_t)pagesize;
    natural_t mem_free = vm_stat.free_count * (natural_t)pagesize;
    natural_t mem_total = mem_used + mem_free;
    
    NSLog(@"used: %u free: %u total: %u", mem_used, mem_free, mem_total);
    
    NSArray *Arr=@[[NSNumber numberWithDouble:mem_used],[NSNumber numberWithDouble:mem_free],[NSNumber numberWithDouble:mem_total]];
    return Arr;
}

- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions {
//    NSString *aStrUpdadte2 = [NSString stringWithFormat:@"update FileDownloadList set IsDownloaded = 0 where serial = 156"];
//    [[Database sharedDatabase] Update:aStrUpdadte2];

    self.mutArrDataItem = [[NSMutableArray alloc] init];
    [[NSUserDefaults standardUserDefaults]setBool:YES forKey:@"PlayAnimation"];
    [[NSUserDefaults standardUserDefaults]synchronize];

    _selectedModeSize = 1;
    appDelegate = (AppDelegate*)[UIApplication sharedApplication].delegate;
    AppDelegateobj = (AppDelegate*)[UIApplication sharedApplication].delegate;

    self.window = [[UIWindow alloc] initWithFrame:[UIScreen mainScreen].bounds];
    application.idleTimerDisabled = NO;

    _intSelectedTab = 0;
    _intPrevSelectedTab = 0;
    // Override point for customization after application launch.
    [self awakeFromNib];
    [self addNewTablesForNewVersion];

    [AppDelegate listOfRemainFileDownload];

    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES); 
    NSString *documentDirectory = paths[0]; // Get Library folder  
    NSLog(@"Document directory %@",documentDirectory);
    intStatusCheckForiCloud = 0;
    
    NSURL *pathURL= [NSURL fileURLWithPath:documentDirectory];
    BOOL attSet=[self addSkipBackupAttributeToItemAtURL:pathURL];
    if (attSet) 
    {
        //NSLog(@"Done");
    }
    isUnregisteredDone = FALSE;

    isChangedinPlayList = FALSE;
    
    strDeviceId = [[UIDevice currentDevice]uniqueGlobalDeviceIdentifier];
    //NSLog(@"DeviceID=%@",strDeviceId);
    [[NSUserDefaults standardUserDefaults]setObject:strDeviceId forKey:@"DeviceId"];
    [[NSUserDefaults standardUserDefaults]synchronize];
    
//    [[UIApplication sharedApplication]setStatusBarOrientation:UIInterfaceOrientationLandscapeRight];

    
    UIInterfaceOrientation interfaceOrientation = [UIApplication sharedApplication].statusBarOrientation;
    
    if ([UIDevice currentDevice].systemVersion.floatValue>8.0) {
        
        if (interfaceOrientation == UIInterfaceOrientationLandscapeLeft)
        {
            // AppDel.isBookListLandscape = YES;
            (self.window).frame = CGRectMake(0,0, self.window.frame.size.width, self.window.frame.size.height);
        }
        else  if (interfaceOrientation == UIInterfaceOrientationLandscapeRight)
        {
            // AppDel.isBookListLandscape = YES;
            (self.window).frame = CGRectMake(0,0, self.window.frame.size.width, self.window.frame.size.height);
        }
        
    }

    self.mutArrPlayVideo = [NSMutableArray arrayWithCapacity:0];
    self.arrScrollViewCount = [NSMutableArray arrayWithCapacity:0];
    self.arrlastCollectionRow = [NSMutableArray arrayWithCapacity:0];
    self.arrUserDefineSeq = [NSMutableArray arrayWithCapacity:0];

    [[NSUserDefaults standardUserDefaults] setObject:@"1" forKey:@"FirstTimeVideo"];
    [[NSUserDefaults standardUserDefaults]setBool:NO forKey:@"FullDownloaded"];
    [[NSUserDefaults standardUserDefaults] setBool:YES forKey:@"isLocked"];
    [[NSUserDefaults standardUserDefaults] synchronize];
    
    self.viewController = [[BiteFXMainScreenVC alloc] initWithNibName:@"BiteFXMainScreenVC" bundle:nil];
    self.window.rootViewController = self.viewController;
    [self.window makeKeyAndVisible];
    
    NSURL *ubiq = [[NSFileManager defaultManager] URLForUbiquityContainerIdentifier:nil];
    
    if (ubiq) {
        isiCloudAvailble = YES;
    } else {
        isiCloudAvailble = NO;
    }
    
    [[NSUserDefaults standardUserDefaults] setBool:NO forKey:@"Launch Bitefx"];
    [[NSUserDefaults standardUserDefaults] synchronize];
    
    //For database Changes for Downloaded Users
    if(![[NSUserDefaults standardUserDefaults] boolForKey:@"isUpdatedFirstTime"])
    {
        NSString *aTempDelStrFileUpdateList = @"Delete from FileUpdateList";
        [[Database sharedDatabase] Delete:aTempDelStrFileUpdateList];
        
        NSString *aTempDelStrUpdatesList = @"Delete from UpdatesList";
        [[Database sharedDatabase] Delete:aTempDelStrUpdatesList];
        
        [[NSUserDefaults standardUserDefaults] setBool:YES forKey:@"isUpdatedFirstTime"];
    }
    if (    [UIDevice currentDevice].systemVersion.floatValue>8.0) {
//        [[UIApplication sharedApplication] setStatusBarHidden:YES ];
        application.statusBarHidden = YES;

    }
    
    //===================
    // Version 2.4 Changes
    //===================

    [AppDelegate createFoldersInDocumentDirectoryToSaveAppData];
    [self addNewColumnsInDB];
    [self copyFile];
    [self copyUpdateFile];
    
    //===================
    // Version 3.1 Changes
    //===================
    // Feature MVI file changes.
    [self createTableForFeatures];
    // Get FeatureID...
    self.intSelectedFeatureId = [[NSUserDefaults standardUserDefaults] integerForKey:SELECTED_FEATURE_SET_ID];

    _btnHideUnhideImages = [[UIButton alloc] init];
    //    _btnHideUnhideImages = [UIButton buttonWithType:UIButtonTypeCustom];
//    _btnHideUnhideImages.frame = CGRectMake(850,702,75,44);
    // Version 3.0 Changes
    // New frame change for Small, Medium and Large buttons...
    _btnHideUnhideImages.frame = CGRectMake(725,702,75,44);
    [_btnHideUnhideImages setImage:[UIImage imageNamed:btnUnHideImage] forState:UIControlStateNormal];
    [_btnHideUnhideImages setImage:[UIImage imageNamed:btnHideImage] forState:UIControlStateSelected];
    [_btnHideUnhideImages setSelected:YES];
    //    [_btnHideUnhideImages setUserInteractionEnabled:NO];
    //    [_btnHideUnhideImages setAlpha:0.4];
    
    // This is used to show selected image, video and presentation from FullScreenMode to SelectionPanel...
    self.isScrollToSelectedIndex = YES;

    // Version 2.5 change support for app upgrade with updates list...
    self.isUpgradeAvailable = NO;
    
    // Version 3.0 change - SVProgressHUD is added...
    [SVProgressHUD setDefaultMaskType:SVProgressHUDMaskTypeClear];
    
    // Version 3.0 change Attach an observer to the payment queue.
    [[SKPaymentQueue defaultQueue] addTransactionObserver:[IAPManager sharedInstance]];
    
    // Set up next available custom image group sequence number
    [self setUpImageGroupSequenceNumber];

    return YES;
}

- (void)setUpImageGroupSequenceNumber {
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    NSInteger currentValue = [defaults integerForKey:COLLECTION_NUMBER];
    
    // It will only be zero first time through, signalling that it has never been set. Subsequent passes will be non-zero, so this code will only run once.
    if (currentValue == 0) {
        NSLog(@"Custom image sequence number not found. Creating in app defaults.");
        
        // See how many custom image groups currently exist
        NSString *aStrImageGroupName = [NSString stringWithFormat:@"select collectionID,collectionDisplayName from CollectionMaster where fileType = \'%@\' AND isUserDefine = '1'", mediaImage];
        NSMutableArray *aMutArrImageGroups = [[Database sharedDatabase] getAllDataForQuery:aStrImageGroupName];
        NSInteger groupCount = [aMutArrImageGroups count];
        
        // See if any of the groups are prepended with #nn and if they are, use nn+1 as our starting point
        NSInteger maxNumber = groupCount;
        for (NSDictionary *entry in aMutArrImageGroups) {
            NSString *displayName = entry[@"collectionDisplayName"];
            // If the displayName starts with #nn where nn is an integer
            if ([displayName hasPrefix:@"#"] && displayName.length > 1) {
                NSInteger number = [[displayName substringFromIndex:1] integerValue];
                // If nn is larger than groupCount, update maxNumber
                if (number > groupCount) {
                    maxNumber = number;
                }
            }
        }
        
        // Save next available number
        [defaults setInteger:maxNumber+1 forKey:COLLECTION_NUMBER];
        [defaults synchronize]; // Write it out immediately, not later.
    }
}

-(CGSize) getObjectFrame1
{
    if(appDelegate.selectedModeSize == 2)
    {
        return CGSizeMake(60, 58);
//        return CGSizeMake(70, 68);
    }
    else if(appDelegate.selectedModeSize == 3)
    {
        return CGSizeMake(90, 88);
    }
    else
    {
        return CGSizeMake(50, 48);
    }
    
}

-(CGSize) getObjectFrame
{
    if(appDelegate.selectedModeSize == 2)
    {
        return CGSizeMake(120, 90);
//        return CGSizeMake(70, 68);
    }
    else if(appDelegate.selectedModeSize == 3)
    {
        return CGSizeMake(160, 120);
    }
    else
    {
        return CGSizeMake(72, 54);
    }
    
}

-(int) getMaxCountForRow1
{
    if(appDelegate.isPresentationInfoOpen)
    {
        if(appDelegate.selectedModeSize == 2)
        {
            return 6;
        }
        else if(appDelegate.selectedModeSize == 3)
        {
            return 3;
        }
        else
        {
            return 6;
        }
    }
    else
    {
        if(appDelegate.selectedModeSize == 2)
        {
            return 11;
        }
        else if(appDelegate.selectedModeSize == 3)
        {
            return 7;
        }
        else
        {
            return 13;
        }
    }
}

-(int) getMaxCountForRow
{
    if(appDelegate.isPresentationInfoOpen)
    {
        if(appDelegate.selectedModeSize == 2)
        {
            return 3;
        }
        else if(appDelegate.selectedModeSize == 3)
        {
            return 2;
        }
        else
        {
            return 5;
        }
    }
    else
    {
        if(appDelegate.selectedModeSize == 2)
        {
            return 5;
        }
        else if(appDelegate.selectedModeSize == 3)
        {
            return 4;
        }
        else
        {
            return 9;
        }
    }
}


-(UIStatusBarStyle)preferredStatusBarStyle
{
    return NO;
}
- (void)application:(UIApplication *)application willChangeStatusBarOrientation:(UIInterfaceOrientation)newStatusBarOrientation duration:(NSTimeInterval)duration {
    
    if ([UIDevice currentDevice].systemVersion.floatValue >= 7.0) {
        //Code if iPhone 4 or 4s and iOS 7
        if (newStatusBarOrientation == UIInterfaceOrientationLandscapeLeft)  {
            // AppDel.isBookListLandscape = YES;
           
            (self.window).frame = CGRectMake(20,0, self.window.frame.size.width, self.window.frame.size.height);
        }
        else  if (newStatusBarOrientation == UIInterfaceOrientationLandscapeRight) {
            // AppDel.isBookListLandscape = YES;
    
            (self.window).frame = CGRectMake(-20,0, self.window.frame.size.width, self.window.frame.size.height);
        }
    }
}

- (void)applicationWillResignActive:(UIApplication *)application {
    /*
     Sent when the application is about to jpge from active to inactive state. This can occur for certain types of temporary interruptions (such as an incoming phone call or SMS message) or when the user quits the application and it begins the transition to the background state.
     Use this method to pause ongoing tasks, disable timers, and throttle down OpenGL ES frame rates. Games should use this method to pause the game.
     */
}

- (void)applicationDidEnterBackground:(UIApplication *)application {
    
    /*
     Use this method to release shared resources, save user data, invalidate timers, and store enough application state information to restore your application to its current state in case it is terminated later. 
     If your application supports background execution, this method is called instead of applicationWillTerminate: when the user quits.
     */
}

- (void)applicationWillEnterForeground:(UIApplication *)application {
    
    /*
     Called as part of the transition from the background to the inactive state; here you can undo many of the changes made on entering the background.
     */
}

- (void)applicationDidBecomeActive:(UIApplication *)application {
    /*
     Restart any tasks that were paused (or not yet started) while the application was inactive. If the application was previously in the background, optionally refresh the user interface.
     */
     
}

- (void)applicationWillTerminate:(UIApplication *)application {
    /*
     Called when the application is about to terminate.
     Save data if appropriate.
     See also applicationDidEnterBackground:
     */
    
    // Version 3.0 change - Remove the observer.
    [[SKPaymentQueue defaultQueue] removeTransactionObserver: [IAPManager sharedInstance]];
}


- (void)applicationDidReceiveMemoryWarning:(UIApplication *)application {
    
    NSLog(@"applicationDidReceiveMemoryWarning ");
}

#pragma mark - awakeFromNib method

-(void)overWriteDB
{

    NSFileManager *fileManager = [NSFileManager defaultManager];
    NSError *error;
    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES);
    NSString *documentsDirectory = paths[0];
    NSString *writableDBPath = [documentsDirectory stringByAppendingPathComponent:@"BITEFXDatabase.sqlite"];

    
    NSString *defaultDBPath = [[NSBundle mainBundle].resourcePath stringByAppendingPathComponent:@"BITEFXDatabase.sqlite"];
    [fileManager copyItemAtPath:defaultDBPath toPath:writableDBPath error:&error];

}

- (void)awakeFromNib {
    [super awakeFromNib];
	BOOL success;
	NSFileManager *fileManager = [NSFileManager defaultManager];
	NSError *error;
	NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES);
	NSString *documentsDirectory = paths[0];
	NSString *writableDBPath = [documentsDirectory stringByAppendingPathComponent:@"BITEFXDatabase.sqlite"];
	success = [fileManager fileExistsAtPath:writableDBPath];
	
    if (success) return;
	// The writable database does not exist, so copy the default to the appropriate location.
	NSString *defaultDBPath = [[NSBundle mainBundle].resourcePath stringByAppendingPathComponent:@"BITEFXDatabase.sqlite"];
	success = [fileManager copyItemAtPath:defaultDBPath toPath:writableDBPath error:&error];
	
//    NSURL *url = [NSURL fileURLWithPath:writableDBPath];
//    BOOL AttribSet = [self addSkipBackupAttributeToItemAtURL:url];
//    if(AttribSet)
//    {
//        NSLog(@"DoneDatabase");
//    }
	if (!success) {
		//UIAlertView *alert = [[UIAlertView alloc] initWithTitle:@"Error!!!" message:@"Failed to create writable database" delegate:self cancelButtonTitle:@"Cancel" otherButtonTitles:nil];
		//[alert show];
		//[alert release];
	}
}


#pragma mark - copy DT.CSS

+ (NSString *) documentPath {
    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES);
    NSString *documentDirectory = paths[0]; // Get Library folder
    return documentDirectory;
}

#pragma mark --------------------------------
#pragma mark NSFileManager Methods


+ (NSString *) BitFXFull {
    
    NSString *path = [AppDelegate documentPath];
    path = [NSString stringWithFormat:@"%@/BiteFXiPadFull",path];
    if (![[NSFileManager defaultManager] fileExistsAtPath:path])
        [[NSFileManager defaultManager] createDirectoryAtPath:path withIntermediateDirectories:NO attributes:nil error:nil]; //Create folder
    {
        NSURL *url = [NSURL fileURLWithPath:path];
        BOOL success = [appDelegate addSkipBackupAttributeToItemAtURL:url];
     
        if(success) {
        
            //NSLog(@"successfully Done");
        }
    }
    return path;
}

+ (NSString*)UpdatePath {
    
    NSString *path = [AppDelegate documentPath];
    path = [NSString stringWithFormat:@"%@/Update0001",path];
    if (![[NSFileManager defaultManager] fileExistsAtPath:path])
        [[NSFileManager defaultManager] createDirectoryAtPath:path withIntermediateDirectories:NO attributes:nil error:nil]; //Create folder
    {
        NSURL *url = [NSURL fileURLWithPath:path];
        BOOL success = [appDelegate addSkipBackupAttributeToItemAtURL:url];
        
        if(success) {
        }
    }
    return path;
}

#pragma mark --------------------------------
#pragma mark Version 2.4 Changes

+ (void)createFoldersInDocumentDirectoryToSaveAppData {
    
    NSString *aStrDirPath = nil;
    
    // Create folder named "BiteFXV2/Movies"...
    aStrDirPath = [NSString stringWithFormat:@"%@/%@/%@", DOCUMENT_DIRECTORY_PATH, BITEFXV2, MOVIES];
    
    BOOL isDir;
    if (![[NSFileManager defaultManager] fileExistsAtPath:aStrDirPath isDirectory:&isDir]) {
        
        // Directory does not exist. Create it and add skip backup attributes...
        [[NSFileManager defaultManager] createDirectoryAtPath:aStrDirPath withIntermediateDirectories:YES attributes:nil error:nil]; //Create folder
        {
            NSURL *url = [NSURL fileURLWithPath:aStrDirPath];
            BOOL success = [appDelegate addSkipBackupAttributeToItemAtURL:url];
            
            if(success) {
            }
        }
        
    }
    
    // Create folder named "BiteFXV2/Images"...
    aStrDirPath = [NSString stringWithFormat:@"%@/%@/%@", DOCUMENT_DIRECTORY_PATH, BITEFXV2, IMAGES];
    
    if (![[NSFileManager defaultManager] fileExistsAtPath:aStrDirPath isDirectory:&isDir]) {

        // Directory does not exist. Create it and add skip backup attributes...
        [[NSFileManager defaultManager] createDirectoryAtPath:aStrDirPath withIntermediateDirectories:YES attributes:nil error:nil]; //Create folder
        {
            NSURL *url = [NSURL fileURLWithPath:aStrDirPath];
            BOOL success = [appDelegate addSkipBackupAttributeToItemAtURL:url];
            
            if(success) {
            }
        }
        
    }

    // Create folder named "BiteFXV2/Presentations"...
    aStrDirPath = [NSString stringWithFormat:@"%@/%@/%@", DOCUMENT_DIRECTORY_PATH, BITEFXV2, PRESENTATIONS];
    
    if (![[NSFileManager defaultManager] fileExistsAtPath:aStrDirPath isDirectory:&isDir]) {
        
        // Directory does not exist. Create it and add skip backup attributes...
        [[NSFileManager defaultManager] createDirectoryAtPath:aStrDirPath withIntermediateDirectories:YES attributes:nil error:nil]; //Create folder
        {
            NSURL *url = [NSURL fileURLWithPath:aStrDirPath];
            BOOL success = [appDelegate addSkipBackupAttributeToItemAtURL:url];
            
            if(success) {
            }
        }
        
    }

    // Create folder named "BiteFXV2/Thumbnails/Movies"...
    aStrDirPath = [NSString stringWithFormat:@"%@/%@/%@/%@", DOCUMENT_DIRECTORY_PATH, BITEFXV2, THUMBNAILS, MOVIES];
    
    if (![[NSFileManager defaultManager] fileExistsAtPath:aStrDirPath isDirectory:&isDir]) {
        
        // Directory does not exist. Create it and add skip backup attributes...
        [[NSFileManager defaultManager] createDirectoryAtPath:aStrDirPath withIntermediateDirectories:YES attributes:nil error:nil]; //Create folder
        {
            NSURL *url = [NSURL fileURLWithPath:aStrDirPath];
            BOOL success = [appDelegate addSkipBackupAttributeToItemAtURL:url];
            
            if(success) {
            }
        }
        
    }
    
    // Create folder named "BiteFXV2/Thumbnails/Images"...
    aStrDirPath = [NSString stringWithFormat:@"%@/%@/%@/%@", DOCUMENT_DIRECTORY_PATH, BITEFXV2, THUMBNAILS, IMAGES];
    
    if (![[NSFileManager defaultManager] fileExistsAtPath:aStrDirPath isDirectory:&isDir]) {
        
        // Directory does not exist. Create it and add skip backup attributes...
        [[NSFileManager defaultManager] createDirectoryAtPath:aStrDirPath withIntermediateDirectories:YES attributes:nil error:nil]; //Create folder
        {
            NSURL *url = [NSURL fileURLWithPath:aStrDirPath];
            BOOL success = [appDelegate addSkipBackupAttributeToItemAtURL:url];
            
            if(success) {
            }
        }
        
    }
    
    //===================
    // Version 3.1 Changes
    //===================
    // Feature MVI file changes.
    //-----------------
    // Create folder named "BiteFXV2/FeatureSetImages"...
    aStrDirPath = [NSString stringWithFormat:@"%@/%@/%@", DOCUMENT_DIRECTORY_PATH, BITEFXV2, FEATURESETIMAGES];
    
    if (![[NSFileManager defaultManager] fileExistsAtPath:aStrDirPath isDirectory:&isDir]) {
        
        // Directory does not exist. Create it and add skip backup attributes...
        [[NSFileManager defaultManager] createDirectoryAtPath:aStrDirPath withIntermediateDirectories:YES attributes:nil error:nil]; //Create folder
        {
            NSURL *url = [NSURL fileURLWithPath:aStrDirPath];
            BOOL success = [appDelegate addSkipBackupAttributeToItemAtURL:url];
            
            if(success) {
            }
        }
        
    }

}

- (void)addNewColumnsInDB {

    // First check for column "fileType" in CollectionMaster...
    NSString *aStrSql = [NSString stringWithFormat:@"select fileType from CollectionMaster"];
    BOOL aBoolColumnExists = [[Database sharedDatabase] checkColumnExists:aStrSql];
    
    if (!aBoolColumnExists)
    {
        NSString *aStrUpdateQuery = [NSString stringWithFormat:@"ALTER TABLE CollectionMaster ADD COLUMN fileType TEXT DEFAULT 'MOVIE'"];
        [[Database sharedDatabase] Update:aStrUpdateQuery];
    }

    // New change regarding import images from iCloud...
    // Now check for column "isUserDefine"...
    aStrSql = [NSString stringWithFormat:@"select isUserDefine from CollectionMaster"];
    aBoolColumnExists = [[Database sharedDatabase] checkColumnExists:aStrSql];
    
    if (!aBoolColumnExists)
    {
        NSString *aStrUpdateQuery = [NSString stringWithFormat:@"ALTER TABLE CollectionMaster ADD COLUMN isUserDefine TEXT DEFAULT '0'"];
        [[Database sharedDatabase] Update:aStrUpdateQuery];
    }

    // New change regarding Presentation Information panel...
    // Now check for column "infoFilePath"...
    aStrSql = [NSString stringWithFormat:@"select infoFilePath from CollectionMaster"];
    aBoolColumnExists = [[Database sharedDatabase] checkColumnExists:aStrSql];
    
    if (!aBoolColumnExists)
    {
        NSString *aStrUpdateQuery = [NSString stringWithFormat:@"ALTER TABLE CollectionMaster ADD COLUMN infoFilePath TEXT DEFAULT ''"];
        [[Database sharedDatabase] Update:aStrUpdateQuery];
    }
    
    // Now check for column "isHidden" in CollectionFilesMaster...
    aStrSql = [NSString stringWithFormat:@"select isHidden from CollectionFilesMaster"];
    aBoolColumnExists = [[Database sharedDatabase] checkColumnExists:aStrSql];
    
    if (!aBoolColumnExists)
    {
        NSString *aStrUpdateQuery = [NSString stringWithFormat:@"ALTER TABLE CollectionFilesMaster ADD COLUMN isHidden TEXT DEFAULT '0'"];
        [[Database sharedDatabase] Update:aStrUpdateQuery];
    }

    // Now check for column "fileType" in LocalFileMaster...
    aStrSql = [NSString stringWithFormat:@"select fileType from LocalFileMaster"];
    aBoolColumnExists = [[Database sharedDatabase] checkColumnExists:aStrSql];
    
    if (!aBoolColumnExists)
    {
        NSString *aStrUpdateQuery = [NSString stringWithFormat:@"ALTER TABLE LocalFileMaster ADD COLUMN fileType TEXT DEFAULT 'MOVIE'"];
        [[Database sharedDatabase] Update:aStrUpdateQuery];
    }
    
    aStrSql = [NSString stringWithFormat:@"select moviePlaySpeed from LocalFileMaster"];
    aBoolColumnExists = [[Database sharedDatabase] checkColumnExists:aStrSql];
    
    if (!aBoolColumnExists)
    {
        NSString *aStrUpdateQuery = [NSString stringWithFormat:@"ALTER TABLE LocalFileMaster ADD COLUMN moviePlaySpeed TEXT DEFAULT '1.0'"];
        [[Database sharedDatabase] Update:aStrUpdateQuery];
    }
    
    //===================
    // Version 2.5 Changes
    //===================
    
    // Add new column "fileOrder" in CollectionMaster...(This is added to manage sequence index)
    // First check for column "fileOrder" in CollectionMaster...
    aStrSql = [NSString stringWithFormat:@"select fileOrder from CollectionMaster"];
    aBoolColumnExists = [[Database sharedDatabase] checkColumnExists:aStrSql];
    
    if (!aBoolColumnExists)
    {
        NSString *aStrUpdateQuery = [NSString stringWithFormat:@"ALTER TABLE CollectionMaster ADD COLUMN fileOrder INTEGER DEFAULT '0'"];
        [[Database sharedDatabase] Update:aStrUpdateQuery];
    }
    
    // Add new column "isFavourite" in CollectionMaster...(This is added to manage Favourite)
    // First check for column "isFavourite" in CollectionMaster...
    aStrSql = [NSString stringWithFormat:@"select isFavourite from CollectionMaster"];
    aBoolColumnExists = [[Database sharedDatabase] checkColumnExists:aStrSql];
    
    if (!aBoolColumnExists)
    {
        NSString *aStrUpdateQuery = [NSString stringWithFormat:@"ALTER TABLE CollectionMaster ADD COLUMN isFavourite INTEGER DEFAULT '0'"];
        [[Database sharedDatabase] Update:aStrUpdateQuery];
    }
    
    //===================
    // Version 3.0 Changes
    //===================
    
//    // Add new column "sequenceIndex" in CollectionUserDefine...(This is added to manage presentation sequence index)
//    // First check for column "sequenceIndex" in CollectionUserDefine...
    aStrSql = [NSString stringWithFormat:@"select sequenceIndex from CollectionUserDefine"];
    aBoolColumnExists = [[Database sharedDatabase] checkColumnExists:aStrSql];
    
    if (!aBoolColumnExists)
    {
        NSString *aStrUpdateQuery = [NSString stringWithFormat:@"ALTER TABLE CollectionUserDefine ADD COLUMN sequenceIndex INTEGER DEFAULT '0'"];
        [[Database sharedDatabase] Update:aStrUpdateQuery];
    }

    //===================
    // Version 3.1 Changes
    //===================
    // Feature MVI file changes.
    
    // Add new column "featureID" in CollectionMaster...(This is added to manage Presentations in FeatureSets.)
    // First check for column "featureID" in CollectionMaster...
    aStrSql = [NSString stringWithFormat:@"select featureID from CollectionMaster"];
    aBoolColumnExists = [[Database sharedDatabase] checkColumnExists:aStrSql];
    
    if (!aBoolColumnExists)
    {
        NSString *aStrUpdateQuery = [NSString stringWithFormat:@"ALTER TABLE CollectionMaster ADD COLUMN featureID INTEGER DEFAULT '0'"];
        [[Database sharedDatabase] Update:aStrUpdateQuery];
    }
    
    // Add new column "isFavourite" in LocalFileMaster...(This is added to manage sequence index)
    // First check for column "isFavourite" in LocalFileMaster...
    aStrSql = [NSString stringWithFormat:@"select isFavourite from LocalFileMaster"];
    aBoolColumnExists = [[Database sharedDatabase] checkColumnExists:aStrSql];
    
    if (!aBoolColumnExists)
    {
        NSString *aStrUpdateQuery = [NSString stringWithFormat:@"ALTER TABLE LocalFileMaster ADD COLUMN isFavourite INTEGER DEFAULT '0'"];
        [[Database sharedDatabase] Update:aStrUpdateQuery];
    }
    
}

#pragma mark -
#pragma mark Network Reachability Methods

- (BOOL)isNetWorkAvailable {
	
	[NetworkReachability sharedReachability].hostName = @"http://www.google.com/";
	NetworkStatus remoteHostStatus = [[NetworkReachability sharedReachability] internetConnectionStatus];
	
	if (remoteHostStatus == NotReachable){
		return NO;
	}
	else if (remoteHostStatus == ReachableViaCarrierDataNetwork || remoteHostStatus == ReachableViaWiFiNetwork){
		return YES;
	}
	
	return YES;
}

- (void) copyFile {
    
    NSString *filePath = [[NSBundle mainBundle] pathForResource:@"dt" ofType:@"css"];
    NSString *img1Path = [[NSBundle mainBundle]pathForResource:@"NextButton" ofType:@"png"];
    NSString *img2Path = [[NSBundle mainBundle]pathForResource:@"PlayButton" ofType:@"png"];
    NSString *img3Path = [[NSBundle mainBundle]pathForResource:@"LoopButton" ofType:@"png"];
    NSString *img4Path = [[NSBundle mainBundle]pathForResource:@"LoopButtonPressed" ofType:@"png"];
    
    NSString *aStrDirPath = [NSString stringWithFormat:@"%@/%@/%@", DOCUMENT_DIRECTORY_PATH, BITEFXV2, MOVIES];
    
    NSString *toCopy = [NSString stringWithFormat:@"%@/dt.css", aStrDirPath];
    NSString *toCopyImage1 = [NSString stringWithFormat:@"%@/NextButton.png", aStrDirPath];
    NSString *toCopyImage2 = [NSString stringWithFormat:@"%@/PlayButton.png", aStrDirPath];
    NSString *toCopyImage3 = [NSString stringWithFormat:@"%@/LoopButton.png", aStrDirPath];
    NSString *toCopyImage4 = [NSString stringWithFormat:@"%@/LoopButtonPressed.png", aStrDirPath];
//    NSString *toCopyImage4 = [NSString stringWithFormat:@"%@/LoopButtonPressed.png",[AppDelegate BitFXFull]];
    
    if(![[NSFileManager defaultManager] fileExistsAtPath:toCopy]){
        if ([[NSFileManager defaultManager] copyItemAtPath:filePath toPath:toCopy error:nil]) {

	    NSURL *pathURL= [NSURL fileURLWithPath:toCopy];
            BOOL attSet=[self addSkipBackupAttributeToItemAtURL:pathURL];
            if(attSet) {
                
                //NSLog(@"Done");
            }
            //NSLog(@"file copy");
        } else {
            //NSLog(@"file not copy");
        }
    
    }  else {
        //NSLog(@"file already copy");
    }
    
    if(![[NSFileManager defaultManager] fileExistsAtPath:toCopyImage1]){
        if ([[NSFileManager defaultManager] copyItemAtPath:img1Path toPath:toCopyImage1 error:nil]) {
            NSURL *pathURL= [NSURL fileURLWithPath:toCopyImage1];
            BOOL attSet=[self addSkipBackupAttributeToItemAtURL:pathURL];
            if(attSet) {
                
               // NSLog(@"Done");
            }

            //NSLog(@"file copy");
        } else {
           // NSLog(@"file not copy");
        }
    } else {
       // NSLog(@"file already copy");
    }
    
    if(![[NSFileManager defaultManager] fileExistsAtPath:toCopyImage2]){
        if ([[NSFileManager defaultManager] copyItemAtPath:img2Path toPath:toCopyImage2 error:nil]) {

	 NSURL *pathURL= [NSURL fileURLWithPath:toCopyImage2];
            BOOL attSet=[self addSkipBackupAttributeToItemAtURL:pathURL];
          
            if(attSet)
            {
               // NSLog(@"Done");
            }
            //NSLog(@"file copy");
        }
        else {
            //NSLog(@"file not copy");
        }
    }
    else {
        //NSLog(@"file already copy");
    }
    
    if(![[NSFileManager defaultManager] fileExistsAtPath:toCopyImage3]){
        if ([[NSFileManager defaultManager] copyItemAtPath:img3Path toPath:toCopyImage3 error:nil]) {

            NSURL *pathURL= [NSURL fileURLWithPath:toCopyImage3];
            BOOL attSet=[self addSkipBackupAttributeToItemAtURL:pathURL];
            
           
            if(attSet) {
                //NSLog(@"Done");
            }
            //NSLog(@"file copy");
        }
        else {
           // NSLog(@"file not copy");
        }
    }
    else {
        //NSLog(@"file already copy");
    }
    
    if(![[NSFileManager defaultManager] fileExistsAtPath:toCopyImage4]){
        if ([[NSFileManager defaultManager] copyItemAtPath:img4Path toPath:toCopyImage4 error:nil]) {
            NSURL *pathURL= [NSURL fileURLWithPath:toCopyImage4];
            BOOL attSet=[self addSkipBackupAttributeToItemAtURL:pathURL];
            if(attSet)
            {
                //NSLog(@"Done");
            }
           // NSLog(@"file copy");
        }
        else {
            //NSLog(@"file not copy");
        }
    }
    else {
        //NSLog(@"file already copy");
    }
    
    #pragma mark --------------------------------
    #pragma mark Version 2.4 Folder Structure Changes

    [self copyFilesForPresentations];
    
}

//===================
// Version 2.4 Changes
//===================

- (void) copyFilesForPresentations {
    
    NSString *filePath = [[NSBundle mainBundle] pathForResource:@"dt" ofType:@"css"];
    NSString *img1Path = [[NSBundle mainBundle]pathForResource:@"NextButton" ofType:@"png"];
    NSString *img2Path = [[NSBundle mainBundle]pathForResource:@"PlayButton" ofType:@"png"];
    NSString *img3Path = [[NSBundle mainBundle]pathForResource:@"LoopButton" ofType:@"png"];
    NSString *img4Path = [[NSBundle mainBundle]pathForResource:@"LoopButtonPressed" ofType:@"png"];
    
    #pragma mark --------------------------------
    #pragma mark Version 2.4 Folder Structure Changes
    
    NSString *aStrPresentationPath = [NSString stringWithFormat:@"%@/%@/%@", DOCUMENT_DIRECTORY_PATH, BITEFXV2, PRESENTATIONS];
    
    NSString *toCopy = [NSString stringWithFormat:@"%@/dt.css", aStrPresentationPath];
    NSString *toCopyImage1 = [NSString stringWithFormat:@"%@/NextButton.png", aStrPresentationPath];
    NSString *toCopyImage2 = [NSString stringWithFormat:@"%@/PlayButton.png", aStrPresentationPath];
    NSString *toCopyImage3 = [NSString stringWithFormat:@"%@/LoopButton.png", aStrPresentationPath];
    NSString *toCopyImage4 = [NSString stringWithFormat:@"%@/LoopButtonPressed.png", aStrPresentationPath];
    
    if(![[NSFileManager defaultManager] fileExistsAtPath:toCopy]){
        if ([[NSFileManager defaultManager] copyItemAtPath:filePath toPath:toCopy error:nil]) {
            
            NSURL *pathURL= [NSURL fileURLWithPath:toCopy];
            BOOL attSet=[self addSkipBackupAttributeToItemAtURL:pathURL];
            if(attSet) {
                
                //NSLog(@"Done");
            }
            //NSLog(@"file copy");
        } else {
            //NSLog(@"file not copy");
        }
        
    }  else {
        //NSLog(@"file already copy");
    }
    
    if(![[NSFileManager defaultManager] fileExistsAtPath:toCopyImage1]){
        if ([[NSFileManager defaultManager] copyItemAtPath:img1Path toPath:toCopyImage1 error:nil]) {
            NSURL *pathURL= [NSURL fileURLWithPath:toCopyImage1];
            BOOL attSet=[self addSkipBackupAttributeToItemAtURL:pathURL];
            if(attSet) {
                
                // NSLog(@"Done");
            }
            
            //NSLog(@"file copy");
        } else {
            // NSLog(@"file not copy");
        }
    } else {
        // NSLog(@"file already copy");
    }
    
    if(![[NSFileManager defaultManager] fileExistsAtPath:toCopyImage2]){
        if ([[NSFileManager defaultManager] copyItemAtPath:img2Path toPath:toCopyImage2 error:nil]) {
            
            NSURL *pathURL= [NSURL fileURLWithPath:toCopyImage2];
            BOOL attSet=[self addSkipBackupAttributeToItemAtURL:pathURL];
            
            if(attSet)
            {
                // NSLog(@"Done");
            }
            //NSLog(@"file copy");
        }
        else {
            //NSLog(@"file not copy");
        }
    }
    else {
        //NSLog(@"file already copy");
    }
    
    if(![[NSFileManager defaultManager] fileExistsAtPath:toCopyImage3]){
        if ([[NSFileManager defaultManager] copyItemAtPath:img3Path toPath:toCopyImage3 error:nil]) {
            
            NSURL *pathURL= [NSURL fileURLWithPath:toCopyImage3];
            BOOL attSet=[self addSkipBackupAttributeToItemAtURL:pathURL];
            
            
            if(attSet) {
                //NSLog(@"Done");
            }
            //NSLog(@"file copy");
        }
        else {
            // NSLog(@"file not copy");
        }
    }
    else {
        //NSLog(@"file already copy");
    }
    
    if(![[NSFileManager defaultManager] fileExistsAtPath:toCopyImage4]){
        if ([[NSFileManager defaultManager] copyItemAtPath:img4Path toPath:toCopyImage4 error:nil]) {
            NSURL *pathURL= [NSURL fileURLWithPath:toCopyImage4];
            BOOL attSet=[self addSkipBackupAttributeToItemAtURL:pathURL];
            if(attSet)
            {
                //NSLog(@"Done");
            }
            // NSLog(@"file copy");
        }
        else {
            //NSLog(@"file not copy");
        }
    }
    else {
        //NSLog(@"file already copy");
    }
    
}

- (void) copyUpdateFile {
    
    NSString *filePath = [[NSBundle mainBundle] pathForResource:@"dt" ofType:@"css"];
    NSString *img1Path = [[NSBundle mainBundle]pathForResource:@"NextButton" ofType:@"png"];
    NSString *img2Path = [[NSBundle mainBundle]pathForResource:@"PlayButton" ofType:@"png"];
    NSString *img3Path = [[NSBundle mainBundle]pathForResource:@"LoopButton" ofType:@"png"];
    NSString *img4Path = [[NSBundle mainBundle]pathForResource:@"LoopButtonPressed" ofType:@"png"];
    
    NSString *toCopy = [NSString stringWithFormat:@"%@/dt.css",[AppDelegate UpdatePath]];
    NSString *toCopyImage1 = [NSString stringWithFormat:@"%@/NextButton.png",[AppDelegate UpdatePath]];
    NSString *toCopyImage2 = [NSString stringWithFormat:@"%@/PlayButton.png",[AppDelegate UpdatePath]];
    NSString *toCopyImage3 = [NSString stringWithFormat:@"%@/LoopButton.png",[AppDelegate UpdatePath]];
    NSString *toCopyImage4 = [NSString stringWithFormat:@"%@/LoopButtonPressed.png",[AppDelegate UpdatePath]];

    if(![[NSFileManager defaultManager] fileExistsAtPath:toCopy]){
        if ([[NSFileManager defaultManager] copyItemAtPath:filePath toPath:toCopy error:nil]) {
            NSURL *pathURL= [NSURL fileURLWithPath:toCopy];
            BOOL attSet=[self addSkipBackupAttributeToItemAtURL:pathURL];
            if(attSet)
            {
                //NSLog(@"Done");
            }
            //NSLog(@"file copy");
        }
        else {
            //NSLog(@"file not copy");
        }
    }
    else {
        //NSLog(@"file already copy");
    }
    if(![[NSFileManager defaultManager] fileExistsAtPath:toCopyImage1]){
        if ([[NSFileManager defaultManager] copyItemAtPath:img1Path toPath:toCopyImage1 error:nil]) {
	    NSURL *pathURL= [NSURL fileURLWithPath:toCopyImage1];
            BOOL attSet=[self addSkipBackupAttributeToItemAtURL:pathURL];
            if(attSet)
            {
                //NSLog(@"Done");
            }
            //NSLog(@"file copy");
        }
        else {
           // NSLog(@"file not copy");
        }
    }
    else {
        //NSLog(@"file already copy");
    }

    if(![[NSFileManager defaultManager] fileExistsAtPath:toCopyImage2]){
        if ([[NSFileManager defaultManager] copyItemAtPath:img2Path toPath:toCopyImage2 error:nil]) {

            NSURL *pathURL= [NSURL fileURLWithPath:toCopyImage2];
            BOOL attSet=[self addSkipBackupAttributeToItemAtURL:pathURL];
            if(attSet)
            {
                //NSLog(@"Done");
            }
            //NSLog(@"file copy");
        }
        else {
            //NSLog(@"file not copy");
        }
    }
    else {
        //NSLog(@"file already copy");
    }

    if(![[NSFileManager defaultManager] fileExistsAtPath:toCopyImage3]){
        if ([[NSFileManager defaultManager] copyItemAtPath:img3Path toPath:toCopyImage3 error:nil]) {
       	    NSURL *pathURL= [NSURL fileURLWithPath:toCopyImage3];
            BOOL attSet=[self addSkipBackupAttributeToItemAtURL:pathURL];
            if(attSet)
            {
                //NSLog(@"Done");
            }
           //NSLog(@"file copy");
        }
        else {
            //NSLog(@"file not copy");
        }
    }
    else {
        //NSLog(@"file already copy");
    }

    if(![[NSFileManager defaultManager] fileExistsAtPath:toCopyImage4]){
        if ([[NSFileManager defaultManager] copyItemAtPath:img4Path toPath:toCopyImage4 error:nil]) {

	    NSURL *pathURL= [NSURL fileURLWithPath:toCopyImage4];
            BOOL attSet=[self addSkipBackupAttributeToItemAtURL:pathURL];
            if(attSet)
            {
               // NSLog(@"Done");
            }
            //NSLog(@"file copy");
        }
        else {
           // NSLog(@"file not copy");
        }
    }
    else {
        //NSLog(@"file already copy");
    }

}


#pragma mark -
#pragma mark  NetworkConnection 

- (BOOL)connected  {
   
    Reachability *reachability = [Reachability reachabilityForInternetConnection];
    NetworkStatus1 networkStatus = [reachability currentReachabilityStatus];
    return !(networkStatus == NotReachable);
}

#pragma mark -
#pragma mark - Skip Backup attribute method

- (BOOL)addSkipBackupAttributeToItemAtURL:(NSURL *)URL1 {
    
    if ([UIDevice currentDevice].systemVersion.floatValue < 5.1) 
    {
        const char* filePath = URL1.path.fileSystemRepresentation;
        
        const char* attrName = "com.apple.MobileBackup";
        u_int8_t attrValue = 1;
        
        int result = setxattr(filePath, attrName, &attrValue, sizeof(attrValue), 0, 0);
        return result == 0;
    } 
    
    else { // iOS >= 5.1
        NSError *error = nil;
        [URL1 setResourceValue:@YES forKey:NSURLIsExcludedFromBackupKey error:&error];
        return error == nil;
    }
}


#pragma mark - iCloud Delegate files

-(void) loadFiles:(NSArray *)arrFileList{
    
     ICloud *cloud = [ICloud shareCloud];
     if(arrFileList.count == 0 ) {
         
                NSLog(@"-------------> Database Saved on iCloud <--------------");
               [cloud createFile:strFileName];
             
             if ([[NSUserDefaults standardUserDefaults] boolForKey:@"RestorePressed"])
             {
                 [[NSUserDefaults standardUserDefaults] setBool:NO forKey:@"RestorePressed"];
                 [[NSUserDefaults standardUserDefaults] synchronize];
                 
                 [self performSelector:@selector(checkForRestore) withObject:self afterDelay:20.0];
             }
             
        }  else {
            
               NSLog(@"-------------> Database Copied From iCloud <--------------");
               [self.mutArrDataItem removeAllObjects];
               [self.mutArrDataItem addObject: arrFileList[0]];
             
             for (int aIntCnt = 0; aIntCnt < arrFileList.count; aIntCnt++)
            {
                NSMetadataItem *item = arrFileList[aIntCnt];
                
                NSString *aStrName = [item valueForAttribute:NSMetadataItemDisplayNameKey];
                
                //Memory Leaks Commments
                NSLog(@"Item :: %@",aStrName);
            }
             
             if (arrFileList.count > 0)
             {

                 if (intStatusCheckForiCloud == 1) 
                 {
                     intStatusCheckForiCloud = 0;
                     
                     [self startIndicator];
                     [self performSelector:@selector(checkForRestore) withObject:self afterDelay:20.0];
                 }

             }
             else
             {
                 if (intStatusCheckForiCloud == 1) 
                 {
                     intStatusCheckForiCloud = 0;
                     
                     [UIAlertController showAlertInViewController:self.window.rootViewController withTitle:@"BiteFX" message:@"No Data available on iCloud." cancelButtonTitle:@"OK" destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
                 }
             }
             
            

            
               NSOperationQueue *queue = [[NSOperationQueue alloc]init];
               
               /* Create our NSInvocationOperation to call loadiCloudData, passing in nil */
               NSInvocationOperation *operation = [[NSInvocationOperation alloc] initWithTarget:self
                                                                                              selector:@selector(loadiCloudData)
                                                                                               object:nil];
               
               /* Add the operation to the queue */
               [queue addOperation:operation];
            
             }
    //  [actIndicator stopAnimating];
     
     
}

- (void)loadiCloudData
{
    ICloud *cloud = [ICloud shareCloud];
    [cloud openFile:(self.mutArrDataItem)[0]];
}

- (void)iCloudIntegrationforDB
{
    NSURL *ubiq = [[NSFileManager defaultManager] URLForUbiquityContainerIdentifier:nil];
    
    if(ubiq)
    {
        NSLog(@"@------------------->>Check Point-1<<-------------------@");
        isiCloudAvailble = YES;
        {
            if(self.mutArrDataItem.count > 0)
            {
                NSLog(@"@------------------->>Check Point-2<<-------------------@");
                NSOperationQueue *queue = [[NSOperationQueue alloc]init];
                
                /* Create our NSInvocationOperation to call loadiCloudData, passing in nil */
                NSInvocationOperation *operation = [[NSInvocationOperation alloc] initWithTarget:self
                                                                                        selector:@selector(loadiCloudData)
                                                                                          object:nil];
                
                /* Add the operation to the queue */
                [queue addOperation:operation];
            }
            else 
            {
                NSLog(@"@------------------->>Check Point-3<<-------------------@");
                ICloud *cloud = [ICloud shareCloud];
//                cloud.delegate = self;
                [cloud getAllFileList];
            }
        }
    }
    else 
    {
        isiCloudAvailble = NO;
        NSLog(@"No iCloud access");
    }
}

#pragma mark - Check Restore Methods

- (void)checkForRestore
{
    [self stopIndicator];
    
    NSString *aStrNameQuery = [NSString stringWithFormat:@"select * from InAppTbl"];
    
    NSMutableArray *aMutArrNames = [[Database sharedDatabase] getAllDataForQuery:aStrNameQuery];
    
    if (aMutArrNames.count > 0)
    {
        if ([aMutArrNames[0][@"Kind"] isEqualToString:@"InApp"])
        {
            NSString *aStrEndDate = aMutArrNames[0][@"End_Date"];
            
            NSDateFormatter *aDtfrmt = [[NSDateFormatter alloc] init];
            
            aDtfrmt.dateFormat = @"dd/MM/yyyy";
            
            NSDate *aDtEndDate = [aDtfrmt dateFromString:aStrEndDate];
            
            //Memory Leaks Commments
            
            
            NSTimeInterval aTmIntEndDate = aDtEndDate.timeIntervalSince1970;
            
            NSTimeInterval aTmIntCurr = [NSDate date].timeIntervalSince1970;
            
            if (aTmIntEndDate >= aTmIntCurr)
            {
                if (![[NSUserDefaults standardUserDefaults] boolForKey:@"Restore"])
                {
                    [[NSUserDefaults standardUserDefaults] setBool:YES forKey:@"Restore"];
                    [[NSUserDefaults standardUserDefaults] synchronize];
                }
                if([self connected])
                {
                    // remove data from database.
                    
                    [self.viewController moveToBasicVersion];
                    
                    [self.viewController callWSforInAppVarification:aMutArrNames[0][@"Receipt"]]; 
                    
                    [[NSUserDefaults standardUserDefaults] setBool:YES forKey:@"Restore"];
                    [[NSUserDefaults standardUserDefaults] synchronize];

                }
                else
                 {
                    [UIAlertController showAlertInViewController:self.window.rootViewController withTitle:@"BiteFX" message:@"No internet connection found. Please try again!" cancelButtonTitle:@"OK" destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
                    
                    [[NSUserDefaults standardUserDefaults] setBool:NO forKey:@"Restore"];
                    [[NSUserDefaults standardUserDefaults] synchronize];

                } 
            }
            else
            {
                [UIAlertController showAlertInViewController:self.window.rootViewController withTitle:@"BiteFX" message:@"Your BiteFX subscription has been expired." cancelButtonTitle:@"OK" destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
                
                NSLog(@"Your BiteFX Has been expired.");
                
                [[NSUserDefaults standardUserDefaults] setBool:YES forKey:@"Restore"];
                [[NSUserDefaults standardUserDefaults] synchronize];
                
//                intStatusCheckForiCloud = 2;
                
//                [self performSelector:@selector(showAlertView) withObject:nil afterDelay:3.0];
                
            }
        }
        else
        {
            
        }
    }
    else
    {
        [UIAlertController showAlertInViewController:self.window.rootViewController withTitle:@"BiteFX" message:@"If you are a BiteFX subscriber, please login to access your available content”. It will be taken care of." cancelButtonTitle:@"OK" destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
    }
     [appDelegate stopIndicator];
    [self stopIndicator];
}

#pragma mark - IndicatorMethod

- (void)startPurchaseIndicatorWithID:(NSInteger)tag {
    
    [[UIApplication sharedApplication] beginIgnoringInteractionEvents];
    
    actView = [[UIActivityIndicatorView alloc] initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleGray];
    
    if (tag == 0) {
        actView.frame = CGRectMake(453, 318, 37, 37);
    } else {
        actView.frame = CGRectMake(453, 548, 37, 37);
    }
    
    actView.tag = 555;
    
    [self.viewController.view addSubview:actView];
    
    [actView startAnimating];
    
    //===================
    // Version 3.0 Changes
    //===================
    // New InApp Code changes...Commented below line...
//    [self performSelector:@selector(stopIndicator) withObject:self afterDelay:30.0];
}

- (void)startIndicator {
    
    [[UIApplication sharedApplication] beginIgnoringInteractionEvents];
    
    actView = [[UIActivityIndicatorView alloc] initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleGray];

    actView.frame = CGRectMake(453, 328, 37, 37);

    actView.tag = 555;

    [self.viewController.view addSubview:actView];

    [actView startAnimating];
    
    //===================
    // Version 3.0 Changes
    //===================
    // New InApp Code changes...Commented below line...
//    [self performSelector:@selector(stopIndicator) withObject:self afterDelay:30.0];
}

- (void)stopIndicator {
    
    [actView stopAnimating];
    
    [[UIApplication sharedApplication] endIgnoringInteractionEvents];
    
    if ([self.viewController.view viewWithTag:555])
    {
        [actView removeFromSuperview];
    }
}


#pragma mark - method

+ (NSMutableArray *) listOfRemainFileDownload {

    //Memory Leaks Commments
    //Deleted that image from player view BiteFX Main Help.png
    NSMutableArray *arrAllFile = [[NSMutableArray alloc]init];

    NSString *sql = @"select FileURL from FileDownloadList where IsDownloaded = 0";
    [arrAllFile addObjectsFromArray:[[Database shareDatabase] getFirstColumnForQuery:sql]];
    
    if (arrAllFile.count > 0) {
        
        AppDelegateobj.isFullversionDownloading = TRUE;
        AppDelegateobj.isUpdateDownloading = FALSE;
        return arrAllFile;
    }
    //TODO: Only for testing

    NSString *strSql = [NSString stringWithFormat:@"select * from FileUpdateList where IsDownloaded = 0"];
    //NSString *sql = @"select FileURL from FileUpdateList where IsDownloaded = 0";
    [arrAllFile addObjectsFromArray:[[Database shareDatabase] getAllDataForQuery:strSql]];
    
    if (arrAllFile.count > 0) {
        AppDelegateobj.isUpdateDownloading = TRUE;
        AppDelegateobj.isFullversionDownloading = FALSE;
        return arrAllFile;
    }
    
    return arrAllFile;
}


- (BOOL)checkIsFullversionDownloading {
    
    if (self.isFullversionDownloading) {
        return TRUE;
    }
    return FALSE;
}

- (void) addNewTablesForNewVersion {
    
//    NSString *key = @"NewTableAdded";
//    
//    BOOL isVersionSet = [[NSUserDefaults standardUserDefaults ]boolForKey:key];
//    if (isVersionSet == FALSE)
//    {
//        
//        [self createTableFoNewVersion];
////        [self callTestwebServiceRemainingVideos];
//        [[NSUserDefaults standardUserDefaults] setBool:TRUE forKey:key];
//        [[NSNotificationCenter defaultCenter] postNotificationName:@"NewVersionUpdateSuccessfully" object:nil];
//    }
}

- (void) createTableFoNewVersion {
    
    NSString *strCreateTable = @"create table IF NOT EXISTS FileDownloadList (serial INTEGER PRIMARY KEY ,FileURL TEXT,IsDownloaded INTEGER,isSkipFile INTERGER)";
    [[Database shareDatabase] Insert:strCreateTable];
    strCreateTable = @"create table IF NOT EXISTS FileUpdateList (serial INTEGER PRIMARY KEY ,FileName TEXT,FileURL TEXT,IsDownloaded INTEGER,isSkipFile INTEGER,UpdateVersion TEXT)";
    [[Database shareDatabase] Insert:strCreateTable];
    
    /*
    NSString *strCreateTable = @"create table IF NOT EXISTS FileDownloadList (serial INTEGER PRIMARY KEY ,FileURL TEXT,isDownloaded INTEGER)";
    [[Database shareDatabase] Insert:strCreateTable];
    strCreateTable = @"create table IF NOT EXISTS FileUpdateList (serial INTEGER PRIMARY KEY ,FileName TEXT,FileURL TEXT,isDownloaded INTEGER,UpdateVersion TEXT)";
    [[Database shareDatabase] Insert:strCreateTable];*/
}


- (void) createTableForUserDefineSqu {
    
    NSString *strCreateTable = @"create table IF NOT EXISTS CollectionUserDefineFiles (collectionFilesID INTEGER PRIMARY KEY, collectionID Numeric, filesID Numeric, order_file Numeric, isHidden TEXT, isUserDefine TEXT, fileTitle TEXT, isFavourite INTEGER)";
    [[Database shareDatabase] Insert:strCreateTable];
        
    //===================
    // Version 3.0 Changes
    //===================
    // Presentation Sequence Name changes
    strCreateTable = @"create table IF NOT EXISTS CollectionUserDefine (collectionFilesID INTEGER PRIMARY KEY ,collectionID Numeric,sequenceName TEXT, sequenceIndex INTEGER)";
    [[Database shareDatabase] Insert:strCreateTable];
    
}

//===================
// Version 3.1 Changes
//===================
// Feature MVI file changes.
// Note: Create new table for Features.
- (void) createTableForFeatures {
    
    NSString *strCreateTable = @"create table IF NOT EXISTS FeatureMaster (featureID INTEGER PRIMARY KEY, featureName TEXT, imageFilePath TEXT)";
    [[Database shareDatabase] Insert:strCreateTable];
    
}


- (void)callTestwebServiceRemainingVideos {
    
    NSMutableDictionary *body = [[NSMutableDictionary alloc]init];
    
    body[@"method"] = LOGIN;
    
    NSString *aStrNameQuery = [NSString stringWithFormat:@"select * from UserMaster"];
    
    NSMutableArray *aMutArrNames = [[Database sharedDatabase] getAllDataForQuery:aStrNameQuery];
    
    if (aMutArrNames.count > 0) {
        
        body[@"emailID"] = aMutArrNames[0][@"emailId"];
        body[@"password"] = aMutArrNames[0][@"SerialNumber"];
    }
    
    body[@"udid"] = UDIDForWS;
    CallWebService *webservice;
    
    //NSLog(@"%s",__FUNCTION__);
    
    webservice =[[CallWebService alloc] initWithURL:WebserviceURLuserTesting delegate:self args:body key:@"RegisterUser"];
}

- (void)responsedidreceive:(NSMutableData *)response_data forKey:(NSString *)reskey {

    NSString *returnStr = [[NSString alloc]initWithData:response_data encoding:NSUTF8StringEncoding];
    NSDictionary *responsedictionary= [returnStr JSONValue];
    
    //Memory Leaks Commments
    //NSString *aStrResponse = [responsedictionary objectForKey:@"message"];
    //NSString *astrEmail = [[responsedictionary objectForKey:@"user_info"] objectForKey:@"email"];
    int intStatus = [responsedictionary[@"status"] intValue];
    
    if ([reskey isEqualToString:@"RegisterUser"]) {
       
        if(intStatus == 1) {
            
            NSMutableArray *mutArrFullVersion = responsedictionary[@"full_version"];
            
            [self insertIntoDownloadFileList:mutArrFullVersion];
            
            //Memory Leaks Commments
            
            
            NSString *aStrMviFile = responsedictionary[@"placement_determination_file"];
            NSString *aStrFeaturesMviFile = responsedictionary[@"features_file"];
            [[NSUserDefaults standardUserDefaults]setObject:aStrMviFile forKey:MVI_FILE_URL];
            [[NSUserDefaults standardUserDefaults]setObject:aStrFeaturesMviFile forKey:FEATURES_MVI_FILE_URL];
            [[NSUserDefaults standardUserDefaults] synchronize];
            
            //Memory Leaks Commments
           //// [aStrMviFile release];
        }
    }
    NSLog(@"Posting notifiction");
    [[NSNotificationCenter defaultCenter] postNotificationName:@"NewVersionUpdateSuccessfully" object:nil];
}

- (void) insertIntoDownloadFileList:(NSMutableArray *)mutArrFullVersion {
    
    for (int i=0; i < mutArrFullVersion.count; i++) {
        
        NSMutableDictionary *dic = mutArrFullVersion[i];
        BOOL isExist = [self isFileExist:dic[@"file"]];
        NSString *videoFileURL = [NSString stringWithFormat:@"%@/%@",DownloadFileURL,dic[@"file"]];
        NSString *sql = [NSString stringWithFormat:@"insert into FileDownloadList ('FileURL','IsDownloaded','isSkipFile') values ('%@',%d,'0')",videoFileURL,isExist];
        [[Database sharedDatabase] Insert:sql];
        
        isExist = [self isFileExist:dic[@"img"]];
        NSString *imgFileURL = [NSString stringWithFormat:@"%@/%@",DownloadFileURL,dic[@"img"]];
        sql = [NSString stringWithFormat:@"insert into FileDownloadList ('FileURL','IsDownloaded','isSkipFile') values ('%@',%d,'0')",imgFileURL,isExist];
        [[Database sharedDatabase] Insert:sql];
        
        isExist = [self isFileExist:dic[@"html"]];
        NSString *htmlFileURL = [NSString stringWithFormat:@"%@/%@",DownloadFileURL,dic[@"html"]];
        sql = [NSString stringWithFormat:@"insert into FileDownloadList ('FileURL','IsDownloaded','isSkipFile') values ('%@',%d,'0')",htmlFileURL,isExist];
        [[Database sharedDatabase] Insert:sql];
        
    }
}

- (BOOL) isFileExist:(NSString *)fileName {
    
    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES);
    NSString *documentsDir = paths[0];
    
    NSString *dir_path= [documentsDir stringByAppendingPathComponent:@"BiteFXiPadFull/"];
    NSString *filePath = [NSString stringWithFormat:@"%@/%@",dir_path,fileName];
    
    if( [[NSFileManager defaultManager] fileExistsAtPath:filePath]){
        return TRUE;
    }
    else{
        return FALSE;
    }
}

- (BOOL) isVersion:(NSString *)strVersion1 isGraterThan:(NSString *)strVersion2 {
    
    NSArray *arr1 = [strVersion1 componentsSeparatedByString:@"."];
    NSArray *arr2 = [strVersion2 componentsSeparatedByString:@"."];
    
    NSInteger max = MAX([arr1 count], [arr2 count]);
    
    for (int i=0; i < max; i++) {
        NSInteger version1,version2;
       
        if (i < arr1.count) {
            version1 = [arr1[i] integerValue];
        
        } else {
            return FALSE;
        }
        
        if (i < arr2.count) {
            version2 = [arr2[i] integerValue];
        
        } else {
            return FALSE;
        }
        
        if (version1 < version2) {
            return FALSE;
        } else if(version1 > version2){
            return TRUE;
        }
    }
    return FALSE;
}

-(void)hideShowAllImportedImages:(NSString*)srtVal
{
    NSString *aStrMovieGroupName = [NSString stringWithFormat:@"select collectionID from CollectionMaster where isUserDefine = \'1\'"];

    NSMutableArray *mutMovieGroup = [[Database sharedDatabase] getAllDataForQuery:aStrMovieGroupName];
    for (int aIntSeqDetail = 0; aIntSeqDetail < mutMovieGroup.count; aIntSeqDetail++)
    {
        NSString* currCollectionID = mutMovieGroup[aIntSeqDetail][@"collectionID"];
        NSString *aStrUpdateQuery = [NSString stringWithFormat:@"update CollectionFilesMaster SET isHidden = '%@' where collectionID = \'%@\'", srtVal,currCollectionID];
        [[Database sharedDatabase]Update:aStrUpdateQuery];
    }
    NSString *aStrQuery = @"select DISTINCT filesID from CollectionFilesMaster";
    NSMutableArray *aMutArrCollection = [[Database sharedDatabase] getAllDataForQuery:aStrQuery];

    for (int i=0; i< aMutArrCollection.count; i++)
    {
        NSString *aStrQuery = [NSString stringWithFormat:@"select isHidden from CollectionFilesMaster where filesID = \'%@'",aMutArrCollection[i][@"filesID"]];
        NSMutableArray *aArrCollection = [[Database sharedDatabase] getAllDataForQuery:aStrQuery];
        NSString *hide = @"0";
        if (aArrCollection.count > 0)
        {
            hide = aArrCollection[0][@"isHidden"];
            if ([hide isEqualToString:@"(null)"] || hide == nil)
            {
                hide = @"0";
            }
        }
        NSString *aStrUpdateQuery = [NSString stringWithFormat:@"update CollectionUserDefineFiles SET IsHidden = '%@' where filesID = '%@'", hide, aMutArrCollection[i][@"filesID"]];
        [[Database sharedDatabase]Update:aStrUpdateQuery];

    }
}


@end

//@implementation UITextField (NoSelectTextField)
//
//- (BOOL)canPerformAction:(SEL)action withSender:(id)sender {
//    
//    
//    //return NO;
//    
//    if (action == @selector(paste:) ||
//        action == @selector(cut:) ||
//        action == @selector(copy:) ||
//        action == @selector(select:) ||
//        action == @selector(selectAll:) ||
//        action == @selector(delete:) ||
//        action == @selector(makeTextWritingDirectionLeftToRight:) ||
//        action == @selector(makeTextWritingDirectionRightToLeft:) ||
//        action == @selector(toggleBoldface:) ||
//        action == @selector(toggleItalics:) ||
//        action == @selector(toggleUnderline:)
//        ) {
//        return NO;
//    }
//    return [super canPerformAction:action withSender:sender];
//}
//@end
