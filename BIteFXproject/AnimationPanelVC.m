//
//  AnimationPanelVC.m
//  BIteFXproject
//  **********
//  Created by IndiaNIC Infotech Ltd on 19/12/11.
//  Copyright (c) 2011 __MyCompanyName__. All rights reserved.
//  9380-9215 = 165

#import "AnimationPanelVC.h"
#import "AnimationView.h"
#import "AppDelegate.h"
#import "BiteFXMainScreenVC.h"
#import "Database.h"
#import "AFTableViewCell.h"
//#import "UIImageView+ForScrollView.h"
#import "AlwaysOpaqueImageView.h"
#import <QBImagePickerController/QBImagePickerController.h>
#import <SVProgressHUD.h>
#import <PhotosUI/PhotosUI.h>

#define MovieSelectedImg @"Active_Animation.png"
#define MovieUnSelectedImg @"Default_Animation.png"
#define PictureSelectedImg @"Active_Pictures.png"
#define PictureUnSelectedImg @"Default_Pictures.png"
#define PresentSelectedImg @"Active_Presentations.png"
#define PresentUnSelectedImg @"Default_Presentations.png"
#define btnHideImage @"btnHide.png"
#define btnUnHideImage @"btnUnHide.png"

#define IMAGE_NAME @"ImageName"
#define IMAGE @"Image"
#define PICTURES_HIDDEN @"Pictures hidden"

#define SEQUENCE_NAME @"SequenceName"
#define SEQUENCE_INDEX @"SequenceIndex"

#define redColor [UIColor colorWithRed:20.0/255.0 green:91.0/255.0 blue:125.0/255.0 alpha:1.0]
#define animationBGColor [UIColor colorWithRed:255.0/255.0 green:127.0/255.0 blue:0.0/255.0 alpha:1.0]

#define HeightSegment 35

#define NO_USER_IMAGES 3

UIButton *lockbtn;
CGFloat finalX;
CGFloat finalY;
BOOL check_Touch;
BOOL isFilePlayed;


#define RADIANS(degrees) ((degrees * M_PI) / 180.0)

@interface AnimationPanelVC () <QBImagePickerControllerDelegate>

@end

@implementation AnimationPanelVC

@synthesize viewAnimation;
@synthesize mutDictCurrentAnimatedObj;
@synthesize hasSearchResults;

@synthesize mutDictCurrPlaylist;
@synthesize videoPlayDelegate;
@synthesize isLocked;


#define X_View 0
#define Y_View 0
#define width_view 916
#define height_view 700
#define scrlLimit 12;


- (instancetype)initWithNibName:(NSString *)nibNameOrNil bundle:(NSBundle *)nibBundleOrNil {
    self = [super initWithNibName:nibNameOrNil bundle:nibBundleOrNil];
    return self;
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
}

#pragma mark - View lifecycle

- (void)viewDidLoad {
    [super viewDidLoad];
    animationInProgress = NO;
    intSelected =-11;
    intSelectedPresentation = -11;
    cntMovement = 0;
    gestureCount = 0;
    rowRemovedinsequence = FALSE;
    self.view.multipleTouchEnabled = NO;
    isAdded = NO;
    
    iscustomSequenceModified = FALSE;
    imgIndex = 0;
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(handleSearchBarFilteredItemsEmpty:) name:@"SearchBarFilteredItemsEmptyNotification" object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(handleSearchBarFavUnFavTapped:) name:@"SearchBarFavUnFavTapped" object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(btnLockClicked) name:@"StopAnimation" object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(handleSearchArraysDidUpdate:)
                                                 name:@"SearchArraysDidUpdateNotification"
                                               object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(handleSearchSelection:)
                                                 name:@"SearchDidSelectObject"
                                               object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(clearSearchPresentation:)
                                                 name:@"ClearSearchPresentation"
                                               object:nil];
    
    //===================
    // Version 3.0 Changes
    //===================
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(refreshData) name:REFRESH_ANIMATIOM_PANEL_DATA object:nil];
    
    appDelegate.arrScrollViewCount = [[NSMutableArray alloc] init];
    mutArrAnimationData = [[NSMutableArray alloc] init];
    mutArrFilesData = [[NSMutableArray alloc] init];
    mutDicSequence = [[NSMutableDictionary alloc]init];
    dictGropLockImages = [[NSMutableDictionary alloc]init];
    dictGropImages = [[NSMutableDictionary alloc]init];
    mutDictCurrentAnimatedObj = [[NSMutableDictionary alloc] init];
    appDelegate.arrMovie = [[NSMutableArray alloc] init];
    mutDicDisplayMvGroupName = [[NSMutableDictionary alloc] init];
    mutDicDisplayMvGroupFav = [[NSMutableDictionary alloc] init];
    mutArrDisplayMvGroupFavourite = [[NSMutableArray alloc] init];
    appDelegate.arrImage = [[NSMutableArray alloc] init];
    appDelegate.arrLockImage = [[NSMutableArray alloc] init];
    appDelegate.arrUserDefineSeq = [[NSMutableArray alloc]init];
    appDelegate.arrPresentation = [[NSMutableArray alloc] init];
    mutArrSelectedImages = [[NSMutableArray alloc] init];
    appDelegate.arrMovieFavourite = [[NSMutableArray alloc] init];
    appDelegate.arrImageFavourite = [[NSMutableArray alloc] init];
    appDelegate.arrPresentationFavourite = [[NSMutableArray alloc] init];
    appDelegate.arrMedia = [[NSMutableArray alloc] init];
    
    // Version 3.0 Changes
    // Set the animation area height according to thumbnail image size...
    [self setAnimationAreaHeight];
    
    // Create UITableView for Animation area.
    int previousState = appDelegate.intlastResizeViewYAxis;
    // Version 3.0 Changes
    // From feedback point...
    // Condition is added to show atleast one row of user defined presentation...
    if (previousState == 0 || previousState > intAnimationAreaHeight) {
        
        // Version 3.0 change - When the app starts for the first time the text "Drag items to this line" should be visible.
        //        previousState = 73*8.35;
        previousState = intAnimationAreaHeight;
    }
    
    self.tblPartAnimationArea = [[UITableView alloc]initWithFrame:CGRectMake(-1,HeightSegment, self.view.frame.size.width+15, previousState) style:UITableViewStylePlain];
    (self.tblPartAnimationArea).tag = 101;
    (self.tblPartAnimationArea).backgroundColor = [UIColor colorWithRed:56/255.0 green:56/255.0 blue:56/255.0 alpha:1];
    (self.tblPartAnimationArea).separatorStyle = UITableViewCellSeparatorStyleNone;
    
    [self setTableViews:self.tblPartAnimationArea];
    
    // Create UITableView for user define sequence.
    self.tblPartUserSequences = [[UITableView alloc]initWithFrame:CGRectMake(0, previousState+10, self.view.frame.size.width+13,700 - self.tblPartAnimationArea.frame.size.height) style:UITableViewStylePlain];
    (self.tblPartUserSequences).tag = 102;
    (self.tblPartUserSequences).backgroundColor = [UIColor colorWithRed:6/255.0 green:29/255.0 blue:102/255.0 alpha:1];
    (self.tblPartUserSequences).separatorStyle = UITableViewCellSeparatorStyleSingleLine;
    self.tblPartUserSequences.scrollIndicatorInsets = UIEdgeInsetsMake(25.0f, 0.0f, 0.0f, 0.0f);
    (self.tblPartUserSequences).separatorColor = [UIColor blackColor];
    [self setTableViews:self.tblPartUserSequences];
    
    if (@available(iOS 15.0, *)) {
        //        self.tblPartAnimationArea.sectionHeaderTopPadding = 0.0;
        self.tblPartUserSequences.sectionHeaderTopPadding = 0.0;
    }
    
    y = 0;
    X = 101;
    Y = 916;
    UIImageView *aimageFrame = [[UIImageView alloc]initWithFrame:CGRectMake(0, 697-1, 929+5, 50+25)];
    aimageFrame.image = [UIImage imageNamed:@"SliderContainer.png"];
    aimageFrame.backgroundColor = [UIColor clearColor];
    aimageFrame.alpha=0.7;
    
    [self.view addSubview:aimageFrame];
    //    UILabel *lbl = [[UILabel alloc]initWithFrame:CGRectMake(139, 696, 650, 50)];
    // Version 3.0 Changes
    // New frame change for Small, Medium and Large buttons...
    lbl = [[UILabel alloc]initWithFrame:CGRectMake(134, 696, 650, 50)];
    lbl.backgroundColor = [UIColor clearColor];
    lbl.textColor = [UIColor lightGrayColor];
    lbl.alpha = 1;
    lbl.baselineAdjustment = UIBaselineAdjustmentAlignCenters;
    lbl.shadowColor = [UIColor blackColor];
    CGSize size = CGSizeMake(0.5, 1);
    lbl.shadowOffset = size;
    [lbl setOpaque:YES];
    lbl.font = [UIFont boldSystemFontOfSize:14.0];
    
    [self.view addSubview:lbl];
    [self.view bringSubviewToFront:lbl];
    lockbtn = [UIButton buttonWithType:UIButtonTypeCustom];
    lockbtn.tag = 121;
    [lockbtn addTarget:self action:@selector(btnLockClicked) forControlEvents:UIControlEventTouchUpInside];
    //    lockbtn.frame = CGRectMake(800,702,44,44);
    // Version 3.0 Changes
    // New frame change for Small, Medium and Large buttons...
    lockbtn.frame = CGRectMake(671,702,44,44);
    
    [lockbtn setImage:[UIImage imageNamed:@"locked.png"] forState:UIControlStateNormal];
    [self.view addSubview:lockbtn];
    [self.view bringSubviewToFront:lockbtn];
    
    isLocked = YES;
    [[NSUserDefaults standardUserDefaults] setBool:isLocked forKey:@"isLocked"];
    [[NSUserDefaults standardUserDefaults] synchronize];
    
    
    appDelegate.isUpdateDownloading = TRUE;
    
    //===================
    // Version 3.1 Changes
    //===================
    // Feature MVI file changes.
    [self getUserSubscriptionStatus];
    
    [self getDataForAllTabs];
    [self getNoOfCollectionsAndItsContentsUD]; //For Userdefine Sequence
    
    
    appDelegate.arrScrollViewCount = appDelegate.arrImage;
    // Check if all images are unhide...
    [self checkAllImgUnHidden];
    
    [self createSegments];
    //    [self setupCustomSegmentView];
    
    //===================
    // Version 2.4 Changes
    //===================
    wbView = [[InfoWebView alloc] initWithFrame:CGRectMake(0, 30, PRESENTATION_INFO_VIEW_WIDTH, 630)];
    wbView.backgroundColor = [UIColor whiteColor];
    self.viewPresentationInfo = [[UIView alloc] initWithFrame:CGRectMake(0, HeightSegment, PRESENTATION_INFO_VIEW_WIDTH, 660)];
    
    if (appDelegate.intSelectedTab == 2 && appDelegate.isReOpenPresentationInfo) {
        
        NSInteger currPresentationIndex = 0;
        
        // If selected index is greater than data, set last index as selected index...
        if (appDelegate.intSelectedScrNo > appDelegate.arrScrollViewCount.count - 1) {
            currPresentationIndex = (int)(appDelegate.arrScrollViewCount.count - 1);
        }
        else if (appDelegate.intSelectedScrNo > 0) {
            currPresentationIndex = appDelegate.intSelectedScrNo - 1;
        }
        
        [self showPresentationInfo:currPresentationIndex];
    }
    [self addSizeButtons];
    
    
    
}

-(void) addSizeButtons
{
    UIView *btnContainer = [[UIView alloc] initWithFrame:CGRectMake(805, 702, 120, 44)];
    
    imgViewBgSizeButtons = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, btnContainer.frame.size.width, btnContainer.frame.size.height)];
    
    NSString *aStrImageName = @"";
    if (appDelegate.selectedModeSize == 1) {
       aStrImageName = @"small";
    } else if (appDelegate.selectedModeSize == 2) {
       aStrImageName = @"medium";
    } else {
       aStrImageName = @"large";
    }
    
    // Set image according to selected size...
    imgViewBgSizeButtons.image = [UIImage imageNamed:aStrImageName];
    imgViewBgSizeButtons.contentMode = UIViewContentModeScaleAspectFit;
    imgViewBgSizeButtons.clipsToBounds = YES;
    
    // Set small as default size...
//    appDelegate.selectedModeSize = 1;
    [btnContainer addSubview:imgViewBgSizeButtons];
    
    UIButton *btn1 = [[UIButton alloc] initWithFrame:CGRectMake(0, 0, 40, 44)];
    [btn1 setTitle:@"" forState:UIControlStateNormal];
    [btn1 addTarget:self action:@selector(btnSClicked) forControlEvents:UIControlEventTouchUpInside];
    [btn1 setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    
    UIButton *btn2 = [[UIButton alloc] initWithFrame:CGRectMake(40, 0, 40, 44)];
    [btn2 setTitle:@"" forState:UIControlStateNormal];
    [btn2 addTarget:self action:@selector(btnMClicked) forControlEvents:UIControlEventTouchUpInside];
    [btn2 setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    
    UIButton *btn3 = [[UIButton alloc] initWithFrame:CGRectMake(80, 0, 44, 44)];
    [btn3 setTitle:@"" forState:UIControlStateNormal];
    [btn3 addTarget:self action:@selector(btnLClicked) forControlEvents:UIControlEventTouchUpInside];
    [btn3 setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    
    [btnContainer addSubview:btn1];
    [btnContainer addSubview:btn2];
    [btnContainer addSubview:btn3];
    
    [self.view addSubview:btnContainer];
    [self.view bringSubviewToFront:btnContainer];
}

-(void)reloadTblViewsAndUnhide
{
    self.tblPartAnimationArea.hidden = NO;
    self.tblPartUserSequences.hidden = NO;
}

-(void)btnSClicked
{
    [self setThumbSizeChange:1];
}

-(void)btnMClicked
{
    [self setThumbSizeChange:2];
}

-(void)btnLClicked
{
    [self setThumbSizeChange:3];
}

-(void)setThumbSizeChange:(int)index
{
    // Check if same size is selected again...
    if (appDelegate.selectedModeSize == index) {
        return;
    }
    
    NSString *aStrImageName = @"";
    if (index == 1) {
        aStrImageName = @"small";
    } else if (index == 2) {
        aStrImageName = @"medium";
    } else {
        aStrImageName = @"large";
    }
        
    imgViewBgSizeButtons.image = [UIImage imageNamed:aStrImageName];
    appDelegate.selectedModeSize = index;
    
    [self setAnimationAreaHeight];
    
    // Set new frames according to height...
    // This is new change is done for dragable point from feedback...
    CGRect frame = self.tblPartAnimationArea.frame;
    if (frame.size.height > intAnimationAreaHeight) {
        frame.size.height = intAnimationAreaHeight;
    }
    self.tblPartAnimationArea.frame = frame;
    
    CGRect frameT1 = self.tblPartUserSequences.frame;
    frameT1.size.height = (self.view.frame.size.height-50) - self.tblPartAnimationArea.frame.size.height;
    frameT1.origin.y = self.tblPartAnimationArea.frame.size.height;
    self.tblPartUserSequences.frame = frameT1;

    self.tblPartAnimationArea.hidden = YES;
    self.tblPartUserSequences.hidden = YES;
    [self.tblPartAnimationArea reloadData];
    [self.tblPartUserSequences reloadData];
    [self performSelector:@selector(reloadTblViewsAndUnhide) withObject:self afterDelay:0.1];
}

- (void)setAnimationAreaHeight {
    // Version 3.0 Changes
    // Set the animation area height according to thumbnail image size...
    if (appDelegate.selectedModeSize == 1) {
        intAnimationAreaHeight = 600;
    } else if (appDelegate.selectedModeSize == 2) {
        intAnimationAreaHeight = 570;
    } else {
        intAnimationAreaHeight = 540;
    }
}

-(void)getDataForImageSection
{
    [appDelegate.arrImage removeAllObjects];
    selectedTabIndex = 1;
    appDelegate.arrImage = [self getNoOfCollectionsAndItsContents];
    
    if (appDelegate.arrImage.count > 0 && appDelegate.arrImageCopy.count == 0) {
        appDelegate.arrImageCopy = appDelegate.arrImage;
    }
    
    if (appDelegate.selectedObj == nil) {
        // Manage favourite array
        NSArray *filteredImages = [self filterFavouritesInArray:appDelegate.arrImage];
        appDelegate.arrImageFavourite = [filteredImages mutableCopy];
    }
    
    appDelegate.btnHideUnhideImages.hidden = (appDelegate.arrImage.count == NO_USER_IMAGES) ? YES : NO;
    
    NSMutableArray *extraObj = [[NSMutableArray alloc] initWithObjects:[self addExtraobjectForImage], nil];
    [appDelegate.arrImage addObject:extraObj];
    
    appDelegate.arrImage = self.hasSearchResults ? appDelegate.arrSearchImage : appDelegate.arrImageCopy;
    
    if (self.hasSearchResults && appDelegate.intSelectedTab == 1) {
        [self setImageTabSelected];
    }
}

//===================
// Version 3.1 Changes
//===================
// Feature MVI file changes.
// New seperate functions created to get data for each section which can further reused in other functionalities...
-(void)getDataForMovieSection {
    [appDelegate.arrMovie removeAllObjects];
    selectedTabIndex = 0;
    appDelegate.arrMovie = [self getNoOfCollectionsAndItsContents];
    if (appDelegate.arrMovie.count > 0 && appDelegate.arrMovieCopy.count == 0) {
        appDelegate.arrMovieCopy = appDelegate.arrMovie;
    }
    
    if (appDelegate.selectedObj == nil) {
        // Manage favourite array
        NSArray *filteredMovie = [self filterFavouritesInArray:appDelegate.arrMovie];
        appDelegate.arrMovieFavourite = [filteredMovie mutableCopy];
    }
    
    appDelegate.arrMovie = self.hasSearchResults ? appDelegate.arrSearchMovie : appDelegate.arrMovieCopy;
    
    if ((self.hasSearchResults && appDelegate.intSelectedTab == 0) || appDelegate.selectedObj == nil) {
        [self setMovieTabSelected];
    }
}

-(void)getDataForPresentationSection:(BOOL)reload {
    // reload >> added to show favourite presentation template
    [appDelegate.arrPresentation removeAllObjects];
    selectedTabIndex = 2;
    appDelegate.arrPresentation = [self getNoOfCollectionsAndItsContents];
    mutArrDisplayMvGroupFavourite = (NSMutableArray*)[mutDicDisplayMvGroupFav allKeysForObject:@"1"];
    if (appDelegate.arrPresentation.count > 0 && appDelegate.arrPresentationCopy.count == 0) {
        appDelegate.arrPresentationCopy = appDelegate.arrPresentation;
    }
    
//    if (appDelegate.selectedObj == nil) {
//        // Manage favourite array
//        NSArray *filteredPresentation = [self filterFavouritesInArray:appDelegate.arrPresentation];
//        appDelegate.arrPresentationFavourite = [filteredPresentation mutableCopy];
//    }
    
    NSMutableArray *favouriteArray = [NSMutableArray array];
    for (NSArray *collection in appDelegate.arrPresentation) {
        NSMutableArray *favouriteArray1 = [NSMutableArray array];
        NSUInteger index = [collection indexOfObjectPassingTest:^BOOL(NSDictionary *obj, NSUInteger idx, BOOL *stop) {
            return [mutArrDisplayMvGroupFavourite containsObject:obj[@"collectionID"]];
        }];
        if (index != NSNotFound) {
            favouriteArray1 = (NSMutableArray*)[collection subarrayWithRange:NSMakeRange(index, collection.count - index)];
        }
        [favouriteArray addObject:favouriteArray1];
    }
    
    NSMutableArray *tempArr1 = [NSMutableArray array];
    for (NSArray *arr in favouriteArray) {
        if (arr.count > 0) {
            [tempArr1 addObject:arr];
        }
    }
    appDelegate.arrPresentationFavourite = tempArr1;
    if (reload) {
        appDelegate.arrScrollViewCount = appDelegate.arrPresentation;
    }
    
    if (reload) {
        return;
    }
    if ((self.hasSearchResults && appDelegate.intSelectedTab == 2) || appDelegate.selectedObj == nil) {
        [self setPresentationTabSelected];
    }
}

-(void)getDataForAllTabs {
    
    [self getDataForMovieSection];
    [self getDataForImageSection];
    [self getDataForPresentationSection:NO];
}

-(NSMutableDictionary*)addExtraobjectForImage
{
    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES);
    NSString *documentsDir = paths[0];
    NSMutableDictionary *dicExtraRow = [[NSMutableDictionary alloc]init];
    [dicExtraRow setValue:@"" forKey:@"fileShortDispName"];
    [dicExtraRow setValue:@"-100" forKey:@"collectionID"];
    dicExtraRow[@"thumbnailPath"] = [NSString stringWithFormat:@"%@/AddMoreImage.png",documentsDir];
    [dicExtraRow setValue:@"" forKey:@"fileName"];
    [dicExtraRow setValue:@"1" forKey:@"isUserDefine"];
    return dicExtraRow;
}

-(void)setTableViews:(UITableView*)myTblView
{
    myTblView.separatorInset = UIEdgeInsetsZero;
    [myTblView setShowsHorizontalScrollIndicator:NO];
    [myTblView setShowsVerticalScrollIndicator:YES];
    myTblView.indicatorStyle = UIScrollViewIndicatorStyleWhite;
    myTblView.dataSource = self;
    myTblView.delegate = self;
    [self.view addSubview:myTblView];
}

-(void)createSegments
{
    UIView *mySegmentView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, self.view.frame.size.width+13, HeightSegment)];
    NSInteger segmentWidth = mySegmentView.frame.size.width/4;
    NSInteger segmentHeight = mySegmentView.frame.size.height;
    btnMovie = [[UIButton alloc] initWithFrame:CGRectMake(0, 0, segmentWidth, segmentHeight)];
    btnMovie.tag = 0;
    btnPicture = [[UIButton alloc] initWithFrame:CGRectMake(segmentWidth, 0, segmentWidth, segmentHeight)];
    btnPicture.tag = 1;
    btnPresentation = [[UIButton alloc] initWithFrame:CGRectMake(segmentWidth*2, 0, segmentWidth, segmentHeight)];
    btnPresentation.tag = 2;
    
    searchbarBaseView = [[SearchBar alloc] initWithFrame:CGRectMake(segmentWidth*3, 0, segmentWidth, segmentHeight)];
//       btnPresentation.tag = 3;

    [btnMovie setBackgroundImage:[UIImage imageNamed:MovieSelectedImg] forState:UIControlStateSelected];
    [btnMovie setBackgroundImage:[UIImage imageNamed:MovieUnSelectedImg] forState:UIControlStateNormal];
    [btnPicture setBackgroundImage:[UIImage imageNamed:PictureSelectedImg] forState:UIControlStateSelected];
    [btnPicture setBackgroundImage:[UIImage imageNamed:PictureUnSelectedImg] forState:UIControlStateNormal];
    [btnPresentation setBackgroundImage:[UIImage imageNamed:PresentSelectedImg] forState:UIControlStateSelected];
    [btnPresentation setBackgroundImage:[UIImage imageNamed:PresentUnSelectedImg] forState:UIControlStateNormal];

    [btnMovie addTarget:self action:@selector(MySegmentControlAction:) forControlEvents:UIControlEventTouchUpInside];
    [btnPicture addTarget:self action:@selector(MySegmentControlAction:) forControlEvents:UIControlEventTouchUpInside];
    [btnPresentation addTarget:self action:@selector(MySegmentControlAction:) forControlEvents:UIControlEventTouchUpInside];

    [mySegmentView addSubview:btnMovie];
    [mySegmentView addSubview:btnPicture];
    [mySegmentView addSubview:btnPresentation];
    [mySegmentView addSubview:searchbarBaseView];
    [self.view addSubview:mySegmentView];
    [self.view bringSubviewToFront:mySegmentView];
    mySegmentView.backgroundColor = [UIColor clearColor];

    if (appDelegate.intSelectedTab == 0)
    {
        [self MySegmentControlAction:btnMovie];
    }
    else if (appDelegate.intSelectedTab == 1)
    {
        [self MySegmentControlAction:btnPicture];
    }
    else
    {
        [self MySegmentControlAction:btnPresentation];
    }
}

- (void)setupCustomSegmentView {
    CGFloat totalWidth = self.view.frame.size.width + 13;
    CGFloat segmentHeight = HeightSegment;

    // Main container view
    UIView *mainSegmentView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, totalWidth, segmentHeight)];
    [self.view addSubview:mainSegmentView];

    // Top section (2/3 width)
    CGFloat topWidth = totalWidth * 2.2 / 3.0;
    UIView *topSectionView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, topWidth, segmentHeight)];
    [mainSegmentView addSubview:topSectionView];

    // Bottom section (1/3 width)
    CGFloat bottomWidth = totalWidth - topWidth;
    UIView *bottomSectionView = [[UIView alloc] initWithFrame:CGRectMake(topWidth, 0, bottomWidth, segmentHeight)];
    [mainSegmentView addSubview:bottomSectionView];

    // === typeView inside topSectionView ===
    UIView *typeView = [[UIView alloc] initWithFrame:topSectionView.bounds];
    UIBezierPath *maskPath = [UIBezierPath bezierPathWithRoundedRect:typeView.bounds
                                                   byRoundingCorners:UIRectCornerTopLeft
                                                         cornerRadii:CGSizeMake(10.0, 10.0)];

    CAShapeLayer *maskLayer = [CAShapeLayer layer];
    maskLayer.frame = typeView.bounds;
    maskLayer.path = maskPath.CGPath;
    typeView.layer.mask = maskLayer;
    typeView.layer.borderColor = [UIColor colorWithRed:(253.0/255.0)
                                                  green:(204.0/255.0)
                                                   blue:(86.0/255.0)
                                                  alpha:1.0].CGColor;
    typeView.layer.borderWidth = 0.0;
    [topSectionView addSubview:typeView];

    // Left view (90%) with 3 buttons
    CGFloat leftWidth = typeView.frame.size.width * 0.93;
    UIView *leftView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, leftWidth, segmentHeight)];
    [typeView addSubview:leftView];

    // Right view (10%) with 1 extra button
    CGFloat rightWidth = typeView.frame.size.width - leftWidth;
    UIView *rightView = [[UIView alloc] initWithFrame:CGRectMake(leftWidth, 0, rightWidth, segmentHeight)];
    [typeView addSubview:rightView];

    // Buttons in leftView
    CGFloat buttonWidth = leftView.frame.size.width / 3;

    btnMovie = [[UIButton alloc] initWithFrame:CGRectMake(0, 0, buttonWidth, segmentHeight)];
    btnMovie.tag = 0;
    [btnMovie setBackgroundImage:[UIImage imageNamed:MovieSelectedImg] forState:UIControlStateSelected];
    [btnMovie setBackgroundImage:[UIImage imageNamed:MovieUnSelectedImg] forState:UIControlStateNormal];
    [btnMovie addTarget:self action:@selector(MySegmentControlAction:) forControlEvents:UIControlEventTouchUpInside];
    [leftView addSubview:btnMovie];

    btnPicture = [[UIButton alloc] initWithFrame:CGRectMake(buttonWidth, 0, buttonWidth, segmentHeight)];
    btnPicture.tag = 1;
    [btnPicture setBackgroundImage:[UIImage imageNamed:PictureSelectedImg] forState:UIControlStateSelected];
    [btnPicture setBackgroundImage:[UIImage imageNamed:PictureUnSelectedImg] forState:UIControlStateNormal];
    [btnPicture addTarget:self action:@selector(MySegmentControlAction:) forControlEvents:UIControlEventTouchUpInside];
    [leftView addSubview:btnPicture];

    btnPresentation = [[UIButton alloc] initWithFrame:CGRectMake(buttonWidth * 2, 0, buttonWidth, segmentHeight)];
    btnPresentation.tag = 2;
    [btnPresentation setBackgroundImage:[UIImage imageNamed:PresentSelectedImg] forState:UIControlStateSelected];
    [btnPresentation setBackgroundImage:[UIImage imageNamed:PresentUnSelectedImg] forState:UIControlStateNormal];
    [btnPresentation addTarget:self action:@selector(MySegmentControlAction:) forControlEvents:UIControlEventTouchUpInside];
    [leftView addSubview:btnPresentation];

    // Extra button in rightView
    UIButton *btnExtra = [[UIButton alloc] initWithFrame:rightView.bounds];
    [btnExtra setTitle:@"+" forState:UIControlStateNormal];
    [btnExtra setTitleColor:UIColor.blackColor forState:UIControlStateNormal];
//    [rightView addSubview:btnExtra];

    // === Add searchBarBaseView to bottom section ===
    searchbarBaseView = [[SearchBar alloc] initWithFrame:bottomSectionView.bounds];
    [bottomSectionView addSubview:searchbarBaseView];

    // Optional: bring to front
    [self.view bringSubviewToFront:mainSegmentView];
}


- (void)MySegmentControlAction:(UIButton *)sender
{
    
    // If animations segment change, first object of first row is default selected...
    //    if (appDelegate.intSelectedTab != sender.tag && ([[NSUserDefaults standardUserDefaults]boolForKey:@"PlayAnimation"])) {
    //
    //        appDelegate.intSelectedScrNo = 0;
    //        appDelegate.intSelectedVideo = 0;
    //    }
    
    appDelegate.intSelectedTab = sender.tag;
    // Remove presentation info view...
    if (appDelegate.isPresentationInfoOpen && appDelegate.intSelectedTab != 2) {
        
        [self btnCloseInfoClick:nil];
    }
    
    if(sender.tag == 0)
    {
        //===================
        // Version 3.1 Changes
        //===================
        // Feature MVI file changes.
        // Remove FeatureSetsView for Movie Tab...
        [self removeFeatureSetDropDownView];
        [self setMovieTabSelected];
    }
    else if(sender.tag == 1)
    {
        //===================
        // Version 3.1 Changes
        //===================
        // Feature MVI file changes.
        // Remove FeatureSetsView for Image Tab...
        [self removeFeatureSetDropDownView];
        [self setImageTabSelected];
    }
    else if(sender.tag == 2)
    {
        //===================
        // Version 3.1 Changes
        //===================
        // Feature MVI file changes.
        // Show FeatureSetDropDown for Presentations Tab...
        [self checkAndShowFeatureSetMainOrDetailView];
        [self setPresentationTabSelected];
    }
    
    [appDelegate.btnHideUnhideImages setUserInteractionEnabled:YES];
    (appDelegate.btnHideUnhideImages).alpha = 1.0;
    [self initWithUI];
}

-(void)setMovieTabSelected
{
    [btnMovie setSelected:YES];
    [btnPicture setSelected:NO];
    [btnPresentation setSelected:NO];
    mutDicDisplayMvGroupName = dictGropMovie;

    if (appDelegate.selectedObj != nil) {
        appDelegate.arrScrollViewCount = appDelegate.arrSelectedMovie;
    } else if (hasSearchResults) {
        appDelegate.arrScrollViewCount = appDelegate.arrSearchMovie.count > 0 ? appDelegate.arrSearchMovie : [[NSMutableArray alloc] init];
    } else {
        appDelegate.arrScrollViewCount = appDelegate.isFavourite ? appDelegate.arrMovieFavourite : appDelegate.arrMovieCopy;
    }
    
    [self.tblPartAnimationArea reloadData];
}

-(void)setImageTabSelected
{
    [btnMovie setSelected:NO];
    [btnPicture setSelected:YES];
    [btnPresentation setSelected:NO];
    //    if (isLocked)
    //    {
    mutDicDisplayMvGroupName = dictGropImages;
    
    if (appDelegate.selectedObj != nil) {
        appDelegate.arrScrollViewCount = appDelegate.arrSelectedImage;
    } else if (hasSearchResults) {
        appDelegate.arrScrollViewCount = appDelegate.arrSearchImage.count > 0 ? appDelegate.arrSearchImage : [[NSMutableArray alloc] init];
    } else {
        appDelegate.arrScrollViewCount = appDelegate.isFavourite ? appDelegate.arrImageFavourite : appDelegate.arrImageCopy;
    }
    
    [self.tblPartAnimationArea reloadData];
}

-(void)setPresentationTabSelected
{
    [btnMovie setSelected:NO];
    [btnPicture setSelected:NO];
    [btnPresentation setSelected:YES];
    mutDicDisplayMvGroupName = dictGropPresentation;
    mutArrDisplayMvGroupFavourite = (NSMutableArray*)[mutDicDisplayMvGroupFav allKeysForObject:@"1"];
    
    NSMutableArray *favouriteArray = [NSMutableArray array];
    for (NSArray *collection in appDelegate.arrPresentation) {
        NSMutableArray *favouriteArray1 = [NSMutableArray array];
        NSUInteger index = [collection indexOfObjectPassingTest:^BOOL(NSDictionary *obj, NSUInteger idx, BOOL *stop) {
            return [mutArrDisplayMvGroupFavourite containsObject:obj[@"collectionID"]];
        }];
        if (index != NSNotFound) {
            favouriteArray1 = (NSMutableArray*)[collection subarrayWithRange:NSMakeRange(index, collection.count - index)];
        }
        [favouriteArray addObject:favouriteArray1];
    }
    
    NSMutableArray *tempArr1 = [NSMutableArray array];
    for (NSArray *arr in favouriteArray) {
        if (arr.count > 0) {
            [tempArr1 addObject:arr];
        }
    }
    appDelegate.arrPresentationFavourite = tempArr1;
    
    appDelegate.arrScrollViewCount = appDelegate.isFavourite ? appDelegate.arrPresentationFavourite : appDelegate.arrPresentation;
    
    if (self.hasSearchResults) {
        NSLog(@"appDelegate.arrPresentation>>> %@", appDelegate.arrPresentation);
        NSLog(@"appDelegate.arrSearchPresentation>>> %@", appDelegate.arrSearchPresentation);
        
        // Create new array to store matching objects
        NSMutableArray *matchingObjects = [[NSMutableArray alloc] init];
        
        // Check each object in search results against presentation array
        for (NSArray *searchCollection in appDelegate.arrSearchPresentation) {
            NSMutableArray *matchingCollection = [[NSMutableArray alloc] init];
            for (NSDictionary *searchObj in searchCollection) {
                // Remove color key from search objects to avoid matching issues
                NSMutableDictionary *searchObjWithoutColor = [searchObj mutableCopy];
                [searchObjWithoutColor removeObjectForKey:@"color"];
                for (NSArray *presentationCollection in appDelegate.arrPresentation) {
                    if ([presentationCollection containsObject:searchObjWithoutColor]) {
                        [matchingObjects addObject:presentationCollection];
                        break;
                    }
                }
            }
            
            if (matchingCollection.count > 0) {
                [matchingObjects addObject:matchingCollection];
            }
        }
        
        appDelegate.arrSearchPresentation = matchingObjects;
        appDelegate.arrScrollViewCount = (NSMutableArray*)[NSSet setWithArray:appDelegate.arrSearchPresentation];
        
        NSLog(@"appDelegate.arrScrollViewCount>>> %@", appDelegate.arrScrollViewCount);
        
        NSMutableDictionary *mutDicDisplayMvGroupNameCopy = [mutDicDisplayMvGroupName mutableCopy];
        mutDicDisplayMvGroupName = [[NSMutableDictionary alloc] init];
        mutDicDisplayMvGroupFav = [[NSMutableDictionary alloc] init];
        mutArrDisplayMvGroupFavourite = [[NSMutableArray alloc] init];
        
        for (int aIntSeqDetail = 0; aIntSeqDetail < appDelegate.arrSearchPresentation.count; aIntSeqDetail++)
        {
            NSDictionary * obj = appDelegate.arrSearchPresentation[aIntSeqDetail][0];
    
            NSString* str = [NSString stringWithFormat:@"%@",obj[@"collectionID"]];
            str = [NSString stringWithFormat:@"%@", [mutDicDisplayMvGroupNameCopy objectForKey:str]];
            
            [mutDicDisplayMvGroupName setValue:str forKey:[NSString stringWithFormat:@"%@",obj[@"collectionID"]]];
            
            NSString* isFavourite = [NSString stringWithFormat:@"%@",obj[@"isFavourite"]];
            [mutDicDisplayMvGroupFav setValue:isFavourite forKey:[NSString stringWithFormat:@"%@",obj[@"collectionID"]]];
            NSLog(@"mutDicDisplayMvGroupName>>> %@", mutDicDisplayMvGroupName);
        }
        
        dictGropPresentation = [mutDicDisplayMvGroupFav mutableCopy];
        mutArrDisplayMvGroupFavourite = (NSMutableArray*)[mutDicDisplayMvGroupFav allKeysForObject:@"1"];
    }
    NSLog(@"appDelegate.arrScrollViewCount>>> %@", appDelegate.arrScrollViewCount);
    
    [self.tblPartAnimationArea reloadData];
}

-(NSMutableArray*)setDataIntoAnimationArr:(NSMutableArray*)myArr
{
    for (int index = 0; index < myArr.count; index ++) {
        NSMutableArray *arrScroll = [[NSMutableArray alloc] init];
        for (int count = 0; count < [myArr[index] intValue]; count++)
        {
            [arrScroll addObject:mutArrAnimationData[index][count]];
        }
        
        NSString *aStrUserDefine = [mutArrAnimationData[index][0] valueForKey:@"isUserDefine"];
        
        // Add "+" button for User defined albums...
        if(selectedTabIndex == 1 && [aStrUserDefine isEqualToString:@"1"])
        {
            [arrScroll addObject:[self addExtraobjectForImage]];
        }
        myArr[index] = arrScroll;

    }
    return myArr;
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:YES];
    [self.tblPartAnimationArea flashScrollIndicators];
    [self.tblPartUserSequences flashScrollIndicators];
    if (self.tblPartUserSequences.frame.size.height > self.tblPartUserSequences.contentSize.height) {
        [self.tblPartUserSequences endUpdates];
        self.tblPartUserSequences.contentSize = CGSizeMake(self.tblPartUserSequences.frame.size.width,self.tblPartUserSequences.frame.size.height);
        [self.tblPartUserSequences layoutIfNeeded];
        [self.tblPartUserSequences flashScrollIndicators];
    }
    
    for (UIView *view in self.tblPartAnimationArea.subviews) {
        if ([view isKindOfClass:[UIImageView class]]) {
            object_setClass(view, [AlwaysOpaqueImageView class]);
            break;
        }
    }
    
    for (UIView *view in self.tblPartUserSequences.subviews) {
        if ([view isKindOfClass:[UIImageView class]]) {
            object_setClass(view, [AlwaysOpaqueImageView class]);
            break;
        }
    }
    for (UIView *view in self.view.subviews) {
        if ([view isKindOfClass:[UIImageView class]]) {
            view.alpha = 1;
            break;
        }
    }
    
    [self.tblPartAnimationArea reloadData];

//    for (UIView *view in self.view.subviews) {
//        if ([view isKindOfClass:[UIImageView class]]) {
//            object_setClass(view, [AlwaysOpaqueImageView class]);
//            break;
//        }
//    }



//    for (UIView *view in wbView.subviews) {
//
//            UIScrollView *vw = (UIScrollView*)view;
//            object_setClass(vw, [AlwaysOpaqueImageView class]);
//            [vw flashScrollIndicators];
//            break;
//
//    }
}
-(void)viewDidDisappear:(BOOL)animated{
    [super viewDidDisappear:animated];
    
    if (appDelegate.mutArrPlayVideo.count == 0) {
        appDelegate.mutArrPlayVideo = (appDelegate.arrScrollViewCount)[0];
        appDelegate.intSelectedScrNo = 1;
    }
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:YES];
    [appDelegate.btnHideUnhideImages removeFromSuperview];
    [self.view addSubview:appDelegate.btnHideUnhideImages];
    [self.view bringSubviewToFront:appDelegate.btnHideUnhideImages];
    [appDelegate.btnHideUnhideImages addTarget:self action:@selector(btnHideUnhideImagesClicked:) forControlEvents:UIControlEventTouchUpInside];
    /*
    NSIndexPath* aPrevState = [NSIndexPath indexPathForRow:appDelegate.intlastPlayVideoState inSection:0];
    if ([[NSUserDefaults standardUserDefaults]boolForKey:@"PlayAnimation"]){
        [self.tblPartAnimationArea scrollToRowAtIndexPath:aPrevState atScrollPosition:UITableViewScrollPositionTop animated:YES];
    }else{
        [self.tblPartUserSequences scrollToRowAtIndexPath:aPrevState atScrollPosition:UITableViewScrollPositionTop animated:YES];
    }
    NSMutableArray *arr = [[NSMutableArray alloc]init];
    for (int i = 0; i < [appDelegate.arrScrollViewCount count]; i++) {
        if ([(NSMutableArray*)[appDelegate.arrScrollViewCount objectAtIndex:i]count]> 0) {
        }else{
            [arr addObject:[appDelegate.arrScrollViewCount objectAtIndex:i]];
        }
    }
    for (NSArray *arry in arr) {
        [appDelegate.arrScrollViewCount removeObject:arry];
    }
    [arr release];
     */
}

-(void)setLockBtnYes
{
    isLocked=YES;
    [lockbtn setImage:[UIImage imageNamed:@"locked.png"] forState:UIControlStateNormal];
    [[NSUserDefaults standardUserDefaults] setBool:isLocked forKey:@"isLocked"];
    [[NSUserDefaults standardUserDefaults] synchronize];
}

- (void)viewWillDisappear:(BOOL)animated {
    
    [super viewWillDisappear:YES];
    [appDelegate.btnHideUnhideImages removeTarget:self action:@selector(btnHideUnhideImagesClicked:) forControlEvents:UIControlEventTouchUpInside];
    [appDelegate.btnHideUnhideImages removeFromSuperview];
    [self setLockBtnYes];
    
}

- (void)initWithUI {
//    isLocked=YES;
//    [lockbtn setImage:[UIImage imageNamed:@"locked.png"] forState:UIControlStateNormal];
//
//    [[NSUserDefaults standardUserDefaults] setBool:isLocked forKey:@"isLocked"];
//    [[NSUserDefaults standardUserDefaults] synchronize];

    [self setSelectedIndexColor];
}

- (void)setSelectedIndexColor {
    
    [self clearAllColor];
    
    int isUpdateDwnlded = (int)[[NSUserDefaults standardUserDefaults]integerForKey:@"UpdateDownloaded"];
    
    for(int i=0;i<(appDelegate.arrScrollViewCount).count;i++) {
        
        for(int j=0;j< [(appDelegate.arrScrollViewCount)[i]count];j++) {
            
            if(isUpdateDwnlded) { // (isUpdateDwnlded is 0 after update installed)
                NSString *aStrplayed = (appDelegate.arrScrollViewCount)[i][j][@"isPlayed"];
                if([(appDelegate.arrScrollViewCount)[i][j][@"isDownloaded"] isEqualToString:@"1"] && [aStrplayed isEqualToString:@"0"]) {
                    (appDelegate.arrScrollViewCount)[i][j][@"color"] = redColor; // Blue
                    
                } else {
                    (appDelegate.arrScrollViewCount)[i][j][@"color"] = [UIColor clearColor];
                }
                
            }
        }
    }

    // If selected index is in Upper tableview and selected tab is same as previous one set selected index color...
    if (appDelegate.intPrevSelectedTab == appDelegate.intSelectedTab)
    {
        if((appDelegate.mutArrPlayVideo).count>0) {
            if ([[NSUserDefaults standardUserDefaults]boolForKey:@"PlayAnimation"]) {

                // Selected index in tblPartAnimationArea (If selected index is in Upper tableview)...
                if (appDelegate.intSelectedScrNo == 0) {
                    appDelegate.intSelectedScrNo = 1;
                }
                
                // Version 3.0 Changes
                // To solve crash issue - if selected index is greater than data, set last index as selected index...
                if (appDelegate.intSelectedScrNo > appDelegate.arrScrollViewCount.count) {
                    appDelegate.intSelectedScrNo = (int)(appDelegate.arrScrollViewCount.count);
                }
                
                
                if (appDelegate.selectedObj == nil) {
                    if (appDelegate.arrScrollViewCount.count > 0) {
                        NSArray *arrData = (appDelegate.arrScrollViewCount)[appDelegate.intSelectedScrNo - 1];
                        if (appDelegate.intSelectedVideo < arrData.count) {

                            (appDelegate.arrScrollViewCount)[appDelegate.intSelectedScrNo - 1][appDelegate.intSelectedVideo][@"color"] = animationBGColor;  // Orange

                        }
                    }
                }
            }else{

                // Previous Code....
            }

        } else {

            (appDelegate.arrScrollViewCount)[appDelegate.intSelectedScrNo][appDelegate.intSelectedVideo][@"color"] = animationBGColor;  // Orange
        }
    }
    
    // If selected index is in bottom tableview set selected index color...
    int aIntPlayAnimation = [[NSUserDefaults standardUserDefaults]boolForKey:@"PlayAnimation"];
    if((appDelegate.mutArrPlayVideo).count>0 && !aIntPlayAnimation) {
        
        // Selected index in tblPartUserSequences (If selected index is in bottom tableview)...
        if (appDelegate.arrUserDefineSeq.count == 1)
        {
            [[NSUserDefaults standardUserDefaults]setBool:YES forKey:@"PlayAnimation"];
            appDelegate.intSelectedScrNo = 1;
            appDelegate.intSelectedVideo = 0;
            (appDelegate.arrScrollViewCount)[appDelegate.intSelectedScrNo - 1][appDelegate.intSelectedVideo][@"color"] = animationBGColor;  // Orange
        }
        else
        {
            if (appDelegate.intSelectedScrNo == 0) {
                appDelegate.intSelectedScrNo = 1;
            }
            
            // If selected index is greater than data, set last index as selected index...
            if (appDelegate.intSelectedScrNo > appDelegate.arrUserDefineSeq.count - 1) {
                appDelegate.intSelectedScrNo = (int)(appDelegate.arrUserDefineSeq.count - 1);
            }
            
            NSArray *arrData = (appDelegate.arrUserDefineSeq)[appDelegate.intSelectedScrNo - 1];
            
            // If selected video is greater than data, set last index as selected video...
            if (appDelegate.intSelectedVideo > arrData.count - 1) {
                appDelegate.intSelectedVideo = (int)(arrData.count - 1);
            }
            
            (appDelegate.arrUserDefineSeq)[appDelegate.intSelectedScrNo - 1][appDelegate.intSelectedVideo][@"color"] = animationBGColor;  // Orange
        }
        
    }
    
    [self.tblPartAnimationArea reloadData];
    [self.tblPartUserSequences reloadData];
    
    // Scroll to selected index in tblPartAnimationArea (If selected index is in Upper tableview)...
    if (appDelegate.intPrevSelectedTab == appDelegate.intSelectedTab && appDelegate.isScrollToSelectedIndex && aIntPlayAnimation && appDelegate.arrScrollViewCount.count > 0) {
        
        int aIntIndex = 0;
        
        if (appDelegate.intSelectedScrNo > 0) {
            aIntIndex = appDelegate.intSelectedScrNo - 1;
        }
        
        NSIndexPath *aIndexPath = [NSIndexPath indexPathForRow:aIntIndex inSection:0];
        [self.tblPartAnimationArea scrollToRowAtIndexPath:aIndexPath atScrollPosition:UITableViewScrollPositionMiddle animated:NO];

    }
}

- (void)addExtraRowForAddingPlaySeq{
    [appDelegate.arrUserDefineSeq addObject:[self forExtraRow]];
}

- (NSMutableArray*)forExtraRow{
    NSMutableDictionary *dicExtraRow = [[NSMutableDictionary alloc]init];
    [dicExtraRow setValue:@"Drag items to this line" forKey:@"fileShortDispName"];
    [dicExtraRow setValue:@"RemoveKey" forKey:@"isExtra"];
    [dicExtraRow setValue:@"SliderContainer.png" forKey:@"thumbnailPath"];
    [dicExtraRow setValue:@"Drag items to this line" forKey:@"fileName"];
    NSMutableArray *arrExtraRow = [[NSMutableArray alloc]init];
    [arrExtraRow addObject:dicExtraRow];
    return arrExtraRow;
}

- (void)resizingAnimationAndUDSequence:(id)sender{
    if(((UIPanGestureRecognizer*)sender).state == UIGestureRecognizerStateChanged) {
        [_tblPartUserSequences setScrollEnabled:NO];
        CGPoint translatedPoint = [(UIPanGestureRecognizer*)sender translationInView:self.view];
        CGPoint velocity = [sender velocityInView:self.tblPartAnimationArea];
        
        if ([sender view].tag == 101) {
            [self.tblPartAnimationArea beginUpdates];
            CGRect frame = self.tblPartAnimationArea.frame;
            if (velocity.y > 0 && tb1_previousY > translatedPoint.y) {
                
                if (tb1_previousY > translatedPoint.y && tb1_previousY > 0) {
                    frame.size.height = frame.size.height + translatedPoint.y;
                }else if (tb1_previousY < translatedPoint.y && tb1_previousY < 0){
                    frame.size.height = frame.size.height + translatedPoint.y;
                }else {
                    frame.size.height = frame.size.height + translatedPoint.y - tb1_previousY;
                }
            }else{
                
                frame.size.height = frame.size.height + translatedPoint.y - (tb1_previousY);
            }
            
            frame.origin.y = HeightSegment;  // sk
            
            if (frame.size.height < 73) {
                frame.size.height = 73;
                (self.tblPartAnimationArea).contentSize = CGSizeMake(8*45, 0);
            }else if (frame.size.height > intAnimationAreaHeight){
                frame.size.height = intAnimationAreaHeight;
            }
            self.tblPartAnimationArea.frame = frame;
            [self.tblPartAnimationArea endUpdates];
            
            [self.tblPartUserSequences beginUpdates];
            CGRect frameT1 = self.tblPartUserSequences.frame;
            frameT1.size.height = (self.view.frame.size.height-50) - self.tblPartAnimationArea.frame.size.height;
            frameT1.origin.y = self.tblPartAnimationArea.frame.size.height;
            self.tblPartUserSequences.frame = frameT1;
            [self.tblPartUserSequences endUpdates];
            tb1_previousY = translatedPoint.y;
            
        }else{
            [self.tblPartUserSequences beginUpdates];
            CGRect frame = self.tblPartUserSequences.frame;
            
            frame.size.height = (frame.size.height - translatedPoint.y) + tb2_previousY;
            frame.origin.y = (frame.origin.y + translatedPoint.y)-tb2_previousY;
            if (frame.size.height < 100) {
                frame.size.height = 100;
                frame.origin.y = 595;
            }
            
            if (frame.origin.y < 105) {
                frame.origin.y = 105;
                frame.size.height = 595;
                
            }
            
            self.tblPartUserSequences.frame = frame;
            [self.tblPartUserSequences endUpdates];
            tb2_previousY = translatedPoint.y;
        }
    }
    
    if(((UIPanGestureRecognizer*)sender).state == UIGestureRecognizerStateEnded)
    {
        appDelegate.intlastResizeViewYAxis = self.tblPartAnimationArea.frame.size.height;
        if ([sender view].tag == 101) {
            tb1_previousY = 0;
        }else{
            tb2_previousY = 0;
        }
        [_tblPartUserSequences setScrollEnabled:YES];
    }
}
/*
- (void)viewDidLayoutSubviews
{
    [super viewDidLayoutSubviews];
    if (self.tblPartUserSequences.frame.size.height > self.tblPartUserSequences.contentSize.height) {
        
        [self.tblPartUserSequences endUpdates];
        self.tblPartUserSequences.contentSize = CGSizeMake(self.tblPartUserSequences.frame.size.width,self.tblPartUserSequences.frame.size.height);
        [self.tblPartUserSequences layoutIfNeeded];
    }
}
*/

- (BOOL)gestureRecognizerShouldBegin:(UIGestureRecognizer *)gestureRecognizer
{
    return YES;
}

- (void)moveVideoInUDSq:(id)sender {
    CGSize objFrame = [appDelegate getObjectFrame];
    int maxCnt = [appDelegate getMaxCountForRow];
    if (gestureCount <=1) {
        float pWx = 0.0;
        static int pIndex= 0;
        intSelectedPanl = [sender view].tag % 1000;
        intScrollIndex = (int)([sender view].tag / 1000) + 1;
        
        static NSMutableArray *arrRemoveObj = nil;
        if(isLocked==FALSE) {
            NSIndexPath *indexPath;
            static int deleteIndex;
            if(((UIPanGestureRecognizer*)sender).state == UIGestureRecognizerStateBegan && !isAdded) {
                isAnimationArea = NO;
                self.tblPartUserSequences.userInteractionEnabled = NO;
                self.tblPartAnimationArea.userInteractionEnabled = NO;
                
                CGPoint locationPointDlt = [sender locationInView:self.tblPartUserSequences];
                indexPath = [self.tblPartUserSequences indexPathForRowAtPoint:locationPointDlt];
                deleteIndex = (int)indexPath.row;
                
                [self.tblPartUserSequences setScrollEnabled:NO];
                self.tblPartUserSequences.alwaysBounceVertical = NO;
                isAdded = YES;
                CGPoint originPoint = [(UIPanGestureRecognizer*)sender locationInView:self.view];
                CGRect fr = [sender view].frame;
                
                originPoint.y = originPoint.y-30;
                originPoint.x = originPoint.x-30;
                fr.origin = originPoint;
                
                viewAnimation = [[AnimationView alloc]initWithNibName:@"AnimationView" bundle:nil];
                (viewAnimation.view).frame = CGRectMake(fr.origin.x, fr.origin.y, objFrame.width, objFrame.height);
                
//                [viewAnimation.btnVid setFrame:CGRectMake(2, 0, viewAnimation.view.frame.size.width-4, viewAnimation.view.frame.size.height-2)];
                // Version 3.0 Changes
                // New frame change for Small, Medium and Large buttons and client feedback point image looks untidy...
                (viewAnimation.btnVid).frame = CGRectMake(2, 0, viewAnimation.view.frame.size.width - 4, viewAnimation.view.frame.size.height-5);
                viewAnimation.view.transform = CGAffineTransformIdentity;
                [viewAnimation.view setExclusiveTouch:YES];
                [viewAnimation.btnVid setExclusiveTouch:YES];
                [viewAnimation.view setMultipleTouchEnabled:NO];
                [viewAnimation.btnVid setMultipleTouchEnabled:NO];
                NSInteger lblNameH = [self getHeightForNamelbl];
//                CGRect lblNameOriFrame = CGRectMake(viewAnimation.btnVid.frame.origin.x+1, viewAnimation.btnVid.frame.size.height - lblNameH - 7, viewAnimation.btnVid.frame.size.width-2, lblNameH);
                // Version 3.0 Changes
                // New frame change for Small, Medium and Large buttons and client feedback point image looks untidy...
                CGRect lblNameOriFrame = CGRectMake(viewAnimation.btnVid.frame.origin.x+1, viewAnimation.btnVid.frame.size.height - lblNameH, viewAnimation.btnVid.frame.size.width-2, lblNameH);

                viewAnimation.lblName.frame = lblNameOriFrame;
                (viewAnimation.lblName).font = [UIFont systemFontOfSize:[self getFontSizeForNamelbl]];
                viewAnimation.view.tag = [sender view].tag;
                (viewAnimation.btnVid).imageView.contentMode = UIViewContentModeScaleAspectFit;
                [viewAnimation.btnVid addTarget:self action:@selector(playVideo:) forControlEvents:UIControlEventTouchUpInside];
                @try {
                    arrRemoveObj = [[NSMutableArray alloc] init];
                    if ([(appDelegate.arrUserDefineSeq)[deleteIndex] count] > intSelectedPanl) {
                        
                        [arrRemoveObj addObject:(appDelegate.arrUserDefineSeq)[deleteIndex][intSelectedPanl]];
                        
                        [(appDelegate.arrUserDefineSeq)[deleteIndex]removeObjectAtIndex:intSelectedPanl];
//                        [self.tblPartUserSequences reloadRowsAtIndexPaths:@[[NSIndexPath indexPathForItem:deleteIndex inSection:0]] withRowAnimation:UITableViewRowAnimationNone];
                        
                        viewAnimation.lblName.text = arrRemoveObj[0][@"fileShortDispName"];
                        
                        int aIntLength = (int) [arrRemoveObj[0][@"fileTitle"] length];
                        
                        //===================
                        // Version 2.4 Changes
                        //===================

                        NSString *aStrImgThumbPath = [NSString stringWithFormat:@"%@/%@/%@", BITEFXV2, THUMBNAILS, IMAGES];
                        NSString *aStrMovieImgThumbPath = [NSString stringWithFormat:@"%@/%@/%@", BITEFXV2, THUMBNAILS, MOVIES];
                        
                        NSString *aStrImgName = [NSString stringWithFormat:@"%@",arrRemoveObj[0][@"thumbnailPath"]];
                        NSString *strImageName = [NSString stringWithFormat:@"%@",arrRemoveObj[0][@"thumbnailPath"]].lastPathComponent;
                        
                        if([aStrImgName rangeOfString:@"Documents/BiteFXiPadFull"].length>0) {
                            
                            NSString *dir_path = [NSString stringWithFormat:@"%@%@",DEST_PATH,strImageName];
                            [viewAnimation.btnVid setImage:[UIImage imageWithContentsOfFile:dir_path] forState:UIControlStateNormal];
                        } else if([aStrImgName rangeOfString:@"Documents/Update0001"].length > 0) {
                            
                            NSString *dir_path = [NSString stringWithFormat:@"%@%@",Update_Path,strImageName];
                            [viewAnimation.btnVid setImage:[UIImage imageWithContentsOfFile:dir_path] forState:UIControlStateNormal];
                        }
                        else if([aStrImgName rangeOfString:aStrImgThumbPath].length > 0) {
                            
                            [viewAnimation.btnVid setImage:[UIImage imageWithContentsOfFile:aStrImgName] forState:UIControlStateNormal];
                        }
                        else if([aStrImgName rangeOfString:aStrMovieImgThumbPath].length > 0) {
                            
                            [viewAnimation.btnVid setImage:[UIImage imageWithContentsOfFile:aStrImgName] forState:UIControlStateNormal];
                        }
                        else
                        {
                            aStrImgName = [NSString stringWithFormat:@"%@.jpg",[arrRemoveObj[0][@"fileTitle"] substringToIndex:aIntLength - 4]];
                            
                            [viewAnimation.btnVid setImage:[UIImage imageNamed:aStrImgName] forState:UIControlStateNormal];
                        }
                        [self.tblPartUserSequences reloadRowsAtIndexPaths:@[[NSIndexPath indexPathForItem:deleteIndex inSection:0]] withRowAnimation:UITableViewRowAnimationFade];
                        [self.view addSubview:viewAnimation.view];
                        [self.view bringSubviewToFront:viewAnimation.view];
                        
                        UIPanGestureRecognizer *panRecognizer11 = [[UIPanGestureRecognizer alloc] initWithTarget:self action:@selector(moveVideoInUDSq:)];
                        panRecognizer11.minimumNumberOfTouches = 1;
                        panRecognizer11.maximumNumberOfTouches = 1;
                        panRecognizer11.delegate = self;
                        
                        [viewAnimation.view addGestureRecognizer:panRecognizer11];


                        UITapGestureRecognizer *tempGest = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(tapVideo:)];
                        tempGest.delegate = self;
                        [viewAnimation.view addGestureRecognizer:tempGest];
                        [panRecognizer11 requireGestureRecognizerToFail:tempGest];
                        [sender setView:viewAnimation.view];
                        firstX = [sender view].center.x;
                        firstY = [sender view].center.y;
                        cntMovement = 0;
                    }
                }
                @catch (NSException *exception) {
                    NSLog(@"Exception :%@",exception);
                }
            }
            
            if(((UIPanGestureRecognizer*)sender).state == UIGestureRecognizerStateChanged) {
                
                cntMovement = cntMovement+1;
                CGPoint translatedPoint = [(UIPanGestureRecognizer*)sender translationInView:self.view];
                translatedPoint = CGPointMake(firstX+translatedPoint.x, firstY+translatedPoint.y);
                [sender view].center = translatedPoint;
                CGPoint locationPoint1 = [sender locationInView:self.tblPartUserSequences];
                if (locationPoint1.y > 0) {
                    NSIndexPath *indexPath = [self.tblPartUserSequences indexPathForRowAtPoint:locationPoint1];
                    @try {
                        NSArray *indexes = (self.tblPartUserSequences).indexPathsForVisibleRows;
                        if (indexes.count > 0) {
                            NSIndexPath *firstIndex = indexes[0];
                            if ((pIndex != indexPath.row) && ((self.tblPartUserSequences).visibleCells.count+ firstIndex.row)-1 <= indexPath.row) {
                                [self.tblPartUserSequences scrollToRowAtIndexPath:[NSIndexPath indexPathForRow:indexPath.row inSection:0] atScrollPosition:UITableViewScrollPositionTop
                                                                         animated:YES];
                            }else if (firstIndex.row >= indexPath.row){
                                [self.tblPartUserSequences scrollToRowAtIndexPath:[NSIndexPath indexPathForRow:indexPath.row inSection:0] atScrollPosition:UITableViewScrollPositionBottom
                                                                         animated:YES];
                            }
                        }
                    }
                    @catch (NSException *exception) {
                        NSLog(@"Exception :%@",exception);
                    }
                    int wX = [sender view].center.x - 190;
                    if(appDelegate.isPresentationInfoOpen)
                    {
                        wX = wX - PRESENTATION_INFO_VIEW_WIDTH;
                    }
//                    wX = wX / 70.0;
                    wX = wX / objFrame.width;
                    if (wX > ((NSMutableArray*)(appDelegate.arrUserDefineSeq)[indexPath.row]).count) {
                        wX = (int)((NSMutableArray*)(appDelegate.arrUserDefineSeq)[indexPath.row]).count;
                }
                    
                    NSMutableDictionary *dicExtraRow = [[NSMutableDictionary alloc]init];
                    [dicExtraRow setValue:@"Drag items to this line" forKey:@"fileShortDispName"];
                    [dicExtraRow setValue:@"extra" forKey:@"isExtraRowTab"];
                    [dicExtraRow setValue:@"0" forKey:@"thumbnailPath1"];

                    NSLog(@"Wx Before : %d", wX);
                    int wY = ([sender view].center.y - self.tblPartAnimationArea.frame.size.height) / (objFrame.height+2);
                    
                    // Version 2.5 change calculate Y index by location in TableviewCell...
                    AFTableViewCell *cell = (AFTableViewCell *)[self.tblPartUserSequences cellForRowAtIndexPath:[NSIndexPath indexPathForItem:indexPath.row inSection:0]];
                    CGPoint aPointInSideTableCell = [self.tblPartUserSequences convertPoint:locationPoint1 toView:cell.contentView];
                    
                    NSLog(@"ContentView : %f", aPointInSideTableCell.y);
                    wY = aPointInSideTableCell.y / (objFrame.height+2);
                    
                    if (wY > 0 ) {
                        wX = wX + (maxCnt * wY);
                        if (wX > ((NSMutableArray*)(appDelegate.arrUserDefineSeq)[indexPath.row]).count) {
                            wX = (int)((NSMutableArray*)(appDelegate.arrUserDefineSeq)[indexPath.row]).count;
                        }
                    }
                    
                    NSLog(@"Wx After : %d", wX);
                    @try {
                        if (pX != wX) {
                            if ([(appDelegate.arrUserDefineSeq)[indexPath.row] containsObject:dicExtraRow]) {
                                [(appDelegate.arrUserDefineSeq)[indexPath.row]removeObject:dicExtraRow];
                            }else{
                                [(appDelegate.arrUserDefineSeq)[indexPath.row] insertObject:dicExtraRow atIndex:wX];
                                pX = wX;
                                
                                /*
                                __unused AFTableViewCell *cell = (AFTableViewCell *)[self.tblPartUserSequences dequeueReusableCellWithIdentifier:@"CellIdentifier"];
                                [CATransaction begin];
                                [CATransaction setCompletionBlock:^{
                                    // Called async when all animations are finished
                                    [self.tblPartUserSequences beginUpdates];
                                    
                                    [self.tblPartUserSequences  reloadRowsAtIndexPaths:@[[NSIndexPath indexPathForItem:indexPath.row inSection:0]]  withRowAnimation:UITableViewRowAnimationNone];
                                    [self.tblPartUserSequences endUpdates];
                                }];
                                [CATransaction commit];
                                */
                                
                                AFTableViewCell *cell = (AFTableViewCell *)[self.tblPartUserSequences cellForRowAtIndexPath:[NSIndexPath indexPathForItem:indexPath.row inSection:0]];
                                [cell.collectionView reloadData];
                            }
                        }
                        
                        if (pIndex != indexPath.row) {
                            [(appDelegate.arrUserDefineSeq)[pIndex] removeObject:dicExtraRow];
                            
                            AFTableViewCell *cell = (AFTableViewCell *)[self.tblPartUserSequences cellForRowAtIndexPath:[NSIndexPath indexPathForItem:indexPath.row inSection:0]];
                            [cell.collectionView reloadData];

                            AFTableViewCell *cellPrevious = (AFTableViewCell *)[self.tblPartUserSequences cellForRowAtIndexPath:[NSIndexPath indexPathForItem:pIndex inSection:0]];
                            [cellPrevious.collectionView reloadData];

//                            [self.tblPartUserSequences reloadRowsAtIndexPaths:@[[NSIndexPath indexPathForItem:pIndex inSection:0]] withRowAnimation:UITableViewRowAnimationNone];
                        }

                    }
                    @catch (NSException *exception) {
                        NSLog(@"NSException %@",exception);
                    }
                    pIndex = (int)indexPath.row;
                    pWx = wX;
                    
                }
            }

            // **********************************************
            if(((UIPanGestureRecognizer*)sender).state == UIGestureRecognizerStateEnded)
            {
                [self.tblPartUserSequences setScrollEnabled:YES];
                if([sender view])
                {
                    [[sender view] removeFromSuperview];
                }
                CGPoint locationPoint1 = [sender locationInView:self.tblPartUserSequences];
                CGPoint locationPoint2 = [sender locationInView:self.tblPartAnimationArea];
                if (locationPoint1.y > 0 && self.tblPartAnimationArea.frame.size.height - locationPoint2.y < 0) {
                    NSIndexPath *indexPath = [self.tblPartUserSequences indexPathForRowAtPoint:locationPoint1];
                    
                    NSMutableArray *arrTemp = [[NSMutableArray alloc]init];
                    [arrTemp addObject:[NSString stringWithFormat:@"%ld",(long)indexPath.row]];
                    NSMutableDictionary *tempDic = [[NSMutableDictionary alloc]init];
                    [tempDic setValue:arrTemp forKey:@"tableIndex"];
                    
                    
                    /* Check For Item Index */
                    int wX = [sender view].center.x - 190;
                    if(appDelegate.isPresentationInfoOpen)
                    {
                        wX = wX - PRESENTATION_INFO_VIEW_WIDTH;
                    }
                    wX = wX / objFrame.width;
                    if (wX > ((NSMutableArray*)(appDelegate.arrUserDefineSeq)[indexPath.row]).count) {
                        wX = (int)((NSMutableArray*)(appDelegate.arrUserDefineSeq)[indexPath.row]).count;
                    }
                    
                    if(appDelegate.isPresentationInfoOpen)
                    {
                        if (((NSMutableArray*)(appDelegate.arrUserDefineSeq)[indexPath.row]).count > maxCnt-1) {

                            int wY = ([sender view].center.y - self.tblPartAnimationArea.frame.size.height) / (objFrame.height+2);
                            
                            // Version 2.5 change calculate Y index by location in TableviewCell...
                            AFTableViewCell *cell = (AFTableViewCell *)[self.tblPartUserSequences cellForRowAtIndexPath:[NSIndexPath indexPathForItem:indexPath.row inSection:0]];
                            CGPoint aPointInSideTableCell = [self.tblPartUserSequences convertPoint:locationPoint1 toView:cell.contentView];
                            
                            NSLog(@"ContentView : %f", aPointInSideTableCell.y);
                            wY = aPointInSideTableCell.y / (objFrame.height+2);

                            if (wY > 0 ) {
                                wX = wX + (maxCnt * wY);
                                if (wX > ((NSMutableArray*)(appDelegate.arrUserDefineSeq)[indexPath.row]).count) {
                                    wX = (int)((NSMutableArray*)(appDelegate.arrUserDefineSeq)[indexPath.row]).count;
                                }
                            }
                        }
                    }
                    else if (((NSMutableArray*)(appDelegate.arrUserDefineSeq)[indexPath.row]).count > maxCnt) {
                        
                        int wY = ([sender view].center.y - self.tblPartAnimationArea.frame.size.height) / (objFrame.height+2);
                        
                        // Version 2.5 change calculate Y index by location in TableviewCell...
                        AFTableViewCell *cell = (AFTableViewCell *)[self.tblPartUserSequences cellForRowAtIndexPath:[NSIndexPath indexPathForItem:indexPath.row inSection:0]];
                        CGPoint aPointInSideTableCell = [self.tblPartUserSequences convertPoint:locationPoint1 toView:cell.contentView];
                        
                        NSLog(@"ContentView : %f", aPointInSideTableCell.y);
                        wY = aPointInSideTableCell.y / (objFrame.height+2);

                        if (wY > 0 ) {
                            wX = wX + (maxCnt * wY);
                            if (wX > ((NSMutableArray*)(appDelegate.arrUserDefineSeq)[indexPath.row]).count) {
                                wX = (int)((NSMutableArray*)(appDelegate.arrUserDefineSeq)[indexPath.row]).count;
                            }
                        }
                    }
                    @try {
                        if ((appDelegate.arrUserDefineSeq).count > 0 && arrRemoveObj.count > 0) {
                            [(appDelegate.arrUserDefineSeq)[indexPath.row]insertObject:arrRemoveObj[0] atIndex:wX];
                        }
                    }
                    @catch (NSException *exception) {
                        NSLog(@"NSException %@",exception);
                    }
                    @try {
                        BOOL isExtraRow = NO;
                        for (NSDictionary *subDic in [(appDelegate.arrUserDefineSeq)[indexPath.row]mutableCopy]) {
                            
                            if (subDic[@"isExtra"]!= nil && !isExtraRow) {

                                
//                                [self.tblPartUserSequences  reloadRowsAtIndexPaths:@[[NSIndexPath indexPathForItem:indexPath.row inSection:0]]  withRowAnimation:UITableViewRowAnimationNone];

                                [(appDelegate.arrUserDefineSeq)[indexPath.row]removeObject:subDic];
                                
                                //Rohan
                                // if (![mutDicSequence objectForKey:[NSString stringWithFormat:@"%ld",(long)indexPath.row]]){
                                //===================
                                // Version 3.0 Changes
                                //===================
                                // Presentation Sequence Name changes
                                NSMutableDictionary *aMutDictSeq = [[NSMutableDictionary alloc] init];
                                aMutDictSeq[SEQUENCE_NAME] = @"Presentation";
                                aMutDictSeq[SEQUENCE_INDEX] = [NSString stringWithFormat:@"%d", 0];
                                [mutDicSequence setValue:aMutDictSeq forKey:[NSString stringWithFormat:@"%ld",(long)indexPath.row+1]];
                                
//                                [mutDicSequence setValue:[NSString stringWithFormat:@"Presentation (&#PD;)"] forKey:[NSString stringWithFormat:@"%ld",(long)indexPath.row+1]];
                                // }
                                
                                isExtraRow = YES;
                                [self.tblPartUserSequences reloadData];
                                [self addExtraRowForAddingPlaySeq];
                            }
                        }
                    }
                    @catch (NSException *exception) {
                        NSLog(@"exception %@",exception);
                    }
                    @finally {
                    }
                    
                }else{
                    
                    if (![[NSUserDefaults standardUserDefaults]boolForKey:@"PlayAnimation"] && deleteIndex == appDelegate.intSelectedScrNo-1) {
                        int tg = [sender view].tag % 1000;
                        if ((appDelegate.intSelectedVideo == tg) && (tg!= 0)) {
                            appDelegate.intSelectedVideo = appDelegate.intSelectedVideo - 1;
                        }
                    }else if (((NSMutableArray*)(appDelegate.arrUserDefineSeq)[deleteIndex]).count != 0){
                        [self.tblPartUserSequences scrollToRowAtIndexPath:[NSIndexPath indexPathForRow:deleteIndex inSection:0] atScrollPosition:UITableViewScrollPositionBottom
                                                                 animated:YES];
                    }
                    NSLog(@"%s, %d", __PRETTY_FUNCTION__, deleteIndex);
                    [self.tblPartUserSequences scrollToRowAtIndexPath:[NSIndexPath indexPathForRow:deleteIndex inSection:0] atScrollPosition:(UITableViewScrollPositionNone) animated:YES];
                }
                @try {
                    if (((NSMutableArray*)(appDelegate.arrUserDefineSeq)[deleteIndex]).count == 0) {
                        for (NSString *strKey in [mutDicSequence.allKeys sortedArrayUsingSelector:@selector(compare:)]) {
                            if ([NSString stringWithFormat:@"%@",strKey].intValue > deleteIndex+1) {
                                int tmp= [NSString stringWithFormat:@"%@",strKey].intValue;
                                [mutDicSequence setValue:mutDicSequence[strKey] forKey:[NSString stringWithFormat:@"%d",tmp-1]];
                                [mutDicSequence removeObjectForKey:[NSString stringWithFormat:@"%d",tmp]];
                            }
                        }
                        [appDelegate.arrUserDefineSeq removeObjectAtIndex:deleteIndex];
                    }
//                    [self.tblPartUserSequences reloadData];
                    NSMutableDictionary *dicExtraRow = [[NSMutableDictionary alloc]init];
                    [dicExtraRow setValue:@"Drag items to this line" forKey:@"fileShortDispName"];
                    [dicExtraRow setValue:@"extra" forKey:@"isExtraRowTab"];
                    [dicExtraRow setValue:@"0" forKey:@"thumbnailPath1"];
                    for (int dIndex = 0; dIndex < (appDelegate.arrUserDefineSeq).count; dIndex++) {
                        if ([(appDelegate.arrUserDefineSeq)[dIndex]count] == 0) {
                            [appDelegate.arrUserDefineSeq removeObjectAtIndex:dIndex];
                            [self.tblPartUserSequences reloadData];
                        }
                        if ([(appDelegate.arrUserDefineSeq)[dIndex] containsObject:dicExtraRow]) {
                            [(appDelegate.arrUserDefineSeq)[dIndex] removeObject:dicExtraRow];
                        }
//                        [self.tblPartUserSequences beginUpdates];
//                        [self.tblPartUserSequences  reloadRowsAtIndexPaths:@[[NSIndexPath indexPathForItem:dIndex inSection:0]]  withRowAnimation:UITableViewRowAnimationNone];
//                        [self.tblPartUserSequences endUpdates];
                    }
                    [self.tblPartUserSequences reloadData];
                }
                @catch (NSException *exception) {
                    NSLog(@"exception %@",exception);
                }
                if (appDelegate.arrUserDefineSeq.count == 0) {
                    [self addExtraRowForAddingPlaySeq];
                }
                isAdded = NO;
                canPan = NO;
                
                dispatch_sync(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
                    for (int dIndex = 0; dIndex < (appDelegate.arrUserDefineSeq).count; dIndex++) {
                        if ([(appDelegate.arrUserDefineSeq)[dIndex]count] == 0) {
                            [appDelegate.arrUserDefineSeq removeObjectAtIndex:dIndex];
//                            [self.tblPartUserSequences reloadData];
                        }
                    }
                    [self.tblPartUserSequences reloadData];
                });
                self.tblPartUserSequences.userInteractionEnabled = YES;
                self.tblPartAnimationArea.userInteractionEnabled = YES;
            }
            [self savingImagesIntoCollectionFilesMaster];
        }
    }
}

- (void)moveVideoFromAnimation:(id)sender {
    CGSize objFrame = [appDelegate getObjectFrame];
    int maxCnt = [appDelegate getMaxCountForRow];
    if (gestureCount <=1) {
        float pWx = 0.0;
        static int pIndex= 0;
        intSelectedPanl = [sender view].tag % 1000;
        intScrollIndex = (int)([sender view].tag / 1000) + 1;
        if(isLocked==FALSE) {
            
            if(((UIPanGestureRecognizer*)sender).state == UIGestureRecognizerStateBegan && !isAdded) {
                isAnimationArea = YES;
                [self.tblPartAnimationArea setScrollEnabled:NO];
                self.tblPartUserSequences.userInteractionEnabled = NO;
                self.tblPartAnimationArea.userInteractionEnabled = NO;
                isAdded = YES;
                CGPoint originPoint = [(UIPanGestureRecognizer*)sender locationInView:self.view];
                CGRect fr = [sender view].frame;
                originPoint.y = originPoint.y-30;
                originPoint.x = originPoint.x-30;
                fr.origin = originPoint;
                
                [self setCurrentDictWithColor:0 tempValue:@"0"];
                viewAnimation = [[AnimationView alloc]initWithNibName:@"AnimationView" bundle:nil];
                (viewAnimation.view).frame = CGRectMake(fr.origin.x, fr.origin.y, objFrame.width, objFrame.height);
//                [viewAnimation.btnVid setFrame:CGRectMake(2, 0, viewAnimation.view.frame.size.width-4, viewAnimation.view.frame.size.height-2)];
                
                // Version 3.0 Changes
                // New frame change for Small, Medium and Large buttons and client feedback point image looks untidy...
                (viewAnimation.btnVid).frame = CGRectMake(2, 0, viewAnimation.view.frame.size.width - 4, viewAnimation.view.frame.size.height-5);

                viewAnimation.view.transform = CGAffineTransformIdentity;
                [viewAnimation.view setExclusiveTouch:YES];
                [viewAnimation.btnVid setExclusiveTouch:YES];
                [viewAnimation.view setMultipleTouchEnabled:NO];
                [viewAnimation.btnVid setMultipleTouchEnabled:NO];
                [viewAnimation.btnVid setUserInteractionEnabled:YES];
                [viewAnimation.view setUserInteractionEnabled:YES];
                NSInteger lblNameH = [self getHeightForNamelbl];
//                CGRect lblNameOriFrame = CGRectMake(viewAnimation.btnVid.frame.origin.x+1, viewAnimation.btnVid.frame.size.height - lblNameH - 7, viewAnimation.btnVid.frame.size.width-2, lblNameH);
                // Version 3.0 Changes
                // New frame change for Small, Medium and Large buttons and client feedback point image looks untidy...
                CGRect lblNameOriFrame = CGRectMake(viewAnimation.btnVid.frame.origin.x+1, viewAnimation.btnVid.frame.size.height - lblNameH, viewAnimation.btnVid.frame.size.width-2, lblNameH);

                viewAnimation.lblName.frame = lblNameOriFrame;
                (viewAnimation.lblName).font = [UIFont systemFontOfSize:[self getFontSizeForNamelbl]];
                viewAnimation.view.tag = [sender view].tag;
                
                (viewAnimation.btnVid).imageView.contentMode = UIViewContentModeScaleAspectFit;
                [viewAnimation.btnVid addTarget:self action:@selector(playVideo:) forControlEvents:UIControlEventTouchUpInside];
                viewAnimation.lblName.text = mutDictCurrentAnimatedObj[@"fileShortDispName"];
                int aIntLength = (int)[mutDictCurrentAnimatedObj[@"fileTitle"] length];
                
                //===================
                // Version 2.4 Changes
                //===================

                NSString *aStrImgThumbPath = [NSString stringWithFormat:@"%@/%@/%@", BITEFXV2, THUMBNAILS, IMAGES];
                NSString *aStrMovieImgThumbPath = [NSString stringWithFormat:@"%@/%@/%@", BITEFXV2, THUMBNAILS, MOVIES];
                
                NSString *aStrImgName = [NSString stringWithFormat:@"%@",mutDictCurrentAnimatedObj[@"thumbnailPath"]];
                NSString *strImageName = [NSString stringWithFormat:@"%@",mutDictCurrentAnimatedObj[@"thumbnailPath"]].lastPathComponent;
                
                if([aStrImgName rangeOfString:@"Documents/BiteFXiPadFull"].length>0) {
                    
                    NSString *dir_path = [NSString stringWithFormat:@"%@%@",DEST_PATH,strImageName];
                    [viewAnimation.btnVid setImage:[UIImage imageWithContentsOfFile:dir_path] forState:UIControlStateNormal];
                } else if([aStrImgName rangeOfString:@"Documents/Update0001"].length > 0) {
                    
                    NSString *dir_path = [NSString stringWithFormat:@"%@%@",Update_Path,strImageName];
                    [viewAnimation.btnVid setImage:[UIImage imageWithContentsOfFile:dir_path] forState:UIControlStateNormal];
                }
                else if([aStrImgName rangeOfString:aStrImgThumbPath].length > 0) {
                    
                    [viewAnimation.btnVid setImage:[UIImage imageWithContentsOfFile:aStrImgName] forState:UIControlStateNormal];
                }
                else if([aStrImgName rangeOfString:aStrMovieImgThumbPath].length > 0) {
                    
                    [viewAnimation.btnVid setImage:[UIImage imageWithContentsOfFile:aStrImgName] forState:UIControlStateNormal];
                }
                else {
                    aStrImgName = [NSString stringWithFormat:@"%@.jpg",[mutDictCurrentAnimatedObj[@"fileTitle"] substringToIndex:aIntLength - 4]];
                    [viewAnimation.btnVid setImage:[UIImage imageNamed:aStrImgName] forState:UIControlStateNormal];
                }
                
                [self.view addSubview:viewAnimation.view];
                [self.view bringSubviewToFront:viewAnimation.view];
                
                UIPanGestureRecognizer *panRecognizer1 = [[UIPanGestureRecognizer alloc] initWithTarget:self action:@selector(moveVideoFromAnimation:)];
                panRecognizer1.minimumNumberOfTouches = 1;
                panRecognizer1.maximumNumberOfTouches = 1;
                [viewAnimation.view addGestureRecognizer:panRecognizer1];

                if ([UIDevice currentDevice].systemVersion.floatValue >=9.0)
                    [[sender view] addGestureRecognizer:panRecognizer1];
                
                UITapGestureRecognizer *tempGest = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(tapVideo:)];
                tempGest.delegate = self;
                [viewAnimation.view addGestureRecognizer:tempGest];
                [panRecognizer1 requireGestureRecognizerToFail:tempGest];
                [sender setView:viewAnimation.view];
                firstX = [sender view].center.x;
                firstY = [sender view].center.y;
                cntMovement = 0;
            }
            if(((UIPanGestureRecognizer*)sender).state == UIGestureRecognizerStateChanged) {
                cntMovement = cntMovement+1;
                CGPoint translatedPoint = [(UIPanGestureRecognizer*)sender translationInView:self.view];
                translatedPoint = CGPointMake(firstX+translatedPoint.x, firstY+translatedPoint.y);
                
                [sender view].center = translatedPoint;
                CGPoint locationPoint1 = [sender locationInView:self.tblPartUserSequences];
                
                if (locationPoint1.y > 0) {
                    NSIndexPath *indexPath = [self.tblPartUserSequences indexPathForRowAtPoint:locationPoint1];
                    @try {
                        NSArray *indexes = (self.tblPartUserSequences).indexPathsForVisibleRows;
                        if (indexes.count > 0) {
                            NSIndexPath *firstIndex = indexes[0];
                            
                            if ((pIndex != indexPath.row) && ((self.tblPartUserSequences).visibleCells.count + firstIndex.row) -1 <= indexPath.row) {
                                [self.tblPartUserSequences scrollToRowAtIndexPath:[NSIndexPath indexPathForRow:indexPath.row inSection:0] atScrollPosition:UITableViewScrollPositionTop
                                                                         animated:YES];
                            }
                        }
                    }
                    @catch (NSException *exception) {
                        NSLog(@"Exception :%@",exception);
                    }
                    NSMutableArray *arrTemp = [[NSMutableArray alloc]init];
                    [arrTemp addObject:[NSString stringWithFormat:@"%ld",(long)indexPath.row]];
                    NSMutableDictionary *tempDic = [[NSMutableDictionary alloc]init];
                    [tempDic setValue:arrTemp forKey:@"tableIndex"];
                    
                    int wX = [sender view].center.x - 190; // 190 is viewleft corner width...
                    if(appDelegate.isPresentationInfoOpen)
                    {
                        wX = wX - PRESENTATION_INFO_VIEW_WIDTH;
                    }
                    
                    wX = wX / objFrame.width;
                    if (wX > ((NSMutableArray*)(appDelegate.arrUserDefineSeq)[indexPath.row]).count) {
                        wX = (int)((NSMutableArray*)(appDelegate.arrUserDefineSeq)[indexPath.row]).count;
                    }
                    NSMutableDictionary *dicExtraRow = [[NSMutableDictionary alloc]init];
                    [dicExtraRow setValue:@"Drag items to this line" forKey:@"fileShortDispName"];
                    [dicExtraRow setValue:@"extra" forKey:@"isExtraRowTab"];
                    [dicExtraRow setValue:@"0" forKey:@"thumbnailPath1"];
                     @try {
                        
                         if(appDelegate.isPresentationInfoOpen)
                         {
                             if (((NSMutableArray*)(appDelegate.arrUserDefineSeq)[indexPath.row]).count > maxCnt-1) {

                                 int wY = ([sender view].center.y - self.tblPartAnimationArea.frame.size.height) / (objFrame.height+2);
                                 
                                 // Version 2.5 change calculate Y index by location in TableviewCell...
                                 AFTableViewCell *cell = (AFTableViewCell *)[self.tblPartUserSequences cellForRowAtIndexPath:[NSIndexPath indexPathForItem:indexPath.row inSection:0]];
                                 CGPoint aPointInSideTableCell = [self.tblPartUserSequences convertPoint:locationPoint1 toView:cell.contentView];
                                 
                                 NSLog(@"ContentView : %f", aPointInSideTableCell.y);
                                 wY = aPointInSideTableCell.y / (objFrame.height+2);
                                 
                                 if (wY > 0 ) {
                                     wX = wX + (maxCnt * wY);
                                     if (wX > ((NSMutableArray*)(appDelegate.arrUserDefineSeq)[indexPath.row]).count) {
                                         wX = (int)((NSMutableArray*)(appDelegate.arrUserDefineSeq)[indexPath.row]).count;
                                     }
                                 }
                             }
                         }
                         else if (((NSMutableArray*)(appDelegate.arrUserDefineSeq)[indexPath.row]).count > maxCnt-1) {

                             int wY = ([sender view].center.y - self.tblPartAnimationArea.frame.size.height) / (objFrame.height+2);
                             
                             // Version 2.5 change calculate Y index by location in TableviewCell...
                             AFTableViewCell *cell = (AFTableViewCell *)[self.tblPartUserSequences cellForRowAtIndexPath:[NSIndexPath indexPathForItem:indexPath.row inSection:0]];
                             CGPoint aPointInSideTableCell = [self.tblPartUserSequences convertPoint:locationPoint1 toView:cell.contentView];
                             
                             NSLog(@"ContentView : %f", aPointInSideTableCell.y);
                             wY = aPointInSideTableCell.y / (objFrame.height+2);

                             if (wY > 0 ) {
                                 wX = wX + (maxCnt * wY);
                                 if (wX > ((NSMutableArray*)(appDelegate.arrUserDefineSeq)[indexPath.row]).count) {
                                     wX = (int)((NSMutableArray*)(appDelegate.arrUserDefineSeq)[indexPath.row]).count;
                                 }
                             }
                         }
                        if (pX != wX) {
                            if ([(appDelegate.arrUserDefineSeq)[indexPath.row] containsObject:dicExtraRow]) {
                                [(appDelegate.arrUserDefineSeq)[indexPath.row]removeObject:dicExtraRow];
                                
                            }else{
                                [(appDelegate.arrUserDefineSeq)[indexPath.row] insertObject:dicExtraRow atIndex:wX];
                                pX = wX;
                                
                                AFTableViewCell *cell = (AFTableViewCell *)[self.tblPartUserSequences cellForRowAtIndexPath:[NSIndexPath indexPathForItem:indexPath.row inSection:0]];
                                [cell.collectionView reloadData];
//                                [self.tblPartUserSequences  reloadRowsAtIndexPaths:@[[NSIndexPath indexPathForItem:indexPath.row inSection:0]]  withRowAnimation:UITableViewRowAnimationNone];
                            }
                        }
                        if (pIndex != indexPath.row) {
                            [(appDelegate.arrUserDefineSeq)[pIndex] removeObject:dicExtraRow];
                            
                            AFTableViewCell *cell = (AFTableViewCell *)[self.tblPartUserSequences cellForRowAtIndexPath:[NSIndexPath indexPathForItem:indexPath.row inSection:0]];
                            [cell.collectionView reloadData];
                            
                            AFTableViewCell *cellPrevious = (AFTableViewCell *)[self.tblPartUserSequences cellForRowAtIndexPath:[NSIndexPath indexPathForItem:pIndex inSection:0]];
                            [cellPrevious.collectionView reloadData];

//                            [self.tblPartUserSequences reloadRowsAtIndexPaths:@[[NSIndexPath indexPathForItem:pIndex inSection:0]] withRowAnimation:UITableViewRowAnimationNone];
                        }
                        pIndex = (int)indexPath.row;
                    }
                    @catch (NSException *exception) {
                        NSLog(@"NSException %@",exception);
                    }
                    pWx = wX;
                }
                [self setCurrentDictWithColor:0 tempValue:@"0"];
            }
            
            if(((UIPanGestureRecognizer*)sender).state == UIGestureRecognizerStateEnded)
            {
                [self.tblPartAnimationArea setScrollEnabled:YES];
                
                /* retrive Current Index Possition From UITableview */
                CGPoint locationPoint1 = [sender locationInView:self.tblPartUserSequences];
                CGPoint locationPoint2 = [sender locationInView:self.tblPartAnimationArea];
                
                if (locationPoint1.y > 0 && locationPoint2.y > self.tblPartAnimationArea.frame.size.height) {
                    
                    AFTableViewCell *cell = (AFTableViewCell *)[self.tblPartUserSequences dequeueReusableCellWithIdentifier:@"CellIdentifier"];
                    
                    NSIndexPath *indexPath = [self.tblPartUserSequences indexPathForRowAtPoint:locationPoint1];
                    
                    NSMutableDictionary *dicExtraRow = [[NSMutableDictionary alloc]init];
                    [dicExtraRow setValue:@"Drag items to this line" forKey:@"fileShortDispName"];
                    [dicExtraRow setValue:@"extra" forKey:@"isExtraRowTab"];
                    [dicExtraRow setValue:@"0" forKey:@"thumbnailPath1"];
                    
                    @try {
                        for (int dIndex = 0; dIndex < (appDelegate.arrUserDefineSeq).count; dIndex++) {
                            [(appDelegate.arrUserDefineSeq)[dIndex] removeObject:dicExtraRow];
                            
//                            [self.tblPartUserSequences  reloadRowsAtIndexPaths:@[[NSIndexPath indexPathForItem:dIndex inSection:0]]  withRowAnimation:UITableViewRowAnimationNone];
                        }
                        [self.tblPartUserSequences reloadData];
                    }
                    @catch (NSException *exception) {
                        NSLog(@"Exception :%@",exception);
                    }
                    CGPoint locationPointCollection1 = [sender locationInView:cell.collectionView];
                    __unused NSIndexPath *indexPath1 = [cell.collectionView indexPathForItemAtPoint:locationPointCollection1];
                    
                    NSMutableArray *arrTemp = [[NSMutableArray alloc]init];
                    [arrTemp addObject:[NSString stringWithFormat:@"%ld",(long)indexPath.row]];
                    NSMutableDictionary *tempDic = [[NSMutableDictionary alloc]init];
                    [tempDic setValue:arrTemp forKey:@"tableIndex"];
                    
                    @try {
                        int wX = [sender view].center.x - 190;
                        if(appDelegate.isPresentationInfoOpen)
                        {
                            wX = wX - PRESENTATION_INFO_VIEW_WIDTH;
                        }
                        wX = wX / objFrame.width;
                        if (wX > ((NSMutableArray*)(appDelegate.arrUserDefineSeq)[indexPath.row]).count) {
                            wX = (int)((NSMutableArray*)(appDelegate.arrUserDefineSeq)[indexPath.row]).count;
                        }
                        if(appDelegate.isPresentationInfoOpen)
                        {
                            if (((NSMutableArray*)(appDelegate.arrUserDefineSeq)[indexPath.row]).count > maxCnt-1) {

                                int wY = ([sender view].center.y - self.tblPartAnimationArea.frame.size.height) / (objFrame.height+2);
                                
                                // Version 2.5 change calculate Y index by location in TableviewCell...
                                AFTableViewCell *cell = (AFTableViewCell *)[self.tblPartUserSequences cellForRowAtIndexPath:[NSIndexPath indexPathForItem:indexPath.row inSection:0]];
                                CGPoint aPointInSideTableCell = [self.tblPartUserSequences convertPoint:locationPoint1 toView:cell.contentView];
                                
                                NSLog(@"ContentView : %f", aPointInSideTableCell.y);
                                wY = aPointInSideTableCell.y / (objFrame.height+2);

                                if (wY > 0 ) {
                                    wX = wX + (maxCnt * wY);
                                    if (wX > ((NSMutableArray*)(appDelegate.arrUserDefineSeq)[indexPath.row]).count) {
                                        wX = (int)((NSMutableArray*)(appDelegate.arrUserDefineSeq)[indexPath.row]).count;
                                    }
                                }
                            }
                        }
                        else if (((NSMutableArray*)(appDelegate.arrUserDefineSeq)[indexPath.row]).count > maxCnt-1) {
                            int wY = ([sender view].center.y - self.tblPartAnimationArea.frame.size.height) / (objFrame.height+2);
                            
                            // Version 2.5 change calculate Y index by location in TableviewCell...
                            AFTableViewCell *cell = (AFTableViewCell *)[self.tblPartUserSequences cellForRowAtIndexPath:[NSIndexPath indexPathForItem:indexPath.row inSection:0]];
                            CGPoint aPointInSideTableCell = [self.tblPartUserSequences convertPoint:locationPoint1 toView:cell.contentView];
                            
                            NSLog(@"ContentView : %f", aPointInSideTableCell.y);
                            wY = aPointInSideTableCell.y / (objFrame.height+2);

                            if (wY > 0) {
                                wX = wX + (maxCnt * wY);
                                if (wX > ((NSMutableArray*)(appDelegate.arrUserDefineSeq)[indexPath.row]).count) {
                                    wX = (int)((NSMutableArray*)(appDelegate.arrUserDefineSeq)[indexPath.row]).count;
                                }
                            }
                        }
                        
                        if ((appDelegate.arrUserDefineSeq).count > 0) {
                            
                            NSMutableDictionary *crntObj = [[NSMutableDictionary alloc] init];
                            crntObj = (appDelegate.arrScrollViewCount)[intScrollIndex-1][intSelectedPanl];
                            
                            if([crntObj valueForKey:@"color"] != nil){
                                crntObj[@"color"] = [UIColor clearColor];
                            }
                            
                            [(appDelegate.arrUserDefineSeq)[indexPath.row]insertObject:crntObj atIndex:wX];
                        }
                        BOOL isExtraRow = NO;
                        for (NSDictionary *subDic in [(appDelegate.arrUserDefineSeq)[indexPath.row]mutableCopy]) {
                            if (subDic[@"isExtra"]!= nil && !isExtraRow) {
                                
//                                [self.tblPartUserSequences  reloadRowsAtIndexPaths:@[[NSIndexPath indexPathForItem:indexPath.row inSection:0]]  withRowAnimation:UITableViewRowAnimationNone];
                                
                                [(appDelegate.arrUserDefineSeq)[indexPath.row]removeObject:subDic];
                                
                                isExtraRow = YES;
                                // dictionary filling with name.
//                                [mutDicSequence setValue:[NSString stringWithFormat:@"Presentation (&#PD;)"] forKey:[NSString stringWithFormat:@"%ld",(long)indexPath.row+1]];
                                //===================
                                // Version 3.0 Changes
                                //===================
                                // Presentation Sequence Name changes
                                NSMutableDictionary *aMutDictSeq = [[NSMutableDictionary alloc] init];
                                aMutDictSeq[SEQUENCE_NAME] = @"Presentation ";
                                aMutDictSeq[SEQUENCE_INDEX] = [NSString stringWithFormat:@"%d", 0];
                                [mutDicSequence setValue:aMutDictSeq forKey:[NSString stringWithFormat:@"%ld",(long)indexPath.row+1]];

                                [self addExtraRowForAddingPlaySeq];
                                
                            }
                        }
                        
//                        if (!isExtraRow) {
//                            [self.tblPartUserSequences  reloadRowsAtIndexPaths:@[[NSIndexPath indexPathForItem:indexPath.row inSection:0]]  withRowAnimation:UITableViewRowAnimationNone];
//                        }
                        
                        [self.tblPartUserSequences reloadData];
                    }
                    @catch (NSException *exception) {
                        NSLog(@"exception %@",exception);
                    }
               }else{
                    
                    NSMutableDictionary *dicExtraRow = [[NSMutableDictionary alloc]init];
                    [dicExtraRow setValue:@"Drag items to this line" forKey:@"fileShortDispName"];
                    [dicExtraRow setValue:@"extra" forKey:@"isExtraRowTab"];
                    [dicExtraRow setValue:@"0" forKey:@"thumbnailPath1"];
                    for (int dIndex = 0; dIndex < (self.tblPartUserSequences).visibleCells.count; dIndex++) {
                        [(appDelegate.arrUserDefineSeq)[dIndex] removeObject:dicExtraRow];
                        
//                        [self.tblPartUserSequences  reloadRowsAtIndexPaths:@[[NSIndexPath indexPathForItem:dIndex inSection:0]]  withRowAnimation:UITableViewRowAnimationNone];
                        
//                        AFTableViewCell *cell = (AFTableViewCell *)[self.tblPartUserSequences cellForRowAtIndexPath:[NSIndexPath indexPathForItem:dIndex inSection:0]];
//                        [cell.collectionView reloadData];

                    }
                   [self.tblPartUserSequences reloadData ];
                }
                // --------------------------- limit for background view --------------------------- //
                isAdded = NO;
                if([sender view])
                {
                    [[sender view] removeFromSuperview];
                }
                [self setCurrentDictWithColor:0 tempValue:@"0"];
                canPan = NO;
                self.tblPartUserSequences.userInteractionEnabled = YES;
                self.tblPartAnimationArea.userInteractionEnabled = YES;
                
                [self savingImagesIntoCollectionFilesMaster];
            }
        }
    }
}

- (void)editSequenceTitle:(id)sender {
    
    // Version 3.0 Changes
    // According to client feedback rename and delete presentation regardless of the lock state...
    // Commented if condition...
//    if(isLocked==FALSE) {
        UITapGestureRecognizer *tapGest = (UITapGestureRecognizer*)sender;
        UILabel *lblSeq = (UILabel*)tapGest.view;
        // tag 502

        UIAlertController * alertController = [UIAlertController alertControllerWithTitle: @"Presentation Name"
                                                                                  message: @""
                                                                           preferredStyle:UIAlertControllerStyleAlert];
        [alertController addTextFieldWithConfigurationHandler:^(UITextField *textField) {
            textField.placeholder = @"Enter Name";
            textField.text = [NSString stringWithFormat:@"%@",lblSeq.text];
            textField.keyboardAppearance = UIKeyboardAppearanceAlert;
            textField.returnKeyType = UIReturnKeyDone;
            textField.tag = lblSeq.tag;
            textField.clearButtonMode = UITextFieldViewModeWhileEditing;
            textField.borderStyle = UITextBorderStyleRoundedRect;
            textField.delegate = (id<UITextFieldDelegate>)self;
        }];

        [alertController addAction:[UIAlertAction actionWithTitle:@"Save" style:UIAlertActionStyleDefault handler:^(UIAlertAction *action) {
            NSArray * textfields = alertController.textFields;
            UITextField * namefield = textfields[0];
            [self->mutDicSequence removeObjectForKey:[NSString stringWithFormat:@"%ld",(long)namefield.tag]];
//            [self->mutDicSequence setValue:[NSString stringWithFormat:@"%@",[namefield text]] forKey:[NSString stringWithFormat:@"%ld",(long)[namefield tag]]];
                        
            //===================
            // Version 3.0 Changes
            //===================
            // Presentation Sequence Name changes
            NSMutableDictionary *aMutDictSeq = [[NSMutableDictionary alloc] init];
            aMutDictSeq[SEQUENCE_NAME] = [NSString stringWithFormat:@"%@",namefield.text];
            aMutDictSeq[SEQUENCE_INDEX] = [NSString stringWithFormat:@"%d", 0];
            NSString *aStrCollectionId = [NSString stringWithFormat:@"%ld",(long)namefield.tag];
            [self->mutDicSequence setValue:aMutDictSeq forKey: aStrCollectionId];

            // Update data in database...
//            [self updateSequenceTitle:aMutDictSeq andCollectionID: aStrCollectionId];
            [self savingImagesIntoCollectionFilesMaster];
            [self.tblPartUserSequences reloadData];
//            [self.tblPartUserSequences reloadRowsAtIndexPaths:@[[NSIndexPath indexPathForRow:[namefield tag]-1 inSection:0]] withRowAnimation:UITableViewRowAnimationFade];

        }]];

        [alertController addAction:[UIAlertAction actionWithTitle:@"Cancel" style:UIAlertActionStyleDefault handler:^(UIAlertAction *action) {


        }]];
        [self presentViewController:alertController animated:YES completion:nil];



//    }
}

/*
-(void)alertView:(UIAlertView *)alertView clickedButtonAtIndex:(NSInteger)buttonIndex{
    
    if (alertView.tag == 505) {

    }
    else if (alertView.tag == 506) {

    }
    else if (alertView.tag == 507) {

    }
    else if(alertView.tag==501)
    {

    }
    else if(alertView.tag==502) {

    }
}
*/
-(void)deleteWholeRow:(NSInteger)indexDelete
{
    [mutDicSequence removeObjectForKey:[NSString stringWithFormat:@"%ld",(long)indexDelete+1]];
    for (NSString* keyNumber in [mutDicSequence.allKeys sortedArrayUsingSelector:@selector(compare:)]) {
        if (keyNumber.integerValue>indexDelete) {
//            NSString *temp = [[mutDicSequence objectForKey:keyNumber] mutableCopy];
            
            //===================
            // Version 3.0 Changes
            //===================
            // Presentation Sequence Name changes
            NSMutableDictionary *aMutDictSeq = [mutDicSequence[keyNumber] mutableCopy];
            [mutDicSequence setValue:aMutDictSeq forKey:[NSString stringWithFormat:@"%ld",(long)keyNumber.integerValue-1]];
        }
    }
    [appDelegate.arrUserDefineSeq removeObjectAtIndex:indexDelete];
    [self.tblPartUserSequences reloadData];
    intSelected = -11;
    
    // Version 3.0 Changes
    // According to client feedback rename and delete presentation regardless of the lock state...
    // Now delete data From CollectionUserDefine...
    //===================
    [self savingImagesIntoCollectionFilesMaster];
    
//    NSString *aStrCollectionId = [NSString stringWithFormat:@"%ld",(long)indexDelete+1];
//    NSString *aStrDelete = [NSString stringWithFormat:@"Delete from CollectionUserDefine where collectionID = \'%@\'", aStrCollectionId];
//
//    NSLog(@"Delete=======\n%@\n",aStrDelete);
//    [[Database sharedDatabase] Delete:aStrDelete];
}

- (void)updateSequenceTitle:(NSMutableDictionary *)mutDictSeq andCollectionID:(NSString *)strCollectionId {
    
    NSString *aStrUpdateQuery = [NSString stringWithFormat:@"update CollectionUserDefine SET sequenceName = \"%@\", sequenceIndex = \"%@\" where collectionID = '%@'", mutDictSeq[SEQUENCE_NAME], mutDictSeq[SEQUENCE_INDEX], strCollectionId];
    [[Database sharedDatabase] Update:aStrUpdateQuery];
    
}

-(void)deleteImageAlbum:(NSInteger)indexDelete
{
    NSString *aStrCollectionId = [NSString stringWithFormat:@"%@",(appDelegate.arrScrollViewCount)[indexDelete][0][@"collectionID"]];
    
    //===================
    // Delete data From LocalFileMaster...
    //===================
    NSString *aStrQuery = [NSString stringWithFormat:@"select filesID from CollectionFilesMaster where collectionID = \'%@\'", aStrCollectionId];
    NSMutableArray *aMutArrFilesData = [[Database sharedDatabase] getAllDataForQuery:aStrQuery];
    
    if(aMutArrFilesData.count > 0) {
        
        for (int aIntJ = 0; aIntJ < aMutArrFilesData.count; aIntJ++)
        {
            [self deleteDataFromFileMaster:[aMutArrFilesData[aIntJ] valueForKey:@"filesID"]];
        }
    }
    
    //===================
    // Now delete data From CollectionFilesMaster...
    //===================
    NSString *aStrDelete = [NSString stringWithFormat:@"Delete from CollectionFilesMaster where collectionID = \'%@\'", aStrCollectionId];
    
    NSLog(@"Delete=======\n%@\n",aStrDelete);
    [[Database sharedDatabase] Delete:aStrDelete];

    //===================
    // Now delete data From CollectionMaster...
    //===================
    aStrDelete = [NSString stringWithFormat:@"Delete from CollectionMaster where collectionID = \'%@\'", aStrCollectionId];
    
    NSLog(@"Delete=======\n%@\n",aStrDelete);
    [[Database sharedDatabase] Delete:aStrDelete];
    
    [appDelegate.arrScrollViewCount removeObjectAtIndex:indexDelete];
    
//    if (isLocked) {
////
////        // No need to delete from arrImage because appDelegate.arrScrollViewCount and arrImage reference is same...
//        [appDelegate.arrImage removeObjectAtIndex:indexDelete];
//    }
//    else
//    {
//         //No need to delete from arrLockImage because appDelegate.arrScrollViewCount and arrLockImage reference is same...
//        [appDelegate.arrImage removeObjectAtIndex:indexDelete];
//    }
    
    // Update data for selection index...
    // Check image is deleted in animation area...
    if ((indexDelete == appDelegate.intSelectedScrNo - 1) && ([[NSUserDefaults standardUserDefaults]boolForKey:@"PlayAnimation"])) {
        
        appDelegate.mutArrPlayVideo = (appDelegate.arrScrollViewCount)[indexDelete-1] ;
        appDelegate.intSelectedVideo = 0;
        appDelegate.intSelectedScrNo--;
        
    }
    
//    [self removeAddButton];
//    appDelegate.mutArrPlayVideo = [appDelegate.arrScrollViewCount objectAtIndex:0] ;
//    appDelegate.intSelectedVideo = 0;
//    appDelegate.intSelectedScrNo = 1;
    
//    [self.tblPartAnimationArea reloadData];
    intSelected = -11;
    
    // Get updated data...
    [self getNoOfCollectionsAndItsContentsUD];
    
    // This is done regarding client comment the picture panel jumps to the top on click of hide/unhide so we also do same for delete image album functionality..
    appDelegate.isScrollToSelectedIndex = NO;
    [self setSelectedIndexColor];
    appDelegate.isScrollToSelectedIndex = YES;
//    [self.tblPartAnimationArea reloadData];
}

- (void)deleteDataFromFileMaster:(NSString *)aStrFileId {
    
    //===================
    // If imported imgae is used in User Define Sequnce, than delete From there...
    //===================
    
    NSString *aStrSql = [NSString stringWithFormat:@"select filesID from CollectionUserDefineFiles where filesID= \'%@\'", aStrFileId];
    
    BOOL isRecordExists =  [[Database shareDatabase] CheckForRecord:aStrSql];
    
    if (isRecordExists) {
        
        // Data exists in User Define Sequence, so delete from CollectionUserDefineFiles...
        NSString *aStrDelete = [NSString stringWithFormat:@"Delete from CollectionUserDefineFiles where filesID = \'%@\'", aStrFileId];
        
        NSLog(@"Delete=======\n%@\n",aStrDelete);
        [[Database sharedDatabase] Delete:aStrDelete];

    }

    NSString *aStrDelete = [NSString stringWithFormat:@"Delete from LocalFileMaster where fileID = \'%@\'", aStrFileId];
    
    NSLog(@"Delete=======\n%@\n",aStrDelete);
    [[Database sharedDatabase] Delete:aStrDelete];

}

-(void)deleteImage:(NSInteger)indexDelete
{
    int currRow = (int)(indexDelete / 1000);
    int currentObj = (int)(indexDelete % 1000);

    NSString *aStrCollectionId = [NSString stringWithFormat:@"%@",(appDelegate.arrScrollViewCount)[currRow][0][@"collectionID"]];
    
    NSString *aStrFileId = [NSString stringWithFormat:@"%@",(appDelegate.arrScrollViewCount)[currRow][currentObj][@"filesID"]];

    NSInteger totalImages = [(appDelegate.arrScrollViewCount)[currRow] count];
    
    //===================
    // Delete data From LocalFileMaster...
    //===================
    [self deleteDataFromFileMaster:aStrFileId];
    
    //===================
    // Now delete data From CollectionFilesMaster...
    //===================
    NSString *aStrDelete = [NSString stringWithFormat:@"Delete from CollectionFilesMaster where collectionID = \'%@\' AND filesID = \'%@\'", aStrCollectionId, aStrFileId];
    
    NSLog(@"Delete=======\n%@\n",aStrDelete);
    [[Database sharedDatabase] Delete:aStrDelete];
    
    //===================
    // If last image is deleted, delete image albumn...
    //===================
    
//    if ((isLocked && totalImages == 2) || (!isLocked && totalImages == 1)) {
    if (totalImages == 2) {
        //===================
        // Now delete data From CollectionMaster...
        //===================
        [appDelegate.arrScrollViewCount removeObjectAtIndex:currRow];
        
        // Update data for selection index...
        // Check image is deleted in animation area...
        if ((currRow == appDelegate.intSelectedScrNo - 1) && ([[NSUserDefaults standardUserDefaults]boolForKey:@"PlayAnimation"])) {
            appDelegate.mutArrPlayVideo = (appDelegate.arrScrollViewCount)[currRow-1] ;
            appDelegate.intSelectedVideo = 0;
            appDelegate.intSelectedScrNo--;
        }

//        appDelegate.mutArrPlayVideo = [appDelegate.arrScrollViewCount objectAtIndex:0] ;
//        appDelegate.intSelectedVideo = 0;
//        appDelegate.intSelectedScrNo= 1;

//        if (isLocked) {
        
            // No need to delete from arrImage because appDelegate.arrScrollViewCount and arrImage reference is same...
//            [appDelegate.arrImage removeObjectAtIndex:currRow];
//        }
//        else
//        {
//            // No need to delete from arrLockImage because appDelegate.arrScrollViewCount and arrLockImage reference is same...
//            [appDelegate.arrImage removeObjectAtIndex:currRow];
//        }

        aStrDelete = [NSString stringWithFormat:@"Delete from CollectionMaster where collectionID = \'%@\'", aStrCollectionId];
        
        NSLog(@"Delete=======\n%@\n",aStrDelete);
        [[Database sharedDatabase] Delete:aStrDelete];
    }
    else
    {
        [(appDelegate.arrScrollViewCount)[currRow] removeObjectAtIndex:currentObj];
        
        // Check image is deleted in animation area...
        if ([[NSUserDefaults standardUserDefaults]boolForKey:@"PlayAnimation"]) {
        
            // Update data for selection index...
            appDelegate.mutArrPlayVideo = (appDelegate.arrScrollViewCount)[currRow];

            if (appDelegate.intSelectedVideo > 0) {
                appDelegate.intSelectedVideo = appDelegate.intSelectedVideo - 1;
            }
            else
            {
                appDelegate.intSelectedVideo = 0;
            }

        }
//        [self removeAddButton];
//        appDelegate.intSelectedScrNo= 1;
//        appDelegate.intSelectedVideo = 0;
//        if (isLocked) {
        
            // No need to delete from arrImage because appDelegate.arrScrollViewCount and arrImage reference is same...
//            [[appDelegate.arrImage objectAtIndex:currRow] removeObjectAtIndex:currentObj];
//        }
//        else
//        {
//            // No need to delete from arrLockImage because appDelegate.arrScrollViewCount and arrLockImage reference is same...
//            [[appDelegate.arrImage objectAtIndex:currRow] removeObjectAtIndex:currentObj];
//        }

    }

    
    
    
    intSelected = -11;
    
    // Get updated data...
    [self getNoOfCollectionsAndItsContentsUD];

    // This is done regarding client comment the picture panel jumps to the top on click of hide/unhide so we also do same for delete image functionality..
    appDelegate.isScrollToSelectedIndex = NO;
    [self setSelectedIndexColor];
    appDelegate.isScrollToSelectedIndex = YES;

}

- (void)removeAddButton {
    
    NSString *aStrUserDefine = [(appDelegate.mutArrPlayVideo)[0] valueForKey:@"isUserDefine"];

    if(appDelegate.intSelectedTab == 1 && [aStrUserDefine isEqualToString:@"1"])
    {
        // Remove add button object...
        NSString *aStrCollectionId = [appDelegate.mutArrPlayVideo.lastObject valueForKey:@"collectionID"];
        if ([aStrCollectionId isEqualToString:@"-100"]) {
            [appDelegate.mutArrPlayVideo removeLastObject];
        }
    
    }

}




- (NSInteger)getFontSizeForNamelbl
{
    if(appDelegate.selectedModeSize == 2)
    {
        return 14;
    }
    else if(appDelegate.selectedModeSize == 3)
    {
        return 16;
    }
    else
    {
        return 9;
    }
}

- (NSInteger)getHeightForNamelbl
{
    NSInteger lblNameH = 0;
    
    if(appDelegate.selectedModeSize == 2)
    {
        lblNameH = 35;
    }
    else if(appDelegate.selectedModeSize == 3)
    {
        lblNameH = 39;
    }
    else
    {
//        lblNameH = 16;
        lblNameH = 25;
    }
    return lblNameH;
}

#pragma mark --------------------------------
#pragma mark UITextField Delegate Methods

- (void)textFieldDidBeginEditing:(UITextField *)textField {
    
    if (!isCreateNewImageAlbum && !isEditImageAlbumName) {
        [textField selectAll:textField];
    }
    
}


-(BOOL)textFieldShouldReturn:(UITextField *)textField {
//    [textField resignFirstResponder];
    
    if (isCreateNewImageAlbum || isEditImageAlbumName) {

        if ([self isValidImageAlbumName:textField.text]) {

            if (isEditImageAlbumName) {
                // Update Image Album Name...
                [self updateImageAlbumName:[self transferCollectionNumberToNewString:oldStrAlbumName newStr:textField.text]];
            }
            else
            {
                // Save Image Album Name...
                [self saveImageAlbumName:textField.text];
            }

            [self dismissViewControllerAnimated:YES completion:nil];
        }
        else
        {
            return NO;
        }
    }
    else
    {
        [mutDicSequence removeObjectForKey:[NSString stringWithFormat:@"%ld",(long)textField.tag]];
//        [mutDicSequence setValue:[NSString stringWithFormat:@"%@",textField.text] forKey:[NSString stringWithFormat:@"%ld",(long)textField.tag]];
        
        //===================
        // Version 3.0 Changes
        //===================
        // Presentation Sequence Name changes
        NSMutableDictionary *aMutDictSeq = [[NSMutableDictionary alloc] init];
        aMutDictSeq[SEQUENCE_NAME] = [NSString stringWithFormat:@"%@",textField.text];
        aMutDictSeq[SEQUENCE_INDEX] = [NSString stringWithFormat:@"%d", 0];
        NSString *aStrCollectionId = [NSString stringWithFormat:@"%ld",(long)textField.tag];
        [self->mutDicSequence setValue:aMutDictSeq forKey: aStrCollectionId];

        // Update data in database...
//        [self updateSequenceTitle:aMutDictSeq andCollectionID: aStrCollectionId];
        [self savingImagesIntoCollectionFilesMaster];

        [self.tblPartUserSequences reloadData];
//        [self.tblPartUserSequences reloadRowsAtIndexPaths:@[[NSIndexPath indexPathForRow:textField.tag-1 inSection:0]] withRowAnimation:UITableViewRowAnimationFade];
        [self dismissViewControllerAnimated:YES completion:nil];
    }

    return YES;
}


#pragma mark - UITableview
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView{
    return 1.0;
}
-(NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    if (tableView.tag == 101) {
        return (appDelegate.arrScrollViewCount).count;
    }else{
        int cnt = (int)(appDelegate.arrUserDefineSeq).count;
        if (cnt == 0) {
            cnt = 1;
        }
        return cnt;
    }
}

-(UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    
    static NSString *CellIdentifier = @"CellIdentifier";
    AFTableViewCell *cell = (AFTableViewCell *)[tableView dequeueReusableCellWithIdentifier:CellIdentifier];
    if (!cell)
    {
        cell = [[AFTableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:CellIdentifier];
    }
    
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    cell.backgroundColor = [UIColor clearColor];
    
//    CGFloat horizontalOffset = 0;
//    [cell.collectionView setContentOffset:CGPointMake(horizontalOffset, 0)];
//    [cell.collectionView setScrollEnabled:YES];
//    [cell.collectionView setTag:indexPath.row];
//
//    if (tableView.tag == 101) {
//        [cell.collectionView setAccessibilityLabel:@"collection1"];
//        [cell.collectionView setBackgroundColor:[UIColor blackColor]];
//        isFilePlayed = NO;
//    }else{
//        [cell.collectionView setAccessibilityLabel:@"collection2"];
//        [cell.collectionView setBackgroundColor:[UIColor colorWithRed:31.0/255.0 green:34.0/255.0 blue:39.0/255.0 alpha:1]];
//    }
//
    
//    dispatch_async(dispatch_get_main_queue(), ^{
//       [cell setCollectionViewDataSourceDelegate:self indexPath:indexPath];
//        [cell.collectionView reloadData];
//    });
    
    return cell;
}

-(UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section{
    if (tableView.tag == 101) {
        UIView *vFooter = [[UIView alloc]init];
        vFooter.frame = CGRectMake(0, 0, self.tblPartAnimationArea.frame.size.width, 20);
        vFooter.backgroundColor = [UIColor clearColor];
        vFooter.tag = 101;
        return vFooter;
    }else{
        return nil;
    }
}

-(CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section{
    if (tableView.tag == 101) {
        return 30.0f;
    }else{
        return 0.0f;
    }
}

-(CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section{
    if (tableView.tag == 101) {
        return 0.0f;
    }else{
        return 30.0f;
    }
}

-(UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section{
    
    if (tableView.tag == 102) {
        UIView *vFooter = [[UIView alloc]init];
        vFooter.frame = CGRectMake(0, 0, self.tblPartAnimationArea.frame.size.width, 23);
        vFooter.backgroundColor = [UIColor clearColor];
        vFooter.tag = 101;

        UIPanGestureRecognizer *panRecognizer1 = [[UIPanGestureRecognizer alloc] initWithTarget:self action:@selector(resizingAnimationAndUDSequence:)];
        panRecognizer1.minimumNumberOfTouches = 1;
        panRecognizer1.maximumNumberOfTouches = 1;
        panRecognizer1.delegate = self;

        [vFooter addGestureRecognizer:panRecognizer1];

        UIImageView *imgSlider = [[UIImageView alloc]init];
        imgSlider.image = [UIImage imageNamed:@"Devider_image.png"];
        imgSlider.frame = CGRectMake(0, 0, vFooter.frame.size.width, 23);

        UILabel *fromLabel = [[UILabel alloc]initWithFrame:CGRectMake(35,3, 300, 17)];
        fromLabel.text = @"My Presentations";
        fromLabel.backgroundColor = [UIColor clearColor];
        fromLabel.textColor = [UIColor whiteColor];
        fromLabel.font = [UIFont boldSystemFontOfSize:13.0];
        [vFooter addSubview:imgSlider];
        [vFooter addSubview:fromLabel];
        return vFooter;
    }
    else{
//        UIView *newView = [[UIView alloc]init];
//        newView.frame = CGRectMake(0, 0, 0, 0);
//        newView.backgroundColor = [UIColor clearColor];
//        return newView;
        return nil;
    }
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    if (tableView.tag == 101) {
        return [self getCountFromArr:appDelegate.arrScrollViewCount andIndexPathRow:indexPath.row];
    }else{
        return [self getCountFromArr:appDelegate.arrUserDefineSeq andIndexPathRow:indexPath.row];
    }
}

-(long)getCountFromArr:(NSMutableArray*)arr andIndexPathRow:(NSInteger)row
{
    if (arr.count > 0)
    {
        NSInteger totalCount = [appDelegate getMaxCountForRow];
        CGSize itemSize = [appDelegate getObjectFrame];
        long cnt = ((NSMutableArray*)arr[row]).count;
        long newCnt = cnt;
        cnt = cnt / totalCount;
        if(newCnt % totalCount > 0)
        {
            cnt = cnt+1;
        }
        return cnt*itemSize.height + 5;
    }
    else
    {
        return 0;
    }

}

-(void)tableView:(UITableView *)tableView willDisplayCell:(AFTableViewCell *)cell forRowAtIndexPath:(NSIndexPath *)indexPath
{
    [cell setCollectionViewDataSourceDelegate:self indexPath:indexPath];
    CGFloat horizontalOffset = 0;
    (cell.collectionView).contentOffset = CGPointMake(horizontalOffset, 0);
    [cell.collectionView setScrollEnabled:YES];
    (cell.collectionView).tag = indexPath.row;

    if (tableView.tag == 101) {
        (cell.collectionView).accessibilityLabel = @"collection1";
        (cell.collectionView).backgroundColor = [UIColor blackColor];
        isFilePlayed = NO;
    }else{
        (cell.collectionView).accessibilityLabel = @"collection2";
        (cell.collectionView).backgroundColor = [UIColor colorWithRed:31.0/255.0 green:34.0/255.0 blue:39.0/255.0 alpha:1];
    }
    
    dispatch_async(dispatch_get_main_queue(), ^{
    [cell.collectionView reloadData];
    });
}

#pragma mark - UICollectionViewDataSource Methods
-(NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section
{
    if ([collectionView.accessibilityLabel isEqualToString:@"collection1"]) {
        isFilePlayed = NO;
        
        if (appDelegate.selectedObj != nil) {
            if (appDelegate.intSelectedTab == 0) {
                return  appDelegate.arrSelectedMovie.count;
            } else if (appDelegate.intSelectedTab == 1) {
                return  appDelegate.arrSelectedImage.count;
            } else {
                return 0;
            }
        }
        else  {
            if (appDelegate.arrScrollViewCount.count > collectionView.tag) {
                return ((NSMutableArray*)appDelegate.arrScrollViewCount[collectionView.tag]).count;
            } else {
                return 0;
            }
        }
        
    }else{
        int cnt = (int)((NSMutableArray*) appDelegate.arrUserDefineSeq).count;
        if (cnt == 0) {
            cnt = 1;
        }else{
            cnt = (int)((NSMutableArray*) appDelegate.arrUserDefineSeq[collectionView.tag]).count;
        }
        
        return cnt;
    }
}

-(UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath
{
    collectionView.backgroundColor = [UIColor clearColor];

    UICollectionViewCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:CollectionViewCellIdentifier forIndexPath:indexPath];
    [cell setExclusiveTouch:YES];
    cell.backgroundColor = [UIColor clearColor];
    cell.tag = 101;
    
    for (UIView *view in cell.contentView.subviews) {
        [view removeFromSuperview];
    }
    
    for (UIView *vW in collectionView.subviews) {
        if ([vW isKindOfClass:[UILabel class]] || [vW isKindOfClass:[UIImageView class]] || [vW isKindOfClass:[UIButton class]]) {
            [vW removeFromSuperview];
        }
    }
    
    AnimationView *animation = [[AnimationView alloc]initWithNibName:@"AnimationView" bundle:nil];
    animation.view.frame = CGRectMake(0, 0, cell.contentView.frame.size.width, cell.contentView.frame.size.height);
    
    animation.view.tag = (collectionView.tag * 1000) + indexPath.row;
    
    if ([collectionView.accessibilityLabel isEqualToString:@"collection2"]) {
        // Second
        collectionView.backgroundColor = [UIColor clearColor];
        [self cellConfigureCollection2:animation indexPath:indexPath WithcollectionView:collectionView];
        
    }else{
        // First
        [self cellConfigureCollection2Else:animation indexPath:indexPath WithcollectionView:collectionView WithCell:cell];
    }
    [animation.view setExclusiveTouch:YES];
    [animation.view setMultipleTouchEnabled:FALSE];
    [collectionView setExclusiveTouch:YES];
    
    animation.btnVid.exclusiveTouch = YES;
    (animation.btnVid).imageView.contentMode = UIViewContentModeScaleAspectFit;
    [animation.btnVid addTarget:self action:@selector(playVideo:) forControlEvents:UIControlEventTouchUpInside];
    
    [cell.contentView addSubview:animation.view];
    (animation.viewLeftCorner).frame = CGRectMake(0, 0, 190, 10);
    
    // Only add once...
    if (indexPath.row == 0) {
        [collectionView addSubview:animation.viewLeftCorner];
    }
    
    if (appDelegate.intSelectedTab == 2) {

        // Show copy sequence and info sequence button...
        [collectionView bringSubviewToFront:animation.btnInfoSequence];
        [collectionView bringSubviewToFront:animation.btnCopySequence];
        [collectionView bringSubviewToFront:animation.btnFavSequence];
    }
    
    [collectionView setScrollEnabled:NO];
    
    return cell;
}


-(void)favUnFav:(UIButton*)sender {
    NSInteger intSelected = sender.tag;
    [self favUpdate:intSelected isSelected:sender.isSelected ? 0 : 1 btnFav:sender];
}

-(void)favUpdate:(NSInteger)indexFav isSelected:(int)selected btnFav:(UIButton*)sender
{
    int currRow = (int)(indexFav / 1000);
    int currentObj = (int)(indexFav % 1000);
    
    NSString *filesID = [NSString stringWithFormat:@"%@", appDelegate.selectedObj != nil ?  (appDelegate.arrScrollViewCount)[0][@"filesID"] : (appDelegate.arrScrollViewCount)[currRow][currentObj][@"filesID"]];
    
    //===================
    // Make fav or unfav data to LocalFileMaster...
    //===================
    NSString *aStrUpdateSql = [NSString stringWithFormat:@"Update LocalFileMaster set isFavourite = %d where fileID = %@",selected, filesID];
    [[Database sharedDatabase]Update:aStrUpdateSql];
    
    [appDelegate.selectedObj != nil ? (appDelegate.arrScrollViewCount)[0] : (appDelegate.arrScrollViewCount)[currRow][currentObj] setObject:[NSString stringWithFormat:@"%d",selected] forKey:@"isFavourite"];
    
    [UIView transitionWithView:sender
                      duration:0.3
                       options:UIViewAnimationOptionTransitionCrossDissolve
                    animations:^{
        [sender setSelected:![sender isSelected]];
    } completion:nil];
    
    if (!sender.isSelected && appDelegate.isFavourite) {
        if (appDelegate.intSelectedTab == 2) {
            appDelegate.arrPresentationFavourite = appDelegate.arrScrollViewCount;
        } else {
            [appDelegate.arrScrollViewCount[currRow] removeObjectAtIndex:currentObj];
            if ([appDelegate.arrScrollViewCount[currRow] count] == 0) {
                [appDelegate.arrScrollViewCount removeObjectAtIndex:currRow];
            }
            if (appDelegate.intSelectedTab == 0) {
                appDelegate.arrMovieFavourite = appDelegate.arrScrollViewCount;
            } else if (appDelegate.intSelectedTab == 1) {
                appDelegate.arrImageFavourite = appDelegate.arrScrollViewCount;
            }
        }
        
        NSIndexSet *sections = [NSIndexSet indexSetWithIndexesInRange:NSMakeRange(0, [self.tblPartAnimationArea numberOfSections])];
        [self.tblPartAnimationArea reloadSections:sections withRowAnimation:UITableViewRowAnimationFade];
    } else {
        if (appDelegate.selectedObj != nil) {
            if (appDelegate.intSelectedTab == 0) {
                appDelegate.arrSelectedMovie = appDelegate.arrScrollViewCount;
            } else if (appDelegate.intSelectedTab == 1) {
                appDelegate.arrSelectedImage = appDelegate.arrScrollViewCount;
            } else {
                appDelegate.arrPresentation = appDelegate.arrScrollViewCount;
            }
        } else {
            if (appDelegate.intSelectedTab == 0) {
                appDelegate.arrMovie = appDelegate.arrScrollViewCount;
                NSArray *filteredMovie = [self filterFavouritesInArray:appDelegate.arrMovie];
                appDelegate.arrMovieFavourite = [filteredMovie mutableCopy];
            } else if (appDelegate.intSelectedTab == 1) {
                appDelegate.arrImage = appDelegate.arrScrollViewCount;
                NSArray *filteredImages = [self filterFavouritesInArray:appDelegate.arrImage];
                appDelegate.arrImageFavourite = [filteredImages mutableCopy];
            } else {
                appDelegate.arrPresentation = appDelegate.arrScrollViewCount;
            }
        }
    }
}

-(void)favUnFavUserDefineSqu:(UIButton*)sender {
    NSInteger intSelected = sender.tag;
    [self favUnFavUserDefineSqu:intSelected isSelected:sender.isSelected ? 0 : 1 btnFav:sender];
}

-(void)favUnFavUserDefineSqu:(NSInteger)indexFav isSelected:(int)selected btnFav:(UIButton*)sender
{
    int currRow = (int)(indexFav / 1000);
    int currentObj = (int)(indexFav % 1000);
    
    NSString *filesID = [NSString stringWithFormat:@"%@",(appDelegate.arrUserDefineSeq)[currRow][currentObj][@"filesID"]];
    
    //===================
    // Make fav or unfav data to CollectionUserDefineFiles...
    //===================
    NSString *aStrUpdateSql = [NSString stringWithFormat:@"Update CollectionUserDefineFiles set isFavourite = %d where fileID = %@",selected, filesID];
    [[Database sharedDatabase]Update:aStrUpdateSql];
    
    NSLog(@"appDelegate.intSelectedTab:::%ld", (long)appDelegate.intSelectedTab);
    
    NSString *favourite = [NSString stringWithFormat:@"%@",(appDelegate.arrUserDefineSeq)[currRow][currentObj][@"isFavourite"]];
    
    NSLog(@"favourite>>>%@", favourite);
    
    [(appDelegate.arrUserDefineSeq)[currRow][currentObj] setObject:[NSString stringWithFormat:@"%d",selected] forKey:@"isFavourite"];
    
    NSString *favourite1 = [NSString stringWithFormat:@"%@",(appDelegate.arrUserDefineSeq)[currRow][currentObj][@"isFavourite"]];
    
    NSLog(@"favourite1>>>%@", favourite1);
    
    [UIView transitionWithView:sender
                      duration:0.3
                       options:UIViewAnimationOptionTransitionCrossDissolve
                    animations:^{
        [sender setSelected:![sender isSelected]];
    } completion:nil];
    
//    if (appDelegate.intSelectedTab == 0) {
//        appDelegate.arrMovie = appDelegate.arrScrollViewCount;
//    } else if (appDelegate.intSelectedTab == 1) {
//        appDelegate.arrImage = appDelegate.arrScrollViewCount;
//    } else {
//        appDelegate.arrPresentation = appDelegate.arrScrollViewCount;
//    }
}

-(void)cellConfigureCollection2:(AnimationView *)animation indexPath:(NSIndexPath *)indexPath WithcollectionView:(UICollectionView *)collectionView
{
    NSString *strlblDisplayMovieGroup = [NSString stringWithFormat:@"%@",(appDelegate.arrUserDefineSeq)[collectionView.tag][indexPath.row][@"fileShortDispName"]];
    strlblDisplayMovieGroup = [self strReplaceAsciCodes:strlblDisplayMovieGroup];
    strlblDisplayMovieGroup = [strlblDisplayMovieGroup stringByReplacingOccurrencesOfString:@"\u00ac\u221e" withString:@"\u00b0"];
    (animation.lblName).text = [NSString stringWithFormat:@"%@",strlblDisplayMovieGroup];
    (animation.lblName).font = [UIFont systemFontOfSize:[self getFontSizeForNamelbl]];
    (animation.btnVid).frame = CGRectMake(2, 0, animation.view.frame.size.width - 4, animation.view.frame.size.height-2);
    
    [self hideButtons:animation];
    
    NSInteger lblNameH = [self getHeightForNamelbl];
    //        CGRect lblNameOriFrame = CGRectMake(animation.btnVid.frame.origin.x+1, animation.btnVid.frame.size.height - lblNameH - 7, animation.btnVid.frame.size.width-2, lblNameH);
    // Version 3.0 Changes
    // New frame change for Small, Medium and Large buttons and client feedback point image looks untidy...
    CGRect lblNameOriFrame = CGRectMake(animation.btnVid.frame.origin.x+1, animation.btnVid.frame.size.height - lblNameH, animation.btnVid.frame.size.width-2, lblNameH);
    NSString *aStr = [NSString stringWithFormat:@"%@",(appDelegate.arrUserDefineSeq)[collectionView.tag][indexPath.row][@"thumbnailPath"]];
    
    [animation.btnVid setImage:[UIImage imageWithContentsOfFile:aStr] forState:UIControlStateNormal];
    (void)(animation.btnVid).clipsToBounds;
    (animation.lblTitle).frame = CGRectMake(5, 12, 180, collectionView.frame.size.height-8.5);
    
    (animation.lblTitle).textColor = [UIColor colorWithRed:0.925 green:1.000 blue:0.525 alpha:1.000];
    
    UITapGestureRecognizer *tapRecognizer_title = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(editSequenceTitle:)];
    [animation.lblTitle setUserInteractionEnabled:YES];
    tapRecognizer_title.delegate = self;
    (animation.lblTitle).tag = collectionView.tag+1;
    [animation.lblTitle addGestureRecognizer:tapRecognizer_title];
    (animation.lblTitle).numberOfLines = 0;
    animation.view.backgroundColor = (UIColor *)(appDelegate.arrUserDefineSeq)[collectionView.tag][indexPath.row][@"color"];
    
    if ([animation.view.backgroundColor isEqual:animationBGColor])
    {
        [self setAnimationBGColor:animationBGColor withView:animation];
        lblNameOriFrame.origin.x = lblNameOriFrame.origin.x+1;
        lblNameOriFrame.size.width = lblNameOriFrame.size.width -2;
    }
    else
    {
        [self setAnimationBGColor:[UIColor clearColor] withView:animation];
    }
    
    (animation.lblName).frame = lblNameOriFrame;

    NSString *isHide = [NSString stringWithFormat:@"%@",(appDelegate.arrUserDefineSeq)[collectionView.tag][indexPath.row][@"IsHidden"]];
    if([isHide isEqualToString:@"1"])
    {
        (animation.lblName).frame = CGRectMake(0, 0, 0, 0);
        [animation.btnVid setImage:[UIImage imageNamed:@"hiddenpic.png"] forState:UIControlStateNormal];
    }
    
    if (![(appDelegate.arrUserDefineSeq)[collectionView.tag][indexPath.row][@"isExtra"]isEqualToString:@"RemoveKey"])
    {
        UIPanGestureRecognizer *panRecognizer1 = [[UIPanGestureRecognizer alloc] initWithTarget:self action:@selector(moveVideoInUDSq:)];
        
        panRecognizer1.minimumNumberOfTouches = 1;
        panRecognizer1.maximumNumberOfTouches = 1;
        panRecognizer1.delegate = self;
        [animation.view addGestureRecognizer:panRecognizer1];
        
        //            NSString *strTitle = [NSString stringWithFormat:@"%@",[mutDicSequence objectForKey:[NSString stringWithFormat:@"%ld",(long)collectionView.tag+1]]];
        
        //===================
        // Version 3.0 Changes
        //===================
        // Presentation Sequence Name changes
        NSMutableDictionary *aMutDictSeq = mutDicSequence[[NSString stringWithFormat:@"%ld",(long)collectionView.tag+1]];
        NSString *aStrName = aMutDictSeq[SEQUENCE_NAME];
        int seqIndex = [aMutDictSeq[SEQUENCE_INDEX] intValue];
        
        // Add sequence index when we display sequence name...
        NSString *strTitle = [NSString stringWithFormat:@"%@", aStrName];
        if (seqIndex > 0) {
            // Add sequence index when we display sequence name...
            strTitle = [NSString stringWithFormat:@"%@ (%@)", aStrName, aMutDictSeq[SEQUENCE_INDEX]];
        }
        
        strTitle = [self strReplaceAsciCodes:strTitle];
        if ([strTitle isEqualToString:@"(null)"]) {
            strTitle = [NSString stringWithFormat:@"Presentation (&#PD;)"];
        }
        
        // Commented below line due to new sequence logic...
        strTitle = [strTitle stringByReplacingOccurrencesOfString:@"&#PD;" withString:[NSString stringWithFormat:@"%ld",(long)collectionView.tag + 1]];
        
        // very first title call as title sequence gets null
        (animation.lblTitle).text = [NSString stringWithFormat:@"%@",strTitle];
        [animation.lblTitle drawTextInRect:animation.lblTitle.frame];
        [collectionView addSubview:animation.lblTitle];
        
    }else{
        [animation.lblTitle removeFromSuperview];
        [animation.btnVid setUserInteractionEnabled:NO];
    }
    
    animation.btnInfoSequence.hidden = TRUE;
    animation.btnCopySequence.hidden = TRUE;
    animation.btnFavSequence.hidden = TRUE;
    animation.btnCopyUDpresentation.hidden = TRUE;
    if ([(appDelegate.arrUserDefineSeq)[collectionView.tag][indexPath.row][@"fileShortDispName"] isEqualToString:@"Drag items to this line"] && [(appDelegate.arrUserDefineSeq)[collectionView.tag][indexPath.row][@"isExtraRowTab"] isEqualToString:@"extra"]){
        [animation.lblName  removeFromSuperview];
    }
    
    NSString *strlblDisplayMovieGroup1 = [NSString stringWithFormat:@"%@",(appDelegate.arrUserDefineSeq)[collectionView.tag][indexPath.row][@"fileShortDispName"]];
    
    if ([(appDelegate.arrUserDefineSeq)[collectionView.tag] count] == 1 && [strlblDisplayMovieGroup1 isEqualToString:@"Drag items to this line"]) {
        (animation.btnLeftCorner).backgroundColor = [UIColor clearColor];
        [self btnImage:animation.btnLeftCorner withName:@"gray.png"];
        animation.btnLeftCorner.hidden = TRUE;
        animation.btnCopyUDpresentation.hidden = TRUE;
        [animation.btnFav setHidden:YES];
        [animation.btnFavMedium setHidden:YES];
        [animation.btnFavLarge setHidden:YES];
        
        CGFloat collectionViewWidth = collectionView.bounds.size.width;
        
        if(appDelegate.selectedModeSize == 3) {
            animation.view.frame = CGRectMake((collectionViewWidth - (animation.viewLeftCorner.frame.size.width * 4.2)) / 2.0, animation.view.frame.origin.y, animation.view.frame.size.width, animation.view.frame.size.height + 50);
        } else if(appDelegate.selectedModeSize == 2) {
            animation.view.frame = CGRectMake((collectionViewWidth - (animation.viewLeftCorner.frame.size.width * 4)) / 2.0, animation.view.frame.origin.y, animation.view.frame.size.width, animation.view.frame.size.height + 60);
        } else {
            animation.view.frame = CGRectMake((collectionViewWidth - (animation.viewLeftCorner.frame.size.width * 3.5)) / 2.0, animation.view.frame.origin.y, animation.view.frame.size.width, animation.view.frame.size.height + 50);
        }
    }else{
        animation.btnLeftCorner.hidden = FALSE;
        animation.btnCopyUDpresentation.hidden = FALSE;
        animation.btnLeftCorner.tag = collectionView.tag;
        animation.btnCopyUDpresentation.tag = collectionView.tag;
        float xValue = -2;
        float yValue = 2.5;
        
        animation.btnLeftCorner.frame = CGRectMake(xValue,yValue,35,animation.lblTitle.frame.size.height/2);
        animation.btnCopyUDpresentation.frame = CGRectMake(170,yValue,35,animation.lblTitle.frame.size.height/2);
        
        [animation.lblTitle addSubview:animation.btnLeftCorner];
        [animation.lblTitle bringSubviewToFront:animation.btnLeftCorner];
        [animation.lblTitle addSubview:animation.btnCopyUDpresentation];
        [animation.lblTitle bringSubviewToFront:animation.btnCopyUDpresentation];
        
        [animation.btnLeftCorner addTarget:self action:@selector(btnLeftCornerClick:) forControlEvents:UIControlEventTouchUpInside];
        [animation.btnCopyUDpresentation addTarget:self action:@selector(btnCopyUDpresentationClick:) forControlEvents:UIControlEventTouchUpInside];
        if (intSelected == collectionView.tag) {
            // Have to delete
            (animation.btnLeftCorner).backgroundColor = [UIColor clearColor];
            [self btnImage:animation.btnLeftCorner withName:@"Yellow.png"];
        }else{
            // Not delete
            //                if (isLocked) {
            //                    [self btnImage:animation.btnLeftCorner withName:@"gray.png"];
            //                }else{
            [self btnImage:animation.btnLeftCorner withName:@"Red.png"];
            //                }
        }
    }
}

-(void)hideButtons:(AnimationView *)animation {
    
    [animation.btnFav setHidden:YES];
    [animation.btnFavMedium setHidden:YES];
    [animation.btnFavLarge setHidden:YES];
}

-(void)btnImage:(UIButton*)btn withName:(NSString*)imgName
{
    [btn setImage:[UIImage imageNamed:imgName] forState:(UIControlStateNormal)];
}

-(NSString*)strReplaceAsciCodes:(NSString*)str
{
    str = [str stringByReplacingOccurrencesOfString:@"&#xD;" withString:@"\n"];
    str = [str stringByReplacingOccurrencesOfString:@"&#xd;" withString:@"\n"];
    str = [str stringByReplacingOccurrencesOfString:@"\\n" withString:@"\n"];
    return str;
}

-(void)btnLeftCornerClick:(UIButton *)sender
{
    // Version 3.0 Changes
    // According to client feedback rename and delete presentation regardless of the lock state...
    // Commented if condition...
//    if (isLocked) {
//        UIButton *btnLock = (UIButton *)[self.view viewWithTag:121];
//        [self lockAnimation:btnLock];
//        return;
//    }
    
    intSelected = sender.tag;
    [self.tblPartUserSequences reloadData];
//    NSString *strMsg = [mutDicSequence valueForKey:[NSString stringWithFormat:@"%ld",(long)intSelected+1]];
    
    //===================
    // Version 3.0 Changes
    //===================
    // Presentation Sequence Name changes
    NSMutableDictionary *aMutDictSeq = mutDicSequence[[NSString stringWithFormat:@"%ld",(long)intSelected+1]];
    NSString *aStrName = aMutDictSeq[SEQUENCE_NAME];
    int seqIndex = [aMutDictSeq[SEQUENCE_INDEX] intValue];

    // Add sequence index when we display sequence name...
    NSString *strMsg = [NSString stringWithFormat:@"%@", aStrName];
    if (seqIndex > 0) {
        // Add sequence index when we display sequence name...
        strMsg = [NSString stringWithFormat:@"%@ (%@)", aStrName, aMutDictSeq[SEQUENCE_INDEX]];
    }
    
    strMsg = [self strReplaceAsciCodes:strMsg];
//    strMsg = [strMsg stringByReplacingOccurrencesOfString:@"&#PD;" withString:[NSString stringWithFormat:@"%ld",(long)intSelected+1]];
    // tag : 505
    [UIAlertController showAlertInViewController:self withTitle:@"Delete Alert" message:[NSString stringWithFormat:@"Are you sure you want to delete presentation: %@?",strMsg] cancelButtonTitle:@"Cancel" destructiveButtonTitle:@"OK" otherButtonTitles:nil tapBlock:^(UIAlertController * _Nonnull controller, UIAlertAction * _Nonnull action, NSInteger buttonIndex) {
        [self dismissViewControllerAnimated:YES completion:nil];
        if(buttonIndex == controller.cancelButtonIndex)
        {
            self->intSelected = -11;
            [self.tblPartUserSequences reloadData];
        }
        else
        {
            [self deleteWholeRow:self->intSelected];
        }
    }];
}

-(void)btnCopyUDpresentationClick:(UIButton *)sender
{
    NSInteger aIndex = sender.tag;
//    NSMutableArray *aMutArrRowData = [appDelegate.arrUserDefineSeq[aIndex] mutableCopy];
    
    // Version 3.0 Changes
    // If any file in original presentation contains orange color (Means any file is selected) copy also contains that orange color....
    // So create deep copy of an original array...
    // The following line was replaced because it used deprecated calls:
    //NSMutableArray* aMutArrRowData = [NSKeyedUnarchiver unarchiveObjectWithData:[NSKeyedArchiver archivedDataWithRootObject:appDelegate.arrUserDefineSeq[aIndex]]];
    NSError *error = nil;
    NSData *archivedData = [NSKeyedArchiver archivedDataWithRootObject:appDelegate.arrUserDefineSeq[aIndex] requiringSecureCoding:YES error:&error];
    NSMutableArray *aMutArrRowData = [NSKeyedUnarchiver unarchivedObjectOfClass:[NSMutableArray class] fromData:archivedData error:&error];
    if (error) {
        NSLog(@"Error unarchiving data: %@", error);
    }

    NSInteger count = appDelegate.arrUserDefineSeq.count;

    NSMutableDictionary *aMutDictSeq = mutDicSequence[[NSString stringWithFormat:@"%ld",(long)aIndex+1]];
    NSString *aStrName = aMutDictSeq[SEQUENCE_NAME];
    int seqIndex = [self getIndexForPresentationName: aStrName];
    
    // Presentation Sequence Name changes
    NSMutableDictionary *aMutDictSeqNew = [[NSMutableDictionary alloc] init];
    aMutDictSeqNew[SEQUENCE_NAME] = aStrName;
    aMutDictSeqNew[SEQUENCE_INDEX] = [NSString stringWithFormat:@"%d", seqIndex];
    
    [appDelegate.arrUserDefineSeq insertObject:aMutArrRowData atIndex:count - 1];
    
    [mutDicSequence setValue:aMutDictSeqNew forKey:[NSString stringWithFormat:@"%ld",(long)appDelegate.arrUserDefineSeq.count - 1]];
    if (self.tblPartUserSequences.contentSize.height > self.tblPartUserSequences.frame.size.height)
    {

        CGPoint aScrollPoint = CGPointMake(0, self.tblPartUserSequences.contentSize.height - self.tblPartUserSequences.frame.size.height + 70);
        [self.tblPartUserSequences setContentOffset:aScrollPoint animated:YES];
        
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self.tblPartUserSequences reloadData];
        });
    }
    else
    {
        [self.tblPartUserSequences reloadData];
    }
    [self savingImagesIntoCollectionFilesMaster];
}

/*
- (NSString *)getCopiedPresentationName:(NSString *)strTitle {
    
    int count = 0;
    int titleNo = 1;
    BOOL isValidTitle = FALSE;
     
    NSString *aStrTitleName = [NSString stringWithFormat:@"%@ (%d)", strTitle, titleNo];
     
    while (!isValidTitle) {
        for (NSString *aStrValue in mutDicSequence.allValues) {
         
            if ([aStrValue isEqualToString:aStrTitleName]) {
                count += 1;
            }
        }
     
        if (count > 0) {
         
            aStrTitleName = [NSString stringWithFormat:@"%@ (%d)", strTitle, titleNo];
            titleNo++;
            count = 0;
        }
        else {
            
            // Title value is not used, so we can use it...
            isValidTitle = TRUE;
        }
    }
    
    return aStrTitleName;
}
*/

-(void)setAnimationBGColor:(UIColor*)mycolor withView:(AnimationView*)myView
{
    myView.view.backgroundColor = [UIColor clearColor];
    CGFloat borderWidth = 5.0f;
    if (mycolor == [UIColor clearColor])
    {
        borderWidth = 0.0f;
    }
    CGRect temp = myView.btnVid.frame;
    temp.size.height = temp.size.height - 5;
//    myView.btnVid.frame = CGRectInset(temp, -borderWidth+1, -borderWidth+6);
    myView.btnVid.layer.borderColor = mycolor.CGColor;
    myView.btnVid.layer.borderWidth = borderWidth;
}

- (NSString *)transferCollectionNumberToNewString:(NSString *)oldStr newStr:(NSString *)newStr {
//    NSString *noPrefix = [self removePrefix:newStr];
    if ([oldStr hasPrefix:@"#"]) {
        // Extract the number from the string
        NSString *numberString = [oldStr substringFromIndex:1];
        NSInteger number = numberString.integerValue;

        //        return [NSString stringWithFormat:@"#%ld %@", (long)number, noPrefix];
        return [NSString stringWithFormat:@"#%ld %@", (long)number, newStr];
    }

    return newStr;
}

- (NSString *)removePrefix:(NSString *)str {
    if ([str hasPrefix:@"#"]) {
        // Extract the number from the string
        NSString *numberString = [str substringFromIndex:1];
        NSInteger number = numberString.integerValue;
        
        if (number != 0) {
            return [str substringFromIndex:[NSString stringWithFormat:@"%ld", (long)number].length+2]; // the +2 accounts for the # and the ' ' in e.g, "#42 Beetlejuice"
        }
    }

    return str;
}

-(void)cellConfigureCollection2Else:(AnimationView *)animation indexPath:(NSIndexPath *)indexPath WithcollectionView:(UICollectionView *)collectionView WithCell:(UICollectionViewCell *)cell
{
    NSUInteger totalMovies = [self getTotalCountFromArray:appDelegate.arrSearchMovie];
    NSUInteger totalPictures = [self getTotalCountFromArray:appDelegate.arrSearchImage];
    NSUInteger totalPresentation = [self getTotalCountFromArray:appDelegate.arrSearchPresentation];
    
    if (appDelegate.arrSearchMovie.count > 0 || appDelegate.arrSearchImage.count > 0 || appDelegate.arrSearchPresentation.count > 0) {
        lbl.text = [NSString stringWithFormat:@"Search found: %lu animations, %lu pictures, and %lu presentation templates", totalMovies, totalPictures, totalPresentation];
    } else {
        lbl.text = @"Tap the lock button and drag objects to create or edit your own presentations";
    }
    
    if (appDelegate.intSelectedTab == 0) {
        appDelegate.arrMedia = appDelegate.arrSelectedMovie;
    } else if (appDelegate.intSelectedTab == 1) {
        appDelegate.arrMedia = appDelegate.arrSelectedImage;
    } else if (appDelegate.intSelectedTab == 2) {
        appDelegate.arrMedia = appDelegate.arrSelectedPresenation;
    }
    
    if (appDelegate.selectedObj != nil && appDelegate.arrMedia.count > 0) {
        
        NSString *strlblDisplayMovieGroup = [NSString stringWithFormat:@"%@",appDelegate.arrMedia[0][@"fileShortDispName"]];//
        strlblDisplayMovieGroup = [self strReplaceAsciCodes:strlblDisplayMovieGroup];
        strlblDisplayMovieGroup = [strlblDisplayMovieGroup stringByReplacingOccurrencesOfString:@"\u00ac\u221e" withString:@"\u00b0"];
        
        //    [animation.btnVid setFrame:CGRectMake(2, 0, animation.view.frame.size.width - 4, animation.view.frame.size.height-2)];
        // Version 3.0 Changes
        // New frame change for Small, Medium and Large buttons and client feedback point image looks untidy...
        (animation.btnVid).frame = CGRectMake(2, 0, animation.view.frame.size.width - 4, animation.view.frame.size.height-5);
        
        (animation.btnVid).contentMode = UIViewContentModeScaleAspectFit;
        (animation.lblName).text = [NSString stringWithFormat:@"%@",strlblDisplayMovieGroup];
        (animation.lblName).font = [UIFont systemFontOfSize:[self getFontSizeForNamelbl]];
        
        //// Temporary logic
        /*
         NSArray *arr = [appDelegate.arrScrollViewCount objectAtIndex:collectionView.tag];
         if (arr.count == 7) {
         [animation.lblName setText:[NSString stringWithFormat:@"%@",arrNewVideos[indexPath.row]]];
         NSMutableDictionary *aDictData = [[appDelegate.arrScrollViewCount objectAtIndex:collectionView.tag] objectAtIndex:indexPath.row];
         [aDictData setObject:arrNewVideos[indexPath.row] forKey:@"fileName"];
         }
         */
        
        NSInteger lblNameH = [self getHeightForNamelbl];
        //    CGRect lblNameOriFrame = CGRectMake(animation.btnVid.frame.origin.x+1, animation.btnVid.frame.size.height - lblNameH - 7, animation.btnVid.frame.size.width-2, lblNameH);
        // Version 3.0 Changes
        // New frame change for Small, Medium and Large buttons and client feedback point image looks untidy...
        CGRect lblNameOriFrame = CGRectMake(animation.btnVid.frame.origin.x+1, animation.btnVid.frame.size.height - lblNameH, animation.btnVid.frame.size.width-2, lblNameH);
        
        NSString *aStr = [NSString stringWithFormat:@"%@",appDelegate.arrMedia[0][@"thumbnailPath"]];
        NSString *currCollectionID = [NSString stringWithFormat:@"%@",appDelegate.arrMedia[0][@"collectionID"]];
        
        animation.view.backgroundColor = (UIColor *)appDelegate.arrMedia[0][@"color"];
        
        animation.btnFav.tag = animation.view.tag;
        animation.btnFavMedium.tag = animation.view.tag;
        animation.btnFavLarge.tag = animation.view.tag;
        
        [self hideButtons:animation];
        
        if (appDelegate.selectedModeSize == 1) {
            [animation.btnFav setHidden:![[NSUserDefaults standardUserDefaults] boolForKey:@"Registered"]];
            [animation.btnFav setFrame:CGRectMake(40, 1, 25, 25)];
        } else if (appDelegate.selectedModeSize == 2) {
            [animation.btnFavMedium setHidden:![[NSUserDefaults standardUserDefaults] boolForKey:@"Registered"]];
            [animation.btnFavMedium setFrame:CGRectMake(85, 1, 28, 28)];
        } else {
            [animation.btnFavLarge setFrame:CGRectMake(115, 1, 35, 35)];
            [animation.btnFavLarge setHidden:![[NSUserDefaults standardUserDefaults] boolForKey:@"Registered"]];
        }
                
        NSString *favourite = [NSString stringWithFormat:@"%@",appDelegate.arrMedia[0][@"isFavourite"]];
        [animation.btnFav setSelected:[favourite isEqualToString:@"1"]];
        [animation.btnFavMedium setSelected:[favourite isEqualToString:@"1"]];
        [animation.btnFavLarge setSelected:[favourite isEqualToString:@"1"]];
        
        [animation.btnFav addTarget:self action:@selector(favUnFav:) forControlEvents:UIControlEventTouchUpInside];
        [animation.btnFavMedium addTarget:self action:@selector(favUnFav:) forControlEvents:UIControlEventTouchUpInside];
        [animation.btnFavLarge addTarget:self action:@selector(favUnFav:) forControlEvents:UIControlEventTouchUpInside];
        
        //Orange BackGroundColor Cell
        if ([animation.view.backgroundColor isEqual:animationBGColor]) {
            [self setAnimationBGColor:animationBGColor withView:animation];
            lblNameOriFrame.origin.x = lblNameOriFrame.origin.x+1;
            lblNameOriFrame.size.width = lblNameOriFrame.size.width -2;
        }
        //Blue BackGroundColor Cell
        else if ([animation.view.backgroundColor isEqual:redColor])
        {
            [self setAnimationBGColor:redColor withView:animation];
            lblNameOriFrame.origin.x = lblNameOriFrame.origin.x+1;
            lblNameOriFrame.size.width = lblNameOriFrame.size.width -2;
        } else {
            [self setAnimationBGColor:[UIColor clearColor] withView:animation];
        }
        
        (animation.lblName).frame = lblNameOriFrame;
        
        NSString *isHide = [NSString stringWithFormat:@"%@",appDelegate.arrMedia[0][@"IsHidden"]];
        NSString *isUserDefine = [NSString stringWithFormat:@"%@",appDelegate.arrMedia[0][@"isUserDefine"]];
        
        [animation.btnVid setImage:[UIImage imageWithContentsOfFile:aStr] forState:UIControlStateNormal];
        
        if ([currCollectionID isEqualToString:@"-100"])
        {
            (animation.lblName).frame = CGRectMake(0, 0, 0, 0);
            [animation.btnVid setImage:[UIImage imageNamed:@"AddMoreImage.png"] forState:UIControlStateNormal];
            animation.btnVid.layer.borderWidth = 0.0;
            [animation.btnFav setHidden:YES];
            [animation.btnFavMedium setHidden:YES];
            [animation.btnFavLarge setHidden:YES];
        }
        else if([isHide isEqualToString:@"1"] && appDelegate.intSelectedTab == 1)
        {
            (animation.lblName).frame = CGRectMake(0, 0, 0, 0);
            [animation.btnVid setImage:[UIImage imageNamed:@"hiddenpic.png"] forState:UIControlStateNormal];
        }
        
        if(appDelegate.intSelectedTab == 1)
        {
            animation.btnShowHideImgs = [[UIButton alloc] init];
            animation.btnShowHideImgs.frame = CGRectMake(150, 15, 40, 25);
            animation.btnShowHideImgs.tag = collectionView.tag;
            //        if(isLocked)
            //        {
            //            if (collectionView.tag == appDelegate.arrScrollViewCount.count-1 || [isUserDefine isEqualToString:@"0"]) {
            //                animation.btnShowHideImgs.hidden = YES;
            //            }
            //            else
            //            {
            //                animation.btnShowHideImgs.hidden = NO;
            //            }
            //        }
            //        else
            //        {
            //            animation.btnShowHideImgs.hidden = NO;
            //        }
            
            if ([isUserDefine isEqualToString:@"0"] || collectionView.tag == appDelegate.arrMedia.count-1)
            {
                animation.btnShowHideImgs.hidden = YES;
            }
            else
            {
                animation.btnShowHideImgs.hidden = NO;
            }
            
            if ([isHide isEqualToString:@"1"])
            {
                [animation.btnShowHideImgs setImage:[UIImage imageNamed:@"btnUnHide.png"] forState:UIControlStateNormal];
                [animation.btnShowHideImgs addTarget:self action:@selector(btnUnHideSectionImgsClick:) forControlEvents:(UIControlEventTouchUpInside)];
            }
            else
            {
                [animation.btnShowHideImgs setImage:[UIImage imageNamed:@"btnHide.png"] forState:UIControlStateNormal];
                [animation.btnShowHideImgs addTarget:self action:@selector(btnHideSectionImgsClick:) forControlEvents:(UIControlEventTouchUpInside)];
            }
            NSLog(@"=== %ld %@",(long)collectionView.tag, isHide);
        }
        else
        {
            animation.btnShowHideImgs.frame = CGRectMake(00, 0, 00, 00);
            animation.btnShowHideImgs.hidden = YES;
        }
        
        (animation.lblTitle).frame = CGRectMake(5, 10, 185, collectionView.frame.size.height-20);
        
        [self prepareMutDicDisplayMvGroupName:@"MOVIE"];
        
        NSString *strTitle = [NSString stringWithFormat:@"%@",mutDicDisplayMvGroupName[[NSString stringWithFormat:@"%@",appDelegate.arrMedia[0][@"collectionID"]]]];
        strTitle = [self strReplaceAsciCodes:strTitle];
        if ([isUserDefine isEqualToString:@"1"] && [isHide isEqualToString:@"1"]) {
            strTitle = [self transferCollectionNumberToNewString:strTitle newStr:PICTURES_HIDDEN];
        }
        if ([strTitle isEqualToString:@"Add Pictures"])
        {
            animation.lblName.hidden = YES;
        }
        else
        {
            animation.lblName.hidden = NO;
        }
        
        (animation.lblTitle).text = [NSString stringWithFormat:@"%@",strTitle];
        (animation.lblTitle).textColor = [UIColor colorWithRed:0.925 green:1.000 blue:0.525 alpha:1.000];
        
        NSString *aStrUserDefine = [appDelegate.arrMedia[0] valueForKey:@"isUserDefine"];
        
        // Set Tap Gesture in Title for Images segment...
        // Only User defined albums name can be edited...
        if (appDelegate.intSelectedTab == 1 && ![strTitle isEqualToString:@"Add Pictures"] && [aStrUserDefine isEqualToString:@"1"]) {
            
            UITapGestureRecognizer *tapRecognizer_title = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(editImageAlbumName:)];
            [animation.lblTitle setUserInteractionEnabled:YES];
            tapRecognizer_title.delegate = self;
            (animation.lblTitle).tag = collectionView.tag+1;
            [animation.lblTitle addGestureRecognizer:tapRecognizer_title];
            (animation.lblTitle).numberOfLines = 0;
            
        }
        
        if ([self checkFileIdFromUD:[NSString stringWithFormat:@"%@",appDelegate.arrMedia[0][@"filesID"]]]) {
            //        [animation.lblTitle setFrame:CGRectMake(5, 12 , 180, collectionView.frame.size.height-12)];
            [animation.lblTitle setBackgroundColor:redColor];
            
            isFilePlayed = YES;
        }
        [collectionView addSubview:animation.lblTitle];
        
        [collectionView addSubview:animation.btnShowHideImgs];
        [collectionView bringSubviewToFront:animation.btnShowHideImgs];
        
        panRecognizer = [[UIPanGestureRecognizer alloc] initWithTarget:self action:@selector(moveVideoFromAnimation:)];
        panRecognizer.minimumNumberOfTouches = 1;
        panRecognizer.maximumNumberOfTouches = 1;
        panRecognizer.delegate = self;
        [animation.view addGestureRecognizer:panRecognizer];
        
        if(appDelegate.intSelectedTab == 1)
        {
            NSInteger count = [appDelegate.arrMedia count];
            if (indexPath.row == count - 1 && [aStrUserDefine isEqualToString:@"1"]) {
                [animation.view removeGestureRecognizer:panRecognizer];
            }
        }
        
        // Show delete button for User defined albums...
        if (appDelegate.intSelectedTab == 1 && ![strTitle isEqualToString:@"Add Pictures"] && [aStrUserDefine isEqualToString:@"1"]) {
            
            NSInteger count = [appDelegate.arrSelectedMovie count];
            if (indexPath.row == count - 1) {
                // Last row is add button...
                animation.btnDelete.hidden = TRUE;
            }
            else
            {
                animation.btnDelete.hidden = FALSE;
                animation.btnDelete.tag = animation.view.tag;
                animation.btnDelete.frame = CGRectMake(animation.btnVid.frame.origin.x+2, 6, 20, 20);
                [animation.btnDelete addTarget:self action:@selector(btnDeleteImageClick:) forControlEvents:(UIControlEventTouchUpInside)];
            }
            animation.btnLeftCorner.hidden = FALSE;
            animation.btnCopyUDpresentation.hidden = TRUE;
            animation.btnLeftCorner.tag = collectionView.tag;
            float xValue = -2;
            float yValue = 2.5;
            
            animation.btnLeftCorner.frame = CGRectMake(xValue,yValue,25,animation.lblTitle.frame.size.height/2);
            [animation.lblTitle addSubview:animation.btnLeftCorner];
            [animation.lblTitle bringSubviewToFront:animation.btnLeftCorner];
            
            [animation.btnLeftCorner addTarget:self action:@selector(btnDeleteImageAlbumClick:) forControlEvents:(UIControlEventTouchUpInside)];
            if (intSelected == collectionView.tag) {
                // Have to delete
                (animation.btnLeftCorner).backgroundColor = [UIColor clearColor];
                [self btnImage:animation.btnLeftCorner withName:@"Yellow.png"];
            }else{
                // Not delete
                //            if (isLocked) {
                //                [self btnImage:animation.btnLeftCorner withName:@"gray.png"];
                //            }else{
                [self btnImage:animation.btnLeftCorner withName:@"Red.png"];
                //            }
            }
            
        }
        else
        {
            animation.btnDelete.hidden = TRUE;
            animation.btnLeftCorner.hidden = TRUE;
            animation.btnCopyUDpresentation.hidden = TRUE;
        }
        
        // Show info and copy sequnce button for Presenations Tab...
        if (appDelegate.intSelectedTab == 2) {
            animation.btnCopyUDpresentation.hidden = TRUE;
            animation.btnInfoSequence.hidden = FALSE;
            animation.btnInfoSequence.tag = collectionView.tag;
            
            animation.btnCopySequence.hidden = FALSE;
            animation.btnCopySequence.tag = collectionView.tag;
            
            [animation.imgLeftCorner setImage:[UIImage imageNamed:@"leftCornerNew"]];
            animation.btnFavSequence.hidden = ![[NSUserDefaults standardUserDefaults] boolForKey:@"Registered"];
            
            float xValue = 100;
            float yValue = -5;
            
            animation.btnInfoSequence.frame = CGRectMake(xValue,yValue,25,25);
            animation.btnCopySequence.frame = CGRectMake(135,yValue,25,25);
            animation.btnFavSequence.frame = CGRectMake(170,0,15,15);
            
            [collectionView addSubview:animation.btnInfoSequence];
            [collectionView bringSubviewToFront:animation.btnInfoSequence];
            
            [collectionView addSubview:animation.btnCopySequence];
            [collectionView bringSubviewToFront:animation.btnCopySequence];
            
            [collectionView addSubview:animation.btnFavSequence];
            [collectionView bringSubviewToFront:animation.btnFavSequence];
            
            
            [animation.btnInfoSequence addTarget:self action:@selector(btnInfoSequenceClick:) forControlEvents:(UIControlEventTouchUpInside)];
            [animation.btnCopySequence addTarget:self action:@selector(btnCopySequenceClick:) forControlEvents:(UIControlEventTouchUpInside)];
            [animation.btnFavSequence addTarget:self action:@selector(btnFavSequenceClick:) forControlEvents:(UIControlEventTouchUpInside)];
            
            if (intSelectedPresentation == collectionView.tag) {
                
                [self btnImage:animation.btnInfoSequence withName:@"btnInfoSelected.png"];
            }
            else
            {
                [self btnImage:animation.btnInfoSequence withName:@"btnInfo.png"];
            }
        }
        else
        {
        }
        
    } else {
        //    NSArray *arrNewVideos = @[@"Open-Close All Muscles", @"Ideal Tooth Contacts", @"Canine Guidance", @"Bruxing Muscles Close-up", @"Molars: Contact Comparison", @"Fractured Tooth Comparison", @"Slide no Muscles"];
        
        
        NSString *strlblDisplayMovieGroup = [NSString stringWithFormat:@"%@",(appDelegate.arrScrollViewCount)[collectionView.tag][indexPath.row][@"fileShortDispName"]];//
        strlblDisplayMovieGroup = [self strReplaceAsciCodes:strlblDisplayMovieGroup];
        strlblDisplayMovieGroup = [strlblDisplayMovieGroup stringByReplacingOccurrencesOfString:@"\u00ac\u221e" withString:@"\u00b0"];
        
        //    [animation.btnVid setFrame:CGRectMake(2, 0, animation.view.frame.size.width - 4, animation.view.frame.size.height-2)];
        // Version 3.0 Changes
        // New frame change for Small, Medium and Large buttons and client feedback point image looks untidy...
        (animation.btnVid).frame = CGRectMake(2, 0, animation.view.frame.size.width - 4, animation.view.frame.size.height-5);
        
        (animation.btnVid).contentMode = UIViewContentModeScaleAspectFit;
        (animation.lblName).text = [NSString stringWithFormat:@"%@",strlblDisplayMovieGroup];
        (animation.lblName).font = [UIFont systemFontOfSize:[self getFontSizeForNamelbl]];
        
        //// Temporary logic
        /*
         NSArray *arr = [appDelegate.arrScrollViewCount objectAtIndex:collectionView.tag];
         if (arr.count == 7) {
         [animation.lblName setText:[NSString stringWithFormat:@"%@",arrNewVideos[indexPath.row]]];
         NSMutableDictionary *aDictData = [[appDelegate.arrScrollViewCount objectAtIndex:collectionView.tag] objectAtIndex:indexPath.row];
         [aDictData setObject:arrNewVideos[indexPath.row] forKey:@"fileName"];
         }
         */
        
        NSInteger lblNameH = [self getHeightForNamelbl];
        //    CGRect lblNameOriFrame = CGRectMake(animation.btnVid.frame.origin.x+1, animation.btnVid.frame.size.height - lblNameH - 7, animation.btnVid.frame.size.width-2, lblNameH);
        // Version 3.0 Changes
        // New frame change for Small, Medium and Large buttons and client feedback point image looks untidy...
        CGRect lblNameOriFrame = CGRectMake(animation.btnVid.frame.origin.x+1, animation.btnVid.frame.size.height - lblNameH, animation.btnVid.frame.size.width-2, lblNameH);
        
        NSString *aStr = [NSString stringWithFormat:@"%@",(appDelegate.arrScrollViewCount)[collectionView.tag][indexPath.row][@"thumbnailPath"]];
        NSString *currCollectionID = [NSString stringWithFormat:@"%@",(appDelegate.arrScrollViewCount)[collectionView.tag][indexPath.row][@"collectionID"]];
        
        animation.view.backgroundColor = (UIColor *)(appDelegate.arrScrollViewCount)[collectionView.tag][indexPath.row][@"color"];
        
        [self hideButtons:animation];
        
        if (appDelegate.intSelectedTab != 2) {
            if (appDelegate.selectedModeSize == 1) {
                [animation.btnFav setHidden:![[NSUserDefaults standardUserDefaults] boolForKey:@"Registered"]];
                [animation.btnFav setFrame:CGRectMake(40, 1, 25, 25)];
            } else if (appDelegate.selectedModeSize == 2) {
                [animation.btnFavMedium setHidden:![[NSUserDefaults standardUserDefaults] boolForKey:@"Registered"]];
                [animation.btnFavMedium setFrame:CGRectMake(85, 1, 28, 28)];
            } else {
                [animation.btnFavLarge setFrame:CGRectMake(115, 1, 35, 35)];
                [animation.btnFavLarge setHidden:![[NSUserDefaults standardUserDefaults] boolForKey:@"Registered"]];
            }
        }
        
        animation.btnFav.tag = animation.view.tag;
        animation.btnFavMedium.tag = animation.view.tag;
        animation.btnFavLarge.tag = animation.view.tag;
        
        
        NSString *favourite = [NSString stringWithFormat:@"%@",(appDelegate.arrScrollViewCount)[collectionView.tag][indexPath.row][@"isFavourite"]];
        [animation.btnFav setSelected:[favourite isEqualToString:@"1"]];
        
        [animation.btnFav addTarget:self action:@selector(favUnFav:) forControlEvents:UIControlEventTouchUpInside];
        
        [animation.btnFavMedium setSelected:[favourite isEqualToString:@"1"]];
        
        [animation.btnFavMedium addTarget:self action:@selector(favUnFav:) forControlEvents:UIControlEventTouchUpInside];
        
        [animation.btnFavLarge setSelected:[favourite isEqualToString:@"1"]];
        
        [animation.btnFavLarge addTarget:self action:@selector(favUnFav:) forControlEvents:UIControlEventTouchUpInside];
        
        //Orange BackGroundColor Cell
        if ([animation.view.backgroundColor isEqual:animationBGColor]) {
            [self setAnimationBGColor:animationBGColor withView:animation];
            lblNameOriFrame.origin.x = lblNameOriFrame.origin.x+1;
            lblNameOriFrame.size.width = lblNameOriFrame.size.width -2;
        }
        //Blue BackGroundColor Cell
        else if ([animation.view.backgroundColor isEqual:redColor])
        {
            [self setAnimationBGColor:redColor withView:animation];
            lblNameOriFrame.origin.x = lblNameOriFrame.origin.x+1;
            lblNameOriFrame.size.width = lblNameOriFrame.size.width -2;
        } else {
            [self setAnimationBGColor:[UIColor clearColor] withView:animation];
        }
        
        (animation.lblName).frame = lblNameOriFrame;
        
        NSString *isHide = [NSString stringWithFormat:@"%@",(appDelegate.arrScrollViewCount)[collectionView.tag][0][@"IsHidden"]];
        NSString *isUserDefine = [NSString stringWithFormat:@"%@",(appDelegate.arrScrollViewCount)[collectionView.tag][0][@"isUserDefine"]];
        
        [animation.btnVid setImage:[UIImage imageWithContentsOfFile:aStr] forState:UIControlStateNormal];
        
        if ([currCollectionID isEqualToString:@"-100"])
        {
            (animation.lblName).frame = CGRectMake(0, 0, 0, 0);
            [animation.btnVid setImage:[UIImage imageNamed:@"AddMoreImage.png"] forState:UIControlStateNormal];
            animation.btnVid.layer.borderWidth = 0.0;
            [animation.btnFav setHidden:YES];
            [animation.btnFavMedium setHidden:YES];
            [animation.btnFavLarge setHidden:YES];
        }
        else if([isHide isEqualToString:@"1"] && appDelegate.intSelectedTab == 1)
        {
            (animation.lblName).frame = CGRectMake(0, 0, 0, 0);
            [animation.btnVid setImage:[UIImage imageNamed:@"hiddenpic.png"] forState:UIControlStateNormal];
        }
        
        if(appDelegate.intSelectedTab == 1)
        {
            animation.btnShowHideImgs = [[UIButton alloc] init];
            animation.btnShowHideImgs.frame = CGRectMake(150, 15, 40, 25);
            animation.btnShowHideImgs.tag = collectionView.tag;
            //        if(isLocked)
            //        {
            //            if (collectionView.tag == appDelegate.arrScrollViewCount.count-1 || [isUserDefine isEqualToString:@"0"]) {
            //                animation.btnShowHideImgs.hidden = YES;
            //            }
            //            else
            //            {
            //                animation.btnShowHideImgs.hidden = NO;
            //            }
            //        }
            //        else
            //        {
            //            animation.btnShowHideImgs.hidden = NO;
            //        }
            
            if ([isUserDefine isEqualToString:@"0"] || collectionView.tag == appDelegate.arrScrollViewCount.count-1)
            {
                animation.btnShowHideImgs.hidden = YES;
            }
            else
            {
                animation.btnShowHideImgs.hidden = NO;
            }
            
            if ([isHide isEqualToString:@"1"])
            {
                [animation.btnShowHideImgs setImage:[UIImage imageNamed:@"btnUnHide.png"] forState:UIControlStateNormal];
                [animation.btnShowHideImgs addTarget:self action:@selector(btnUnHideSectionImgsClick:) forControlEvents:(UIControlEventTouchUpInside)];
            }
            else
            {
                [animation.btnShowHideImgs setImage:[UIImage imageNamed:@"btnHide.png"] forState:UIControlStateNormal];
                [animation.btnShowHideImgs addTarget:self action:@selector(btnHideSectionImgsClick:) forControlEvents:(UIControlEventTouchUpInside)];
            }
            NSLog(@"=== %ld %@",(long)collectionView.tag, isHide);
        }
        else
        {
            animation.btnShowHideImgs.frame = CGRectMake(00, 0, 00, 00);
            animation.btnShowHideImgs.hidden = YES;
        }
        
        //    [animation.lblTitle setFrame:CGRectMake(0, 12, 90+18, collectionView.frame.size.height-12)];
        (animation.lblTitle).frame = CGRectMake(5, 10, 185, collectionView.frame.size.height-20);
        
        NSString *fileTypeinDB = @"";
        if (appDelegate.intSelectedTab == 0) {
            fileTypeinDB = @"MOVIE";
        } else if (appDelegate.intSelectedTab == 1) {
            fileTypeinDB = @"IMAGE";
        } else {
            fileTypeinDB = @"PRESENTATION";
        }
        [self prepareMutDicDisplayMvGroupName:fileTypeinDB];
        
        NSString *strTitle = [NSString stringWithFormat:@"%@",mutDicDisplayMvGroupName[[NSString stringWithFormat:@"%@",(appDelegate.arrScrollViewCount)[collectionView.tag][indexPath.section][@"collectionID"]]]];
        strTitle = [self strReplaceAsciCodes:strTitle];
        if ([isUserDefine isEqualToString:@"1"] && [isHide isEqualToString:@"1"]) {
            strTitle = [self transferCollectionNumberToNewString:strTitle newStr:PICTURES_HIDDEN];
        }
        if ([strTitle isEqualToString:@"Add Pictures"])
        {
            animation.lblName.hidden = YES;
        }
        else
        {
            animation.lblName.hidden = NO;
        }
        
        (animation.lblTitle).text = [NSString stringWithFormat:@"%@",strTitle];
        (animation.lblTitle).textColor = [UIColor colorWithRed:0.925 green:1.000 blue:0.525 alpha:1.000];
        
        // managed fav on presentation icon
        NSString *isFavourite = [NSString stringWithFormat:@"%@",mutDicDisplayMvGroupFav[[NSString stringWithFormat:@"%@",(appDelegate.arrScrollViewCount)[collectionView.tag][indexPath.section][@"collectionID"]]]];
        [animation.btnFavSequence setSelected:[isFavourite isEqualToString:@"1"]];
        
        animation.btnFavSequence.tag = [(appDelegate.arrScrollViewCount)[collectionView.tag][indexPath.section][@"collectionID"] integerValue];
        
        NSString *aStrUserDefine = [(appDelegate.arrScrollViewCount)[collectionView.tag][indexPath.section] valueForKey:@"isUserDefine"];
        
        // Set Tap Gesture in Title for Images segment...
        // Only User defined albums name can be edited...
        if (appDelegate.intSelectedTab == 1 && ![strTitle isEqualToString:@"Add Pictures"] && [aStrUserDefine isEqualToString:@"1"]) {
            
            UITapGestureRecognizer *tapRecognizer_title = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(editImageAlbumName:)];
            [animation.lblTitle setUserInteractionEnabled:YES];
            tapRecognizer_title.delegate = self;
            (animation.lblTitle).tag = collectionView.tag+1;
            [animation.lblTitle addGestureRecognizer:tapRecognizer_title];
            (animation.lblTitle).numberOfLines = 0;
            
        }
        
        if ([self checkFileIdFromUD:[NSString stringWithFormat:@"%@",(appDelegate.arrScrollViewCount)[collectionView.tag][indexPath.row][@"filesID"]]]) {
            //        [animation.lblTitle setFrame:CGRectMake(5, 12 , 180, collectionView.frame.size.height-12)];
            [animation.lblTitle setBackgroundColor:redColor];
            
            isFilePlayed = YES;
        }
        [collectionView addSubview:animation.lblTitle];
        
        [collectionView addSubview:animation.btnShowHideImgs];
        [collectionView bringSubviewToFront:animation.btnShowHideImgs];
        
        panRecognizer = [[UIPanGestureRecognizer alloc] initWithTarget:self action:@selector(moveVideoFromAnimation:)];
        panRecognizer.minimumNumberOfTouches = 1;
        panRecognizer.maximumNumberOfTouches = 1;
        panRecognizer.delegate = self;
        [animation.view addGestureRecognizer:panRecognizer];
        
        if(appDelegate.intSelectedTab == 1)
        {
            NSInteger count = [(appDelegate.arrScrollViewCount)[collectionView.tag] count];
            if (indexPath.row == count - 1 && [aStrUserDefine isEqualToString:@"1"]) {
                [animation.view removeGestureRecognizer:panRecognizer];
                
                
            }
        }
        
        // Show delete button for User defined albums...
        if (appDelegate.intSelectedTab == 1 && ![strTitle isEqualToString:@"Add Pictures"] && [aStrUserDefine isEqualToString:@"1"]) {
            
            NSInteger count = [(appDelegate.arrScrollViewCount)[collectionView.tag] count];
            if (indexPath.row == count - 1) {
                
                // Last row is add button...
                animation.btnDelete.hidden = TRUE;
            }
            else
            {
                animation.btnDelete.hidden = FALSE;
                animation.btnDelete.tag = animation.view.tag;
                animation.btnDelete.frame = CGRectMake(animation.btnVid.frame.origin.x+2, 6, 20, 20);
                [animation.btnDelete addTarget:self action:@selector(btnDeleteImageClick:) forControlEvents:(UIControlEventTouchUpInside)];
            }
            animation.btnLeftCorner.hidden = FALSE;
            animation.btnCopyUDpresentation.hidden = TRUE;
            animation.btnLeftCorner.tag = collectionView.tag;
            float xValue = -2;
            float yValue = 2.5;
            
            animation.btnLeftCorner.frame = CGRectMake(xValue,yValue,25,animation.lblTitle.frame.size.height/2);
            [animation.lblTitle addSubview:animation.btnLeftCorner];
            [animation.lblTitle bringSubviewToFront:animation.btnLeftCorner];
            
            [animation.btnLeftCorner addTarget:self action:@selector(btnDeleteImageAlbumClick:) forControlEvents:(UIControlEventTouchUpInside)];
            if (intSelected == collectionView.tag) {
                // Have to delete
                (animation.btnLeftCorner).backgroundColor = [UIColor clearColor];
                [self btnImage:animation.btnLeftCorner withName:@"Yellow.png"];
            }else{
                // Not delete
                //            if (isLocked) {
                //                [self btnImage:animation.btnLeftCorner withName:@"gray.png"];
                //            }else{
                [self btnImage:animation.btnLeftCorner withName:@"Red.png"];
                //            }
            }
            
        }
        else
        {
            animation.btnDelete.hidden = TRUE;
            animation.btnLeftCorner.hidden = TRUE;
            animation.btnCopyUDpresentation.hidden = TRUE;
        }
        
        // Show info and copy sequnce button for Presenations Tab...
        if (appDelegate.intSelectedTab == 2) {
            animation.btnCopyUDpresentation.hidden = TRUE;
            animation.btnInfoSequence.hidden = FALSE;
            animation.btnInfoSequence.tag = collectionView.tag;
            
            animation.btnCopySequence.hidden = FALSE;
            animation.btnCopySequence.tag = collectionView.tag;
            
            [animation.imgLeftCorner setImage:[UIImage imageNamed:@"leftCornerNew"]];
            animation.btnFavSequence.hidden = ![[NSUserDefaults standardUserDefaults] boolForKey:@"Registered"];
            
            float xValue = 100;
            float yValue = -5;
            
            animation.btnInfoSequence.frame = CGRectMake(xValue,yValue,25,25);
            animation.btnCopySequence.frame = CGRectMake(135,yValue,25,25);
            animation.btnFavSequence.frame = CGRectMake(170,0,15,15);
            
            [collectionView addSubview:animation.btnInfoSequence];
            [collectionView bringSubviewToFront:animation.btnInfoSequence];
            
            [collectionView addSubview:animation.btnCopySequence];
            [collectionView bringSubviewToFront:animation.btnCopySequence];
            
            [collectionView addSubview:animation.btnFavSequence];
            [collectionView bringSubviewToFront:animation.btnFavSequence];
            
            [animation.btnInfoSequence addTarget:self action:@selector(btnInfoSequenceClick:) forControlEvents:(UIControlEventTouchUpInside)];
            [animation.btnCopySequence addTarget:self action:@selector(btnCopySequenceClick:) forControlEvents:(UIControlEventTouchUpInside)];
            [animation.btnFavSequence addTarget:self action:@selector(btnFavSequenceClick:) forControlEvents:(UIControlEventTouchUpInside)];
            
            if (intSelectedPresentation == collectionView.tag) {
                
                [self btnImage:animation.btnInfoSequence withName:@"btnInfoSelected.png"];
            }
            else
            {
                [self btnImage:animation.btnInfoSequence withName:@"btnInfo.png"];
            }
        }
        else
        {
        }
    }
}

- (NSUInteger)getTotalCountFromArray:(NSArray *)array {
    NSUInteger total = 0;
    for (NSArray *subArray in array) {
        total += subArray.count;
    }
    return total;
}

-(void)prepareMutDicDisplayMvGroupName:(NSString*)fileType {
    NSString *aStrMovieGroupName = [NSString stringWithFormat:@"select collectionID,collectionDisplayName,isUserDefine from CollectionMaster where fileType = \'%@\' Order By isUserDefine Asc, fileOrder",fileType];
    
    NSMutableArray *mutMovieGroup = [[Database sharedDatabase] getAllDataForQuery:aStrMovieGroupName];
    for (int aIntSeqDetail = 0; aIntSeqDetail < mutMovieGroup.count; aIntSeqDetail++)
    {
        NSString* str = [NSString stringWithFormat:@"%@",mutMovieGroup[aIntSeqDetail][@"collectionDisplayName"]];
        [mutDicDisplayMvGroupName setValue:str forKey:[NSString stringWithFormat:@"%@",mutMovieGroup[aIntSeqDetail][@"collectionID"]]];
    }
}

-(void)checkAllImgHidden
{
    bool isAllHide = false;
    for (int i=0; i< appDelegate.arrScrollViewCount.count-1; i++)
    {
        NSArray *objArr = appDelegate.arrScrollViewCount[i];
        for (int j=0; j<objArr.count-1; j++)
        {
            NSDictionary *objDict = objArr[j];
            if ([objDict[@"isUserDefine"] isEqualToString:@"1"] && [objDict[@"IsHidden"] isEqualToString:@"1"])
            {
                isAllHide = true;
            }
        }
    }
    
    if (!isAllHide)
    {
        [appDelegate.btnHideUnhideImages setSelected:YES];
    }
    else
    {
        [appDelegate.btnHideUnhideImages setSelected:NO];
    }
}

-(void)checkAllImgUnHidden
{
    bool isAllHide = false;
    bool isUserDefineFound = false;
    
    for (int i=0; i< appDelegate.arrScrollViewCount.count-1; i++)
    {
        NSArray *objArr = appDelegate.arrScrollViewCount[i];
        for (int j=0; j<objArr.count-1; j++)
        {
            NSDictionary *objDict = objArr[j];
            if ([objDict[@"isUserDefine"] isEqualToString:@"1"]) {
                isUserDefineFound = true;
                if ([objDict[@"IsHidden"] isEqualToString:@"0"]) {
                    isAllHide = true;
                }
            }
        }
    }
    
    if (!isAllHide && isUserDefineFound)
    {
        [appDelegate.btnHideUnhideImages setSelected:NO];
    }
    else
    {
        [appDelegate.btnHideUnhideImages setSelected:YES];
    }
    
}

- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
    
    return [appDelegate getObjectFrame];
}

- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout minimumInteritemSpacingForSectionAtIndex:(NSInteger)section {
    return 0.0;
}
- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout minimumLineSpacingForSectionAtIndex:(NSInteger)section{
    return 0.0;
}

#pragma mark - Tap Gesture Methods
- (void)editImageAlbumName:(id)sender {
    
    isEditImageAlbumName = YES;
    UITapGestureRecognizer *tapGest = (UITapGestureRecognizer*)sender;
    UILabel *lblName = (UILabel*)tapGest.view;
    intCurrentRowIndex = (int)lblName.tag - 1;
    
    // Only allow user to change Album name if album is not currently hidden
    if ([lblName.text rangeOfString:@"Pictures hidden"].location == NSNotFound) {
        [self showAlertForEditImageAlbumName:lblName.text];
    } else {
        UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"Error"
                                                                       message:@"Cannot change image collection name while the collection is hidden."
                                                                preferredStyle:UIAlertControllerStyleAlert];
        UIAlertAction *okAction = [UIAlertAction actionWithTitle:@"OK"
                                                           style:UIAlertActionStyleDefault
                                                         handler:nil];
        [alert addAction:okAction];
        [self presentViewController:alert animated:YES completion:nil];
    }
}

- (void)tapVideo : (id)sender {
    
    if(((UITapGestureRecognizer*)sender).state == UIGestureRecognizerStateBegan) {
        gestureCount++;
    }
    
    if(((UITapGestureRecognizer*)sender).state == UIGestureRecognizerStateEnded) {
        canPan = NO;
        gestureCount = 0;
    }
    intSelectedPanl = [sender view].tag % 1000;
    intScrollIndex = (int)([sender view].tag / 1000) + 1;
    if (isLocked != FALSE)
    {
        appDelegate.mutArrPlayVideo = (appDelegate.arrScrollViewCount)[intScrollIndex-1] ;
        appDelegate.intSelectedVideo = intSelectedPanl;
        UIView *view = (self.view).superview;
        NSArray *temp = view.subviews;
        for (int i = 0 ; i < temp.count; i++)
        {
            UIView *vi = temp[i];
            if (vi.tag == 100)
            {
                UIButton *btn_temp = (UIButton *)[vi viewWithTag:20];
                btn_temp.selected = FALSE;
            }
        }
        appDelegate.intSelectedScrNo = intScrollIndex;
        (appDelegate.arrScrollViewCount)[intScrollIndex-1][intSelectedPanl][@"isPlayed"] = @"1";
        NSString *aStrUpdateSql = [NSString stringWithFormat:@"Update LocalFileMaster set isPlayed = '1' where filePath=\'%@\'",(appDelegate.arrScrollViewCount)[intScrollIndex-1][intSelectedPanl][@"filePath"]];
        [[Database sharedDatabase]Update:aStrUpdateSql];
        appDelegate.boolSelectedPlaylist = NO;
        [videoPlayDelegate PlayVideoSelected:sender];
        [self.view removeFromSuperview];
    }
}

- (void)tapVideoForPlay: (id)sender :(long)recTg{
    
    intSelectedPanl = [sender view].tag % 1000;
    intScrollIndex = (int)([sender view].tag / 1000) + 1;
    
    //===================
    // Version 2.4 Changes
    //===================
    if (recTg != 101) {
        
        // Don't show presentation info for User define Sequences(bottom tableview)...
        appDelegate.isPresentationInfoOpen = NO;
        appDelegate.isReOpenPresentationInfo = NO;
    }
    
    //    if (isLocked != FALSE) {
    if (recTg == 101) {
        [[NSUserDefaults standardUserDefaults]setBool:YES forKey:@"PlayAnimation"];
        [[NSUserDefaults standardUserDefaults]synchronize];
        appDelegate.mutArrPlayVideo = (appDelegate.arrScrollViewCount)[intScrollIndex-1] ;
    }else{
        [[NSUserDefaults standardUserDefaults]setBool:NO forKey:@"PlayAnimation"];
        [[NSUserDefaults standardUserDefaults]synchronize];
        appDelegate.mutArrPlayVideo = (appDelegate.arrUserDefineSeq)[intScrollIndex-1] ;
        
    }
    
    appDelegate.intSelectedVideo = intSelectedPanl;
    UIView *view = (self.view).superview;
    NSArray *temp = view.subviews;
    for (int i = 0 ; i < temp.count; i++)
    {
        UIView *vi = temp[i];
        if (vi.tag == 100)
        {
            UIButton *btn_temp = (UIButton *)[vi viewWithTag:20];
            
            btn_temp.selected = FALSE;
        }
    }
    appDelegate.intSelectedScrNo = intScrollIndex;
    NSString *filePath;
    if (recTg == 101) {
        if (appDelegate.selectedObj != nil) {
            (appDelegate.arrScrollViewCount)[0][@"isPlayed"] = @"1";
            filePath = (appDelegate.arrScrollViewCount)[0][@"filePath"];
        } else {
            (appDelegate.arrScrollViewCount)[intScrollIndex-1][intSelectedPanl][@"isPlayed"] = @"1";
            filePath = (appDelegate.arrScrollViewCount)[intScrollIndex-1][intSelectedPanl][@"filePath"];
        }
    }else{
        (appDelegate.arrUserDefineSeq)[intScrollIndex-1][intSelectedPanl][@"isPlayed"] = @"1";
        
        filePath = (appDelegate.arrUserDefineSeq)[intScrollIndex-1][intSelectedPanl][@"filePath"];
    }
    if (filePath.length > 0) {
        appDelegate.intPrevSelectedTab = appDelegate.intSelectedTab;
        appDelegate.boolSelectedPlaylist = NO;
        [videoPlayDelegate PlayVideoSelected:sender];
        [self.view removeFromSuperview];
    }
    //    }
}

#pragma mark - Animation Wobble
/* New Change 07-05-18 Trying to convert ARC...
 - (void) wobbleEnded:(NSString *)animationID finished:(NSNumber *)finished context:(void *)context {
 if ([finished boolValue]) {
 UIView* item = (UIView *)context;
 item.transform = CGAffineTransformIdentity;
 CGAffineTransform leftWobble = CGAffineTransformRotate(CGAffineTransformIdentity, RADIANS(-5.0));
 CGAffineTransform rightWobble = CGAffineTransformRotate(CGAffineTransformIdentity, RADIANS(5.0));
 item.transform = leftWobble;  // starting point
 [UIView beginAnimations:@"wobble" context:item];
 [UIView setAnimationRepeatAutoreverses:YES]; // important
 [UIView setAnimationRepeatCount:1000];
 [UIView setAnimationDuration:0.25];
 [UIView setAnimationDelegate:self];
 [UIView setAnimationDidStopSelector:@selector(wobbleEnded:finished:context:)];
 item.transform = rightWobble; // end here & auto-reverse
 [UIView commitAnimations];
 }
 }
 */

#pragma mark - User Function
- (void)btnHideUnhideImagesClicked:(UIButton*)sender
{
    //    if (isLocked)
    //    {
    if(sender.selected)
    {
        [self hideShowAllImportedImages:@"1"];
        [sender setSelected:NO];
    }
    else
    {
        [self hideShowAllImportedImages:@"0"];
        [sender setSelected:YES];
    }
    //    }
    //    else
    //    {
    //        UIButton *btnLock = (UIButton *)[self.view viewWithTag:121];
    //        [self lockAnimation:btnLock];
    //    }
    
}

-(void)hideShowAllImportedImages:(NSString*)srtVal
{
    [appDelegate hideShowAllImportedImages:srtVal];
    [self getNoOfCollectionsAndItsContentsUD];
    [self getDataForImageSection];
    
    if (appDelegate.intSelectedTab == 1)
    {
        // This is done regarding client comment the picture panel jumps to the top on click of hide/unhide.
        appDelegate.isScrollToSelectedIndex = NO;
        [self MySegmentControlAction:btnPicture];
        appDelegate.isScrollToSelectedIndex = YES;
    }
    else if (appDelegate.intSelectedTab == 0)
    {
        selectedTabIndex = 0;
        appDelegate.arrMovie = [self getNoOfCollectionsAndItsContents];
        [self.tblPartAnimationArea reloadData];
        [self.tblPartUserSequences reloadData];
    }
    else
    {
        selectedTabIndex = 2;
        appDelegate.arrPresentation = [self getNoOfCollectionsAndItsContents];
        [self.tblPartAnimationArea reloadData];
        [self.tblPartUserSequences reloadData];
    }
    
}

- (void)btnDeleteImageAlbumClick:(UIButton *)sender {
    
    //    if (isLocked) {
    
    intSelected = sender.tag;
    [self.tblPartAnimationArea reloadData];
    // tag : 506
    [UIAlertController showAlertInViewController:self withTitle:@"Delete Alert" message:DELETE_IMAGE_ALBUM_CONFIRMATION cancelButtonTitle:@"No" destructiveButtonTitle:@"Yes" otherButtonTitles:nil tapBlock:^(UIAlertController * _Nonnull controller, UIAlertAction * _Nonnull action, NSInteger buttonIndex) {
        [self dismissViewControllerAnimated:YES completion:nil];
        if(buttonIndex == controller.cancelButtonIndex)
        {
            self->intSelected = -11;
            [self.tblPartAnimationArea reloadData];
        }
        else
        {
            [self deleteImageAlbum:self->intSelected];
        }
    }];
    //    }
    //    else
    //    {
    //        UIButton *btnLock = (UIButton *)[self.view viewWithTag:121];
    //        [self lockAnimation:btnLock];
    //    }
    
}

- (void)btnDeleteImageClick:(UIButton *)sender {
    
    //    if (isLocked) {
    
    intSelected = sender.tag;
    // tag : 507
    [UIAlertController showAlertInViewController:self withTitle:@"Delete Alert" message:DELETE_IMAGE_CONFIRMATION cancelButtonTitle:@"No" destructiveButtonTitle:@"Yes" otherButtonTitles:nil tapBlock:^(UIAlertController * _Nonnull controller, UIAlertAction * _Nonnull action, NSInteger buttonIndex) {
        [self dismissViewControllerAnimated:YES completion:nil];
        if(buttonIndex == controller.cancelButtonIndex)
        {
            self->intSelected = -11;
        }
        else
        {
            [self deleteImage:self->intSelected];
            appDelegate.btnHideUnhideImages.hidden = (appDelegate.arrImage.count == NO_USER_IMAGES + 1) ? YES : NO;
        }
    }];
    
    //    }
    //    else
    //    {
    //        UIButton *btnLock = (UIButton *)[self.view viewWithTag:121];
    //        [self lockAnimation:btnLock];
    //    }
    
}
- (void)btnUnHideSectionImgsClick:(UIButton *)sender {
    //    if (isLocked)
    //    {
    [self updateHideShowImgSection:@"0" withSelectedRow:sender.tag];
    [self checkAllImgHidden];
    
    
    //    }
    //    else
    //    {
    //        UIButton *btnLock = (UIButton *)[self.view viewWithTag:121];
    //        [self lockAnimation:btnLock];
    //    }
    
    
}
- (void)btnHideSectionImgsClick:(UIButton *)sender {
    //    if (isLocked)
    //    {
    [self updateHideShowImgSection:@"1" withSelectedRow:sender.tag];
    [self checkAllImgUnHidden];
    //    }
    //    else
    //    {
    //        UIButton *btnLock = (UIButton *)[self.view viewWithTag:121];
    //        [self lockAnimation:btnLock];
    //    }
}

- (void)updateHideShowImgSection:(NSString *)aStrval withSelectedRow:(NSInteger)rowId {
    
    NSString *aStrCollectionId = [NSString stringWithFormat:@"%@",(appDelegate.arrScrollViewCount)[rowId][0][@"collectionID"]];
    NSString *aStrUpdateQuery = [NSString stringWithFormat:@"update CollectionFilesMaster SET isHidden = '%@' where collectionID = '%@'", aStrval, aStrCollectionId];
    [[Database sharedDatabase]Update:aStrUpdateQuery];
    
    NSString *aStrCnt = [NSString stringWithFormat:@"select filesID from CollectionFilesMaster where collectionID = '%@'",aStrCollectionId];
    
    NSMutableArray *aMutResult = [[Database sharedDatabase] getAllDataForQuery:aStrCnt];
    for (int i=0; i < aMutResult.count; i++)
    {
        NSString *aStrUpdateQuery = [NSString stringWithFormat:@"update CollectionUserDefineFiles SET isHidden = '%@' where filesID = '%@'", aStrval, aMutResult[i][@"filesID"]];
        [[Database sharedDatabase]Update:aStrUpdateQuery];
    }
    [self getNoOfCollectionsAndItsContentsUD];
    [self getDataForImageSection];
    
    // This is done regarding client comment the picture panel jumps to the top on click of hide/unhide.
    appDelegate.isScrollToSelectedIndex = NO;
    [self MySegmentControlAction:btnPicture];
    appDelegate.isScrollToSelectedIndex = YES;
}

- (void)btnInfoSequenceClick:(UIButton *)sender {
    
    if (appDelegate.isPresentationInfoOpen && intSelectedPresentation != sender.tag) {
        
        // Info view is already open, just load new info...
        appDelegate.isPresentationInfoOpen = YES;
        intSelectedPresentation = sender.tag;
        [self.tblPartAnimationArea reloadData];
        [self.tblPartUserSequences reloadData];
        
        NSString *aStrUrl = [self getHtmlString:sender.tag];
        [wbView loadRequestWithURL:aStrUrl];
    }
    else if(!appDelegate.isPresentationInfoOpen)
    {
        [self showPresentationInfo:sender.tag];
        
        //        for (UIView *aView in wbView.subviews) {
        //
        //            if ([aView isKindOfClass:[UIWebView class]]) {
        //
        //                UIWebView *aWebView = (UIWebView *)aView;
        //                [aWebView.scrollView flashScrollIndicators];
        //
        //                for (UIView *aSubView in aWebView.scrollView.subviews) {
        //                    if ([aSubView isKindOfClass:[UIImageView class]]) {
        //                        object_setClass(aSubView, [AlwaysOpaqueImageView class]);
        ////                        aSubView.alpha = 1.0;
        ////                        break;
        //                    }
        //                }
        //
        //            }
        //        }
    }
    else
    {
        [self removeWebView];
    }
}

- (void)showPresentationInfo:(NSInteger)index {
    
    appDelegate.isPresentationInfoOpen = YES;
    intSelectedPresentation = index;
    [self.tblPartAnimationArea reloadData];
    [self.tblPartUserSequences reloadData];
    
    // Adjust UI...
    CGRect frame = self.tblPartAnimationArea.frame;
    frame.origin.x = PRESENTATION_INFO_VIEW_WIDTH;
    frame.size.width = frame.size.width - PRESENTATION_INFO_VIEW_WIDTH;
    
    self.tblPartAnimationArea.frame = frame;
    
    frame = self.tblPartUserSequences.frame;
    frame.origin.x = PRESENTATION_INFO_VIEW_WIDTH;
    frame.size.width = frame.size.width - PRESENTATION_INFO_VIEW_WIDTH;
    self.tblPartUserSequences.frame = frame;
    
    NSString *aStrUrl = [self getHtmlString:index];
    [wbView loadRequestWithURL:aStrUrl];
    
    self.viewPresentationTitle.frame = CGRectMake(0, 0, PRESENTATION_INFO_VIEW_WIDTH, 30);
    [self.viewPresentationInfo addSubview:self.viewPresentationTitle];
    [self.viewPresentationInfo addSubview:wbView];
    
    [self.view addSubview:self.viewPresentationInfo];
    
    [self.tblPartUserSequences reloadData];
    [self.tblPartAnimationArea reloadData];
    
}

- (void)changePresentationInfo:(NSInteger)index {
    
    // Info view is already open, just load new info...
    intSelectedPresentation = index;
    //    [self.tblPartAnimationArea reloadData];
    //    [self.tblPartUserSequences reloadData];
    
    NSString *aStrUrl = [self getHtmlString:index];
    [wbView loadRequestWithURL:aStrUrl];
    
}

- (IBAction)btnCloseInfoClick:(UIButton *)sender {
    [self removeWebView];
}

-(void)removeWebView
{
    appDelegate.isPresentationInfoOpen = NO;
    appDelegate.isReOpenPresentationInfo = NO;
    [self.tblPartAnimationArea reloadData];
    [self.tblPartUserSequences reloadData];
    
    // Reset UI...
    CGRect frame = self.tblPartAnimationArea.frame;
    frame.origin.x = 0;
    frame.size.width = frame.size.width + PRESENTATION_INFO_VIEW_WIDTH;
    
    self.tblPartAnimationArea.frame = frame;
    
    frame = self.tblPartUserSequences.frame;
    frame.origin.x = 0;
    frame.size.width = frame.size.width + PRESENTATION_INFO_VIEW_WIDTH;
    self.tblPartUserSequences.frame = frame;
    
    [self.viewPresentationInfo removeFromSuperview];
    
    intSelectedPresentation = -11;
}

- (NSString *)getHtmlString:(NSInteger)index {
    
    //    NSString *aStrFilePath = [[NSBundle mainBundle]pathForResource:@"ExamplePresentationTemplate" ofType:@"html"];
    //
    //    return aStrFilePath;
    
    NSString *aStrCollectionId = (appDelegate.arrScrollViewCount)[index][0][@"collectionID"];
    
    NSString *aStrQuery = [NSString stringWithFormat:@"select infoFilePath from CollectionMaster where collectionID = \'%@\' and fileType = \'%@\'", aStrCollectionId, mediaPresentation];
    
    NSMutableArray *aMutArrResult = [[Database sharedDatabase] getAllDataForQuery:aStrQuery];
    
    //------ For info file Path ----------//
    if (aMutArrResult.count > 0) {
        
        NSString *aStrInfoPathTemp = aMutArrResult[0][@"infoFilePath"];
        
        NSString *aStrFileName = aStrInfoPathTemp.lastPathComponent;
        aStrFileName = aStrFileName.stringByDeletingPathExtension;
        
        NSString *aStrInfoPath = [[NSBundle mainBundle]pathForResource:aStrFileName ofType:@"html"];
        
        // First check in Bundle...
        if ([[NSFileManager defaultManager] fileExistsAtPath:aStrInfoPath])
        {
            return aStrInfoPath;
        }
        else
        {
            aStrInfoPath = [NSString stringWithFormat:@"%@/%@", DOCUMENT_DIRECTORY_PATH,aStrInfoPathTemp];
            return aStrInfoPath;
        }
        
    }
    
    return [[NSBundle mainBundle] pathForResource:@"ExamplePresentationTemplate" ofType:@"html"];
}

- (void)btnFavSequenceClick:(UIButton *)sender {
    
    NSString *aStrValue = [NSString stringWithFormat:@"%@",mutDicDisplayMvGroupFav[[NSString stringWithFormat:@"%ld",(long)sender.tag]]];
    
    BOOL isFavourite = ![aStrValue boolValue];
    
    NSString *aStrUpdateQuery = [NSString stringWithFormat:@"update CollectionMaster SET isFavourite = \'%@\' where collectionID = \'%ld\'", (isFavourite ? @"1" : @"0"), (long)sender.tag];
    
    [[Database sharedDatabase]Update:aStrUpdateQuery];
    
    [UIView transitionWithView:sender
                      duration:0.3
                       options:UIViewAnimationOptionTransitionCrossDissolve
                    animations:^{
        [sender setSelected:![sender isSelected]];
    } completion:nil];
    
    NSString *aStrFav = [NSString stringWithFormat:@"%@",(isFavourite ? @"1" : @"0")];
    
    [mutDicDisplayMvGroupFav setValue:aStrFav forKey:[NSString stringWithFormat:@"%ld",(long)sender.tag]];
    NSLog(@"mutDicDisplayMvGroupFav>>> %@",mutDicDisplayMvGroupFav);
    
    mutArrDisplayMvGroupFavourite = (NSMutableArray*)[mutDicDisplayMvGroupFav allKeysForObject:@"1"];
    NSLog(@"mutArrDisplayMvGroupFavourite>>> %@",mutArrDisplayMvGroupFavourite);

    NSLog(@"appDelegate.arrPresentationFavourite>>> %@",appDelegate.arrPresentationFavourite);
    
    if (!sender.isSelected && appDelegate.isFavourite) {
        NSMutableArray *favouriteArray = [NSMutableArray array];
        for (NSArray *collection in appDelegate.arrPresentation) {
            NSMutableArray *favouriteArray1 = [NSMutableArray array];
            NSUInteger index = [collection indexOfObjectPassingTest:^BOOL(NSDictionary *obj, NSUInteger idx, BOOL *stop) {
                return [mutArrDisplayMvGroupFavourite containsObject:obj[@"collectionID"]];
            }];
            if (index != NSNotFound) {
                favouriteArray1 = (NSMutableArray*)[collection subarrayWithRange:NSMakeRange(index, collection.count - index)];
            }
            [favouriteArray addObject:favouriteArray1];
        }
        
        NSMutableArray *tempArr1 = [NSMutableArray array];
        for (NSArray *arr in favouriteArray) {
            if (arr.count > 0) {
                [tempArr1 addObject:arr];
            }
        }
        appDelegate.arrPresentationFavourite = tempArr1;
        appDelegate.arrScrollViewCount = appDelegate.arrPresentationFavourite;
        
        NSIndexSet *sections = [NSIndexSet indexSetWithIndexesInRange:NSMakeRange(0, [self.tblPartAnimationArea numberOfSections])];
        [self.tblPartAnimationArea reloadSections:sections withRowAnimation:UITableViewRowAnimationFade];
    }
}
- (void)btnCopySequenceClick:(UIButton *)sender {
    
    //===================
    // Copy whole sequence in Presentation area...
    //===================
    
    NSString *aStrTitle = [NSString stringWithFormat:@"%@",mutDicDisplayMvGroupName[[NSString stringWithFormat:@"%@",(appDelegate.arrScrollViewCount)[sender.tag][0][@"collectionID"]]]];

    NSMutableArray *aMutArrRowData = [appDelegate.arrScrollViewCount[sender.tag] mutableCopy];
    NSInteger count = appDelegate.arrUserDefineSeq.count;
    
    // Version 3.0 Changes
    // If any file in original presentation contains orange color (Means any file is selected) copy also contains that orange color....
    // So to solve double orange selection set clear color to all files...
    for (int index = 0; index < aMutArrRowData.count; index++) {
        aMutArrRowData[index][@"color"] = [UIColor clearColor];
    }
    
    // Insert object before last row (Because last row is Add New row...)
    [appDelegate.arrUserDefineSeq insertObject:aMutArrRowData atIndex:count - 1];
    
    // Check how many times title value is copied...
    // According to requirement the name of the presentation will stay the same for the 1st copy, and then with increasing numbers for the following copies.
    
    // Code commented for version 3.0 changes...
    /*
    count = 0;
    int titleNo = 1;
    BOOL isValidTitle = FALSE;
    
    NSString *aStrTitleName = [NSString stringWithFormat:@"%@", aStrTitle];
    
    while (!isValidTitle) {

        for (NSString *aStrValue in mutDicSequence.allValues) {
            
            if ([aStrValue isEqualToString:aStrTitleName]) {
                count += 1;
            }
        }
        
        if (count > 0) {
            
            aStrTitleName = [NSString stringWithFormat:@"%@ (%d)", aStrTitle, titleNo];
            titleNo++;
            count = 0;
        }
        else
        {
            // Title value is not used, so we can use it...
            isValidTitle = TRUE;
        }
    }
    */
    
    //===================
    // Version 3.0 Changes
    //===================
    // Presentation Sequence Name changes
    int seqIndex = [self getIndexForPresentationName: aStrTitle];
    
    // Presentation Sequence Name changes
    NSMutableDictionary *aMutDictSeqNew = [[NSMutableDictionary alloc] init];
    aMutDictSeqNew[SEQUENCE_NAME] = aStrTitle;
    aMutDictSeqNew[SEQUENCE_INDEX] = [NSString stringWithFormat:@"%d", seqIndex];
    
    [mutDicSequence setValue:aMutDictSeqNew forKey:[NSString stringWithFormat:@"%ld",(long)appDelegate.arrUserDefineSeq.count - 1]];
    
    if (self.tblPartUserSequences.contentSize.height > self.tblPartUserSequences.frame.size.height) {
        
        // 70px for one row which is added...
        CGPoint aScrollPoint = CGPointMake(0, self.tblPartUserSequences.contentSize.height - self.tblPartUserSequences.frame.size.height + 70);
        [self.tblPartUserSequences setContentOffset:aScrollPoint animated:YES];
        
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self.tblPartUserSequences reloadData];
        });
    }
    else
    {
        [self.tblPartUserSequences reloadData];
    }
    
    [self savingImagesIntoCollectionFilesMaster];
}

- (void)btnLockClicked {
    UIButton *btnLock = (UIButton *)[self.view viewWithTag:121];
    
//    if (appDelegate.intSelectedTab == 1)
//    {
//        appDelegate.arrScrollViewCount = [[NSMutableArray alloc] init];
//        appDelegate.arrScrollViewCount = appDelegate.arrImage;
//        mutDicDisplayMvGroupName = dictGropImages;
//    }

    if (isLocked)
    {
        [btnLock setImage:[UIImage imageNamed:@"unlocked.png"] forState:UIControlStateNormal];
        if(iscustomSequenceModified)
        {
            [[NSUserDefaults standardUserDefaults] setBool:isLocked forKey:@"isLocked"];
            [[NSUserDefaults standardUserDefaults] synchronize];
        }
    }
    else
    {
        [btnLock setImage:[UIImage imageNamed:@"locked.png"] forState:UIControlStateNormal];
        
    }
    [self savingImagesIntoCollectionFilesMaster];
    isLocked = !isLocked;

//    [self getNoOfCollectionsAndItsContentsUD];
    [_tblPartUserSequences reloadData];
    [_tblPartAnimationArea reloadData];
}

-(void)lockAnimation :(UIButton*)aBtn{
    if (!animationInProgress) {
        animationInProgress = YES;
        
        UIImageView *imgview = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"newiPodFlash"]];
//        imgview.frame = CGRectMake(474, 196, 370, 550);
        // Version 3.0 Changes
        // New frame change for Small, Medium and Large buttons...
        imgview.frame = CGRectMake(345, 196, 370, 550);
        imgview.contentMode = UIViewContentModeScaleAspectFit;
        [self.view addSubview:imgview];
        
        circleLayer = [CAShapeLayer layer];
        circleLayer.path = [UIBezierPath bezierPathWithRoundedRect:CGRectMake(imgview.bounds.origin.x, imgview.bounds.origin.y, imgview.bounds.size.width, imgview.bounds.size.height-1) cornerRadius:10.0].CGPath;
        circleLayer.strokeColor = [UIColor colorWithRed:1.000 green:0.525 blue:0.000 alpha:1.000].CGColor;
        circleLayer.fillColor = [UIColor clearColor].CGColor;
        circleLayer.shadowRadius=3.0;
        circleLayer.shadowColor=[UIColor whiteColor].CGColor;
        circleLayer.shadowOpacity=1.0f;
        circleLayer.shadowOffset=CGSizeMake(0.0, 0.0);
        [imgview.layer addSublayer:circleLayer];
        
        //Animate path
        CABasicAnimation *pathAnimation = [CABasicAnimation animationWithKeyPath:@"opacity"];
        pathAnimation.duration = 0.2f;
        pathAnimation.fromValue = @0.0f;
        pathAnimation.toValue = @1.0f;
        pathAnimation.repeatCount = 10;
        pathAnimation.autoreverses = YES;
        [circleLayer addAnimation:pathAnimation forKey:@"opacity"];
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self->circleLayer removeFromSuperlayer];
            [imgview removeFromSuperview];
            self->animationInProgress = NO;
        });
    }
}

/* old working code
-(void)lockAnimation :(UIButton*)aBtn{
    if (!animationInProgress) {
        animationInProgress = YES;
        circleLayer = [CAShapeLayer layer];
        [circleLayer setPath:[[UIBezierPath bezierPathWithRoundedRect:CGRectMake(aBtn.bounds.origin.x, aBtn.bounds.origin.y, aBtn.bounds.size.width, aBtn.bounds.size.height-2) cornerRadius:10.0] CGPath]];
        [circleLayer setStrokeColor:[[UIColor colorWithRed:1.000 green:0.525 blue:0.000 alpha:1.000] CGColor]];
        [circleLayer setFillColor:[[UIColor clearColor] CGColor]];
        circleLayer.shadowRadius=3.0;
        circleLayer.shadowColor=[UIColor whiteColor].CGColor;
        circleLayer.shadowOpacity=1.0f;
        circleLayer.shadowOffset=CGSizeMake(0.0, 0.0);
        
        [[aBtn layer] addSublayer:circleLayer];
        
        //Animate path
        CABasicAnimation *pathAnimation = [CABasicAnimation animationWithKeyPath:@"opacity"];
        pathAnimation.duration = 0.2f;
        pathAnimation.fromValue = [NSNumber numberWithFloat:0.0f];
        pathAnimation.toValue = [NSNumber numberWithFloat:1.0f];
        pathAnimation.repeatCount = 10;
        pathAnimation.autoreverses = YES;
        [circleLayer addAnimation:pathAnimation forKey:@"opacity"];
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self->circleLayer removeFromSuperlayer];
            self->animationInProgress = NO;
        });
    }
}
 */

#pragma mark - Database methods
-(NSMutableArray*)getNoOfCollectionsAndItsContents
{
    NSMutableArray *RespArr = [[NSMutableArray alloc] init];
    mutArrAnimationData = [[NSMutableArray alloc] init];
    mutArrFilesData = [[NSMutableArray alloc] init];
    mutDicDisplayMvGroupName = [[NSMutableDictionary alloc] init];
    mutDicDisplayMvGroupFav = [[NSMutableDictionary alloc] init];
    mutArrDisplayMvGroupFavourite = [[NSMutableArray alloc] init];
    
    //    [mutArrAnimationData removeAllObjects];
    //    [mutArrFilesData removeAllObjects];
    //    [mutDicDisplayMvGroupName removeAllObjects];
    
    NSString *fileTypeinDB = @"";
    NSString *mediaType = @"";
    if (selectedTabIndex == 0)
    {
        fileTypeinDB = @"MOVIE";
    }
    else if (selectedTabIndex == 1)
    {
        fileTypeinDB = @"IMAGE";
    }
    else
    {
        fileTypeinDB = @"PRESENTATION";
    }
    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES);
    NSString *documentsDir = paths[0];
    
    // Change in query to get UserDefined photos last...
    NSString *aStrMovieGroupName = [NSString stringWithFormat:@"select collectionID,collectionDisplayName,isUserDefine from CollectionMaster where fileType = \'%@\' Order By isUserDefine Asc, fileOrder",fileTypeinDB];
    
    //===================
    // Version 3.1 Changes
    //===================
    // Feature MVI file changes.
    /// Note: These changes are only for logged in user...
    if (selectedTabIndex == 2 && (userSubStat == Subscribed || userSubStat == InAppSubscribed)) {
        
        // Get data according to selected Feature Set...
        aStrMovieGroupName = [NSString stringWithFormat:@"select collectionID,collectionDisplayName,isUserDefine,isFavourite from CollectionMaster where fileType = \'%@\' AND featureID = %ld Order By isUserDefine Asc, fileOrder",fileTypeinDB, (long)appDelegate.intSelectedFeatureId];
    }
    
    NSMutableArray *mutMovieGroup = [[Database sharedDatabase] getAllDataForQuery:aStrMovieGroupName];
    for (int aIntSeqDetail = 0; aIntSeqDetail < mutMovieGroup.count; aIntSeqDetail++)
    {
        NSString* str = [NSString stringWithFormat:@"%@",mutMovieGroup[aIntSeqDetail][@"collectionDisplayName"]];
        [mutDicDisplayMvGroupName setValue:str forKey:[NSString stringWithFormat:@"%@",mutMovieGroup[aIntSeqDetail][@"collectionID"]]];
        
        if ([fileTypeinDB  isEqual: @"PRESENTATION"]) {
            NSString* isFavourite = [NSString stringWithFormat:@"%@",mutMovieGroup[aIntSeqDetail][@"isFavourite"]];
            [mutDicDisplayMvGroupFav setValue:isFavourite forKey:[NSString stringWithFormat:@"%@",mutMovieGroup[aIntSeqDetail][@"collectionID"]]];
        }
    }
    
    if (selectedTabIndex == 0)
    {
        dictGropMovie = mutDicDisplayMvGroupName;
    }
    else if (selectedTabIndex == 1)
    {
        dictGropLockImages = [mutDicDisplayMvGroupName mutableCopy];
        [mutDicDisplayMvGroupName setValue:@"Add Pictures" forKey:@"-100"];
        dictGropImages = [mutDicDisplayMvGroupName mutableCopy];
        
    }
    else
    {
        dictGropPresentation = mutDicDisplayMvGroupName;
        dictGropPresentationFav = mutDicDisplayMvGroupFav;
    }
    
    NSMutableArray *aMutResult = [mutMovieGroup mutableCopy];
    for (int aIntCnt = 0 ; aIntCnt < aMutResult.count; aIntCnt++)
    {
        NSString *isuserDefine = aMutResult[aIntCnt][@"isUserDefine"];
        mutArrFilesData = [[NSMutableArray alloc] init];
        if (aIntCnt <  aMutResult.count)
        {
            NSString *aStrQuery = [NSString stringWithFormat:@"select filesID,isHidden from CollectionFilesMaster where collectionID = \'%@\' ORDER BY order_file",aMutResult[aIntCnt][@"collectionID"]];
            NSMutableArray *aMutArrCollection = [[Database sharedDatabase] getAllDataForQuery:aStrQuery];
            
            
            if(aMutArrCollection.count > 0)
            {
                NSString *isHide = aMutArrCollection[0][@"isHidden"];
                
                for (int aIntJ = 0; aIntJ < aMutArrCollection.count; aIntJ++)
                {
                    
                    NSString *aStrFileQuery = [NSString stringWithFormat:@"select fileTitle, fileName, filePath, thumbnailPath, isPlayed, isDownloaded, fileShortDispName, fileType, moviePlaySpeed, isFavourite from LocalFileMaster where fileID = \'%@\'",aMutArrCollection[aIntJ][@"filesID"]];
                    
                    NSMutableArray *aMutArrFileDet = [[Database sharedDatabase] getAllDataForQuery:aStrFileQuery];
                    if (aMutArrFileDet.count > 0)
                    {
                        if([isHide isEqualToString:@"(null)"] || isHide == nil)
                        {
                            isHide = @"0";
                        }
                        aMutArrFileDet[0][@"IsHidden"] = isHide;
                        aMutArrFileDet[0][@"isUserDefine"] = isuserDefine;
                        aMutArrFileDet[0][@"collectionID"] = aMutResult[aIntCnt][@"collectionID"];
                        
                        aMutArrFileDet[0][@"isUserDefine"] = aMutResult[aIntCnt][@"isUserDefine"];
                        
                        aMutArrFileDet[0][@"filesID"] = aMutArrCollection[aIntJ][@"filesID"];
                        
                        //------ For Thumbh Path ----------//
                        NSString *aStrThumbnailPathTemp = aMutArrFileDet[0][@"thumbnailPath"];
                        
                        //Remove Object from Array than add object with documentDirectory path
                        [aMutArrFileDet[0]removeObjectForKey:@"thumbnailPath"];
                        
                        NSString *aStrThumbnailPath = [[NSBundle mainBundle]pathForResource:aStrThumbnailPathTemp ofType:@"jpg"];
                        
                        if ([[NSFileManager defaultManager] fileExistsAtPath:aStrThumbnailPath])
                        {
                            aMutArrFileDet[0][@"thumbnailPath"] = aStrThumbnailPath;
                        }
                        else
                        {
                            aMutArrFileDet[0][@"thumbnailPath"] = [NSString stringWithFormat:@"%@/%@",documentsDir,aStrThumbnailPathTemp];
                        }
                        
                        //------ For mov file Path ----------//
                        NSString *aStrfilePathTemp = aMutArrFileDet[0][@"filePath"];
                        [aMutArrFileDet[0]removeObjectForKey:@"filePath"];
                        NSString *title = aMutArrFileDet[0][@"fileTitle"];
                        if([title containsString:VIDEO_EXTENSION])
                        {
                            mediaType = VIDEO_EXTENSION;
                        }
                        else if([title containsString:@"jpg"])
                        {
                            mediaType = @"jpg";
                        }
                        
                        NSString *aStrFilePath = [[NSBundle mainBundle]pathForResource:aStrfilePathTemp ofType:mediaType];
                        if ([[NSFileManager defaultManager] fileExistsAtPath:aStrThumbnailPath])
                        {
                            aMutArrFileDet[0][@"filePath"] = aStrFilePath;
                        }
                        else
                        {
                            aMutArrFileDet[0][@"filePath"] = [NSString stringWithFormat:@"%@/%@",documentsDir,aStrfilePathTemp];
                        }
                        [mutArrFilesData addObject:aMutArrFileDet[0]];
                    }
                }
                // To add Array in Mutable animation files
                NSMutableArray *aMutArrTemp = [[NSMutableArray alloc] init];
                
                for (int aIntDetail = 0; aIntDetail < mutArrFilesData.count; aIntDetail++)
                {
                    if ([mutArrFilesData[aIntDetail][@"collectionID"] intValue] == [aMutResult[aIntCnt][@"collectionID"] intValue])
                    {
                        [aMutArrTemp addObject:mutArrFilesData[aIntDetail]];
                    }
                    //                [aMutArrTemp addObject:[mutArrFilesData objectAtIndex:aIntDetail]];
                }
                
                [mutArrAnimationData addObject:aMutArrTemp];
                if (aMutArrCollection.count > 0)
                {
                    
                    [RespArr addObject:[NSString stringWithFormat:@"%ld",(unsigned long)aMutArrCollection.count]];
                }
                
            }
            
        }
    }
    NSMutableArray *myArr = [self setDataIntoAnimationArr:RespArr];
    return myArr;
}

- (void)getNoOfCollectionsAndItsContentsUD {
    appDelegate.arrUserDefineSeq = [[NSMutableArray alloc] init];
    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES);
    NSString *documentsDir = paths[0];
    NSLog(@"%@",documentsDir);
    NSString *mediaType = @"";
    NSMutableArray *mutArrFilesDataUD = [[NSMutableArray alloc] init];
    NSString *aStrCnt = [NSString stringWithFormat:@"select collectionID from CollectionUserDefine"];
    
    NSMutableArray *aMutResult = [[Database sharedDatabase] getAllDataForQuery:aStrCnt];
    //===================
    // Version 3.0 Changes
    //===================
    // Presentation Sequence Name changes
    NSString *aStrSeq = [NSString stringWithFormat:@"select collectionID,sequenceName,sequenceIndex from CollectionUserDefine"];
    NSMutableArray *mutSeq = [[Database sharedDatabase] getAllDataForQuery:aStrSeq];
    
    for (int aIntSeqDetail = 0; aIntSeqDetail < mutSeq.count; aIntSeqDetail++)
    {
        NSString* str = [NSString stringWithFormat:@"%@",mutSeq[aIntSeqDetail][@"sequenceName"]];
        //===================
        // Version 3.0 Changes
        //===================
        // Presentation Sequence Name changes
        NSMutableDictionary *aMutDictSeq = [[NSMutableDictionary alloc] init];
        aMutDictSeq[SEQUENCE_NAME] = str;
        aMutDictSeq[SEQUENCE_INDEX] = [NSString stringWithFormat:@"%@",mutSeq[aIntSeqDetail][@"sequenceIndex"]];
        [mutDicSequence setValue:aMutDictSeq forKey:[NSString stringWithFormat:@"%@",mutSeq[aIntSeqDetail][@"collectionID"]]];
    }
    
    for (int aIntCnt = 0 ; aIntCnt < aMutResult.count; aIntCnt++)
    {
        if (aIntCnt <  aMutResult.count)
        {
            NSString *aStrQuery = [NSString stringWithFormat:@"select filesID from CollectionUserDefineFiles where collectionID = \'%@\' ORDER BY order_file",aMutResult[aIntCnt][@"collectionID"]];
            
            NSMutableArray *aMutArrCollection = [[Database sharedDatabase] getAllDataForQuery:aStrQuery];
            for (int aIntJ = 0; aIntJ < aMutArrCollection.count; aIntJ++)
            {
                NSString *aStrFileQuery = [NSString stringWithFormat:@"select fileTitle, fileName, filePath, thumbnailPath, isPlayed, isDownloaded, fileShortDispName, fileType, moviePlaySpeed, isFavourite from LocalFileMaster where fileID = \'%@\'",aMutArrCollection[aIntJ][@"filesID"]];
                
                NSMutableArray *aMutArrFileDet = [[Database sharedDatabase] getAllDataForQuery:aStrFileQuery];
                NSString *acurrFileIdQuery = [NSString stringWithFormat:@"select isHidden, isUserDefine from CollectionUserDefineFiles where filesID = \'%@\'",aMutArrCollection[aIntJ][@"filesID"]];

                NSMutableArray *aMutArrIsHidden = [[Database sharedDatabase] getAllDataForQuery:acurrFileIdQuery];

                if (aMutArrFileDet.count > 0)
                {
                    NSString *isHiddenStr = @"0";
                    if(aMutArrIsHidden.count > 0)
                    {
                        isHiddenStr = aMutArrIsHidden[0][@"isHidden"];
                        
                        if ([isHiddenStr isEqualToString:@"(null)"] || isHiddenStr == nil)
                        {
                            isHiddenStr = @"0";
                        }

                    }
                    
                    NSString *isUserDefineStr = @"0";
                    if(aMutArrIsHidden.count > 0)
                    {
                        isUserDefineStr = aMutArrIsHidden[0][@"isUserDefine"];
                        
                        if ([isUserDefineStr isEqualToString:@"(null)"] || isUserDefineStr == nil)
                        {
                            isUserDefineStr = @"0";
                        }
                        
                    }
                    aMutArrFileDet[0][@"IsHidden"] = isHiddenStr;
                    aMutArrFileDet[0][@"isUserDefine"] = isUserDefineStr;
                    
                    aMutArrFileDet[0][@"collectionID"] = aMutResult[aIntCnt][@"collectionID"];
                    
                    aMutArrFileDet[0][@"filesID"] = aMutArrCollection[aIntJ][@"filesID"];
                    
                    //------ For Thumbh Path ----------//
                    NSString *aStrThumbnailPathTemp = aMutArrFileDet[0][@"thumbnailPath"];
                    //Remove Object from Array than add object with documentDirectory path
                    [aMutArrFileDet[0]removeObjectForKey:@"thumbnailPath"];
                    
                    NSString *aStrThumbnailPath = [[NSBundle mainBundle]pathForResource:aStrThumbnailPathTemp ofType:@"jpg"];
                    
                    if ([[NSFileManager defaultManager] fileExistsAtPath:aStrThumbnailPath])
                    {
                        aMutArrFileDet[0][@"thumbnailPath"] = aStrThumbnailPath;
                    }
                    else
                    {
                        aMutArrFileDet[0][@"thumbnailPath"] = [NSString stringWithFormat:@"%@/%@",documentsDir,aStrThumbnailPathTemp];
                    }
                    
                    NSString *aStrfilePathTemp = aMutArrFileDet[0][@"filePath"];
                    [aMutArrFileDet[0]removeObjectForKey:@"filePath"];
                    NSString *title = aMutArrFileDet[0][@"fileTitle"];
                    if([title containsString:VIDEO_EXTENSION])
                    {
                        mediaType = VIDEO_EXTENSION;
                    }
                    else if([title containsString:@"jpg"])
                    {
                        mediaType = @"jpg";
                    }
                    NSString *aStrFilePath = [[NSBundle mainBundle]pathForResource:aStrfilePathTemp ofType:mediaType];
                    if ([[NSFileManager defaultManager] fileExistsAtPath:aStrThumbnailPath])
                    {
                        aMutArrFileDet[0][@"filePath"] = aStrFilePath;
                    }
                    else
                    {
                        aMutArrFileDet[0][@"filePath"] = [NSString stringWithFormat:@"%@/%@",documentsDir,aStrfilePathTemp];
                    }
                    [mutArrFilesDataUD addObject:aMutArrFileDet[0]];
                }
            }
            NSMutableArray *aMutArrTemp = [[NSMutableArray alloc] init];
            
            for (int aIntDetail = 0; aIntDetail < mutArrFilesDataUD.count; aIntDetail++)
            {
                if ([mutArrFilesDataUD[aIntDetail][@"collectionID"] intValue] == [aMutResult[aIntCnt][@"collectionID"] intValue])
                {
                    [aMutArrTemp addObject:mutArrFilesDataUD[aIntDetail]];
                }
            }
            
            if (aMutArrTemp.count > 0) {
                [appDelegate.arrUserDefineSeq addObject:aMutArrTemp];
            }
        }
    }
    [self addExtraRowForAddingPlaySeq];
//    appDelegate.arrUserDefineSeq;
}

- (void)savingImagesIntoCollectionFilesMaster {
    
    if (![[NSUserDefaults standardUserDefaults]boolForKey:@"PlayAnimation"]){
        if (appDelegate.arrUserDefineSeq.count > 1) {
            // If selected index is greater than data, set last index as selected index...
            if (appDelegate.intSelectedScrNo > appDelegate.arrUserDefineSeq.count - 1) {
                appDelegate.intSelectedScrNo = (int)(appDelegate.arrUserDefineSeq.count - 1);
            }
            
            appDelegate.mutArrPlayVideo = (appDelegate.arrUserDefineSeq)[appDelegate.intSelectedScrNo-1] ;//after adding or removing update playlist.
        }else{
            [[NSUserDefaults standardUserDefaults]setBool:YES forKey:@"PlayAnimation"];
            appDelegate.mutArrPlayVideo = (appDelegate.arrScrollViewCount)[0] ;//after adding or removing update playlist.
        }
        //        [videoPlayDelegate playVideoAfterLockingorRemoving:nil]; //after removing or adding new sequence generate for user defince sequence area
    }
    [self deletePlaylistData];
    [appDelegate createTableForUserDefineSqu];
    
    
    for (int aIntC = 1; aIntC <= (appDelegate.arrUserDefineSeq).count; aIntC++)
    {
        for(int aIntJ = 0; aIntJ < (int)((NSMutableArray*)appDelegate.arrUserDefineSeq[aIntC-1]).count; aIntJ++)
        {
            NSMutableDictionary *currDict = (appDelegate.arrUserDefineSeq)[aIntC - 1][aIntJ];
            if ((appDelegate.arrUserDefineSeq)[aIntC - 1][aIntJ][@"filesID"]!= nil) {
                NSString *aStrInsert = [NSString stringWithFormat:@"insert into CollectionUserDefineFiles ('collectionID', 'filesID', 'order_file', 'isHidden', 'isUserDefine', 'fileTitle', 'isFavourite') values (\'%d\', \'%@\', \'%d\', '0', \'%@\', \'%@\', \'%d\')", aIntC, currDict[@"filesID"] ,aIntJ + 1,currDict[@"isUserDefine"], currDict[@"fileTitle"], [currDict[@"isFavourite"] intValue]];
                
                [[Database sharedDatabase] Insert:aStrInsert];
            }
        }
        
        
        
        BOOL isValidKey = NO;
        for (NSDictionary *dic in (appDelegate.arrUserDefineSeq)[aIntC-1]) {
            if (dic[@"isExtra"]==nil) {
                isValidKey = YES;
                break;
            }
        }
        
        if (isValidKey) {
            //===================
            // Version 3.0 Changes
            //===================
            // Presentation Sequence Name changes
            NSMutableDictionary *aMutDictSeq = mutDicSequence[[NSString stringWithFormat:@"%d",aIntC]];
            NSString *aStrName = aMutDictSeq[SEQUENCE_NAME];
            int seqIndex = [self getIndexForPresentationName: aStrName];
            
            // Set right index...
            aMutDictSeq[SEQUENCE_INDEX] = [NSString stringWithFormat:@"%d", seqIndex];
            [mutDicSequence setValue:aMutDictSeq forKey:[NSString stringWithFormat:@"%d", aIntC]];
            
            NSString *aStrInsertfileID = [NSString stringWithFormat:@"insert into CollectionUserDefine ('collectionID','sequenceName','sequenceIndex' ) values (\'%d\', \"%@\", \'%d\')", aIntC, aStrName, seqIndex];
            [[Database sharedDatabase] Insert:aStrInsertfileID]; //inserted collection ID for user define sequence
        }
    }
    
    NSString *aStrQuery = @"select DISTINCT filesID from CollectionUserDefineFiles";
    NSMutableArray *aMutArrCollection = [[Database sharedDatabase] getAllDataForQuery:aStrQuery];
    
    for (int i=0; i< aMutArrCollection.count; i++)
    {
        NSString *aStrQuery = [NSString stringWithFormat:@"select isHidden from CollectionFilesMaster where filesID = \'%@'",aMutArrCollection[i][@"filesID"]];
        NSMutableArray *aArrCollection = [[Database sharedDatabase] getAllDataForQuery:aStrQuery];
        NSString *hide = @"0";
        if (aArrCollection.count > 0)
        {
            hide = aArrCollection[0][@"isHidden"];
            if ([hide isEqualToString:@"(null)"] || hide == nil)
            {
                hide = @"0";
            }
        }
        NSString *aStrUpdateQuery = [NSString stringWithFormat:@"update CollectionUserDefineFiles SET isHidden = '%@' where filesID = '%@'", hide, aMutArrCollection[i][@"filesID"]];
        [[Database sharedDatabase]Update:aStrUpdateQuery];
        
    }
}

- (void)deletePlaylistData {
    NSString *aStrDeleteTb = [NSString stringWithFormat:@"DROP TABLE IF EXISTS CollectionUserDefineFiles"];
    [[Database sharedDatabase] Delete:aStrDeleteTb];
    aStrDeleteTb = [NSString stringWithFormat:@"DROP TABLE IF EXISTS CollectionUserDefine"];
    [[Database sharedDatabase] Delete:aStrDeleteTb];
}

//===================
// Version 3.0 Changes
//===================
// Presentation Sequence Name changes
// Note: When any presentation is copied from Animation area or Userdefine sequences new presentation is generated in sequence like (1), (2) and (3)...
// If any presenattion is deleted it's position will not change but for new presentation least available number will be used.
// For Example: There are three presenattions: Example Presentation, first copy will be Example Presentation (1) and so on. If Example Presentation (1) will be deleted next Example Presentation (2) wil not become (1) but new copied presentation will be Example Presentation (1).
- (int)getIndexForPresentationName:(NSString *)strPresentationName {
    
    NSString *aStrSeq = [NSString stringWithFormat:@"select collectionID,sequenceName,sequenceIndex from CollectionUserDefine where sequenceName = \"%@\" Order By sequenceIndex Asc", strPresentationName];
    NSMutableArray *aArrPresentations = [[Database sharedDatabase] getAllDataForQuery:aStrSeq];
    int intPresentationIndex = 0;
    
    for (int index= 0; index< aArrPresentations.count; index++) {
        
        int seqIndex = [aArrPresentations[index][@"sequenceIndex"] intValue];
        if (intPresentationIndex == seqIndex) {
            intPresentationIndex++;
        } else {
            break;
        }
    }
    
    return intPresentationIndex;
}

#pragma mark - Dealloc Method
//- (void)dealloc {
//
//    mutArrFilesData = nil;
//    mutDicSequence = nil;
//    mutDicDisplayMvGroupName = nil;
//    mutArrAnimationData = nil;
//    appDelegate.arrPresentation = nil;
//    appDelegate.arrPresentation;
//    self.tblPartAnimationArea;
//    self.tblPartUserSequences;
//}

#pragma mark - Set Current Dictionary
- (void)setCurrentDictWithColor:(int)aIdColor tempValue:(NSString *)aStrVal {
    
    if (mutDictCurrentAnimatedObj) {
        [mutDictCurrentAnimatedObj removeAllObjects];
        mutDictCurrentAnimatedObj = nil;
        mutDictCurrentAnimatedObj = [[NSMutableDictionary alloc] init];
        [self setObjectInAnimatedObj:@(intScrollIndex) forkey:@"collectionID"];
        if (aIdColor == 0 || aIdColor == 1) {
            [self setObjectInAnimatedObj:[UIColor clearColor] forkey:@"color"];
        }
        NSMutableDictionary *aMutDictTemp = [self getDictionaryForCurrentSelectedView];
        if (aMutDictTemp) {

            [self setObjectInAnimatedObj:aMutDictTemp[@"fileName"] forkey:@"fileName"];
            [self setObjectInAnimatedObj:aMutDictTemp[@"filePath"] forkey:@"filePath"];
            [self setObjectInAnimatedObj:aMutDictTemp[@"fileTitle"] forkey:@"fileTitle"];
            [self setObjectInAnimatedObj:aMutDictTemp[@"isDownloaded"] forkey:@"isDownloaded"];
            [self setObjectInAnimatedObj:aMutDictTemp[@"isPlayed"] forkey:@"isPlayed"];
            [self setObjectInAnimatedObj:aStrVal forkey:@"isTemp"];
            [self setObjectInAnimatedObj:aMutDictTemp[@"thumbnailPath"] forkey:@"thumbnailPath"];
            [self setObjectInAnimatedObj:aMutDictTemp[@"filesID"] forkey:@"filesID"];
            [self setObjectInAnimatedObj:aMutDictTemp[@"fileShortDispName"] forkey:@"fileShortDispName"];
            [self setObjectInAnimatedObj:aMutDictTemp[@"IsHidden"] forkey:@"IsHidden"];
            [self setObjectInAnimatedObj:aMutDictTemp[@"isUserDefine"] forkey:@"isUserDefine"];
        } else {

            [self setObjectInAnimatedObj:@"" forkey:@"fileName"];
            [self setObjectInAnimatedObj:@"" forkey:@"filePath"];
            [self setObjectInAnimatedObj:@"" forkey:@"fileTitle"];
            [self setObjectInAnimatedObj:[NSNumber numberWithBool:FALSE] forkey:@"isDownloaded"];
            [self setObjectInAnimatedObj:[NSNumber numberWithBool:FALSE] forkey:@"isPlayed"];
            [self setObjectInAnimatedObj:aStrVal forkey:@"isTemp"];
            [self setObjectInAnimatedObj:@"" forkey:@"thumbnailPath"];
            [self setObjectInAnimatedObj:@"" forkey:@"filesID"];
            [self setObjectInAnimatedObj:@"0" forkey:@"IsHidden"];
        }
    }
}


-(void)setObjectInAnimatedObj:(NSObject*)myObj forkey:(NSString*)myKey
{
    if(myObj == nil || [myKey isEqualToString:@""])
    {
        return;
    }
    
    mutDictCurrentAnimatedObj[myKey] = myObj;
}


- (NSMutableDictionary *)getDictionaryForCurrentSelectedView {
    NSMutableDictionary *aMutDictTemp = nil;
    int count_arr= (int)((NSMutableArray*)(appDelegate.arrScrollViewCount)[intScrollIndex - 1]).count;
    if (count_arr > 0 && count_arr > intSelectedPanl)
    {
        aMutDictTemp = (appDelegate.arrScrollViewCount)[intScrollIndex-1][intSelectedPanl];
    }
    return aMutDictTemp;
}

#pragma mark - Animation View Delegate Methods
- (void)playVideo:(id)sender {
    UIButton *aBtnPlay = (UIButton *)sender;
//    if (isLocked != FALSE)
//    {
        UIView *aViewTemp = aBtnPlay.superview;

        UIView *parent = aBtnPlay.superview;
        while (parent && ![parent isKindOfClass:[UITableView class]]) {
            parent = parent.superview;
        }

        AnimationView *animation = [[AnimationView alloc]init];
        animation.view.tag = aViewTemp.tag;

        // Check button is in which Tableview...
        if (parent.tag == 101) {
        
            // Button inside Upper TableView...
            int currObj = animation.view.tag % 1000;
            int currRow = (int)(animation.view.tag / 1000);
            NSArray *tempArr = appDelegate.arrScrollViewCount[currRow];
            
            // This is done for client comment "Presentation Info should not close on click of Animation button" going from FullScreenMode to SelectionPanel...
            if (appDelegate.isPresentationInfoOpen) {
                appDelegate.isReOpenPresentationInfo = YES;
            }
            
            // Change Presentation Info(If it is opened)...
            if (appDelegate.isPresentationInfoOpen && intSelectedPresentation != currRow) {
                [self changePresentationInfo:currRow];
            }
            
            NSString *aStrUserDefine = @"";
            if (appDelegate.selectedObj != nil) {
                aStrUserDefine = [(appDelegate.arrScrollViewCount)[0] valueForKey:@"isUserDefine"];
            } else {
                aStrUserDefine = [(appDelegate.arrScrollViewCount)[currRow][0] valueForKey:@"isUserDefine"];
            }
        
            if(currObj == tempArr.count-1 && appDelegate.intSelectedTab == 1 && [aStrUserDefine isEqualToString:@"1"])
            {
                if (currRow == appDelegate.arrScrollViewCount.count - 1) {
                    isCreateNewImageAlbum = YES;
                }
                else
                {
                    isCreateNewImageAlbum = NO;
                    strCollectionId = [NSString stringWithFormat:@"%@",(appDelegate.arrScrollViewCount)[currRow][0][@"collectionID"]];
                }
                
                intCurrentRowIndex = currRow;
                [self btnImportImageClicked:sender];
                return;
            }
//            else
//            {
//                if(appDelegate.intSelectedTab == 1 && [aStrUserDefine isEqualToString:@"1"])
//                {
//                    NSMutableArray *tempArr = appDelegate.arrScrollViewCount[currRow];
//                    if(tempArr.count > 0)
//                    {
//                        [tempArr removeLastObject];
//                        [appDelegate.arrScrollViewCount replaceObjectAtIndex:currRow withObject:tempArr];
//                    }
//                }
//            }

        }

        [self tapVideoForPlay:animation :parent.tag];
        appDelegate.intlastPlayVideoState = (int)aBtnPlay.superview.superview.superview.superview.tag;

//    }else{
//        UIButton *btnLock = (UIButton *)[self.view viewWithTag:121];
//        [self lockAnimation:btnLock];
        
        
        
//    }
}

#pragma mark - Clear All Color
- (void)clearAllColor {
    if (appDelegate.selectedObj != nil) {
        if (appDelegate.arrScrollViewCount.count > 0) {
            NSString * isPlayed = (appDelegate.arrScrollViewCount)[0][@"isPlayed"];
            (appDelegate.arrScrollViewCount)[0][@"color"] = isPlayed.integerValue == 1 ? [UIColor clearColor] : redColor;
        }
    } else {
        for (int aIntCntI = 0; aIntCntI <  (appDelegate.arrScrollViewCount).count; aIntCntI++) {
            for (int aIntCntJ = 0; aIntCntJ < ((NSMutableArray*)(appDelegate.arrScrollViewCount)[aIntCntI]).count; aIntCntJ++) {
                NSString * isPlayed = (appDelegate.arrScrollViewCount)[aIntCntI][aIntCntJ][@"isPlayed"];
                
                (appDelegate.arrScrollViewCount)[aIntCntI][aIntCntJ][@"color"] = isPlayed.integerValue== 1 ? [UIColor clearColor] : redColor;
            }
        }
    }
}
#pragma mark - UIGesture Recognizer Delegate
- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldRecognizeSimultaneouslyWithGestureRecognizer:(UIGestureRecognizer *)otherGestureRecognizer {
    return YES;
}

#pragma mark - Unused Function
- (void)tapVideoFromPlaylist : (id)sender
{
    intSelectedPanl = [sender view].tag % 2000;
    intScrollIndex = (int)([sender view].tag / 2000)+1;
    [self savingImagesIntoCollectionFilesMaster];
    if(isLocked) {
        if (appDelegate.boolSelectedPlaylist == YES) {
            (appDelegate.arrScrollViewCount)[appDelegate.intSelectedScrNo - 1][appDelegate.intSelectedVideo][@"color"] = [UIColor clearColor];
        }
        appDelegate.mutArrPlayVideo = (appDelegate.arrScrollViewCount)[intScrollIndex-1];
        appDelegate.intSelectedVideo = intSelectedPanl;
        UIView *view = (self.view).superview;
        NSArray *temp = view.subviews;
        for (int i = 0; i < temp.count; i++)
        {
            UIView *vi = temp[i];
            if (vi.tag == 100)
            {
                UIButton *btn_temp = (UIButton *)[vi viewWithTag:20];
                btn_temp.selected = FALSE;
            }
        }
        (appDelegate.arrScrollViewCount)[intScrollIndex - 1][intSelectedPanl][@"color"] = animationBGColor;  // Orange
        appDelegate.intSelectedScrNo = intScrollIndex;
        appDelegate.boolSelectedPlaylist = YES;
        [videoPlayDelegate PlayVideoSelected:sender];
        [self.view removeFromSuperview];
    }
}

-(BOOL)checkFileIdFromUD:(NSString*)aFileId{
    if (isFilePlayed) {
        return YES;
    }
    NSString *aStrSql = [NSString stringWithFormat:@"select fileID from LocalFileMaster where isPlayed= 0 and fileID= \'%@\'",aFileId];
    return [[Database shareDatabase] CheckForRecord:aStrSql];
}

#pragma mark --------------------------------
#pragma mark Version 2.4 Changes

#pragma mark --------------------------------
#pragma mark Import Images from iClod and Gallery

//NSString *statusStrings[] = {@"PHAuthorizationStatusNotDetermined", @"PHAuthorizationStatusRestricted", @"PHAuthorizationStatusDenied", @"PHAuthorizationStatusAuthorized", @"PHAuthorizationStatusLimited"};
PHAuthorizationStatus betterStatus = PHAuthorizationStatusNotDetermined;

- (IBAction)btnImportImageClicked:(UIButton *)sender {
    
    //New code
    CGRect frame = [sender convertRect:sender.frame toView:self.view];
    
    UIAlertController *actionSheet = [UIAlertController alertControllerWithTitle:@"Import Image" message:@"" preferredStyle:UIAlertControllerStyleActionSheet];
    
    [actionSheet addAction:[UIAlertAction actionWithTitle:@"Cancel" style:UIAlertActionStyleCancel handler:^(UIAlertAction *action) {
        
        // Cancel button tappped.
        [self dismissViewControllerAnimated:YES completion:^{
        }];
    }]];
    
    [actionSheet addAction:[UIAlertAction actionWithTitle:@"From Gallery" style:UIAlertActionStyleDefault handler:^(UIAlertAction *action) {
        
        // OK button tapped.
        [self dismissViewControllerAnimated:YES completion:nil];
        
        // New, non-deprecated way to check status
        if (@available(iOS 14, *)) {
            // We call this because the requestAuthorization call below returns a misleading PHAuthorizationStatusAuthorized when actual status should
            //  be PHAuthorizationStatusLimited. Not sure why.
            [PHPhotoLibrary requestAuthorizationForAccessLevel:PHAccessLevelReadWrite handler:^(PHAuthorizationStatus newStatus) {
                //                NSLog(@"newStatus: %@", statusStrings[newStatus]);
                betterStatus = newStatus;
            }];
        } else {
            betterStatus = PHAuthorizationStatusAuthorized; // Access to limited number of photos on device only available on iOS 14+
        }
        
        [PHPhotoLibrary requestAuthorization:^(PHAuthorizationStatus status) {
            switch (status) {
                case PHAuthorizationStatusAuthorized:
                {
                    dispatch_async(dispatch_get_main_queue(), ^{
                        // The confusing select-more-photos-only-on-the-first-invocation behavior started with
                        //  iOS 14, making it hard to add more photos without closing and relaunching BiteFX every
                        //  time the end user adds a photo. This code mitigates that behavior to allow user to expand
                        //  photo selection every time they add a photo to BiteFX. SCM 2023-11-08
                        [self openImagePicker:frame];
                        if (@available(iOS 14, *)) {
                            static bool firstTime = true;
                            if (betterStatus == PHAuthorizationStatusLimited) { // If user agreed to let all photos available, we don't need to pop up the additional menu here.
                                if (firstTime) {
                                    NSLog(@"PHAuth: firstTime set to false");
                                    firstTime = false;
                                } else {
                                    NSLog(@"PHAuth: subsequent iteration");
                                    UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"BiteFX Would Like to Access Your Photos" message:@"Get photo library image" preferredStyle:UIAlertControllerStyleAlert];
                                    
                                    UIAlertAction *selectMorePhotosAction = [UIAlertAction actionWithTitle:@"Select More Photos..." style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                                        // Handle "Select More Photos..." action
                                        NSLog(@"Select More Photos Action");
                                        PHPhotoLibrary *library = [PHPhotoLibrary sharedPhotoLibrary];
                                        [library presentLimitedLibraryPickerFromViewController:self];
                                    }];
                                    
                                    UIAlertAction *keepCurrentSelectionAction = [UIAlertAction actionWithTitle:@"Keep Current Selection" style:UIAlertActionStyleDefault handler:nil];
                                    
                                    [alert addAction:selectMorePhotosAction];
                                    [alert addAction:keepCurrentSelectionAction];
                                    
                                    NSMutableAttributedString *titleString = [[NSMutableAttributedString alloc] initWithString:@"BiteFX Would Like to Access Your Photos"];
                                    [titleString addAttribute:NSFontAttributeName value:[UIFont boldSystemFontOfSize:17.0] range:NSMakeRange(0, titleString.length)];
                                    [alert setValue:titleString forKey:@"attributedTitle"];
                                    
                                    NSMutableAttributedString *subtitleString = [[NSMutableAttributedString alloc] initWithString:@"Get photo library image"];
                                    [subtitleString addAttribute:NSFontAttributeName value:[UIFont systemFontOfSize:13.0] range:NSMakeRange(0, subtitleString.length)];
                                    [alert setValue:subtitleString forKey:@"attributedMessage"];
                                    
                                    [self.presentingViewController presentViewController:alert animated:YES completion:nil];
                                }
                            }
                        }
                    });
                }
                    break;
                default:
                {
                    NSLog(@"Not permitted");
                    break;
                }
            }
        }];
    }]];
    
    [actionSheet addAction:[UIAlertAction actionWithTitle:@"From iCloud" style:UIAlertActionStyleDefault handler:^(UIAlertAction *action) {
        
        // OK button tapped.
        [self dismissViewControllerAnimated:YES completion:nil];
        [self getPhotosFromiCloud];
        
    }]];
    
    actionSheet.popoverPresentationController.sourceView = self.view;
    actionSheet.popoverPresentationController.sourceRect = frame;
    
    // Present action sheet.
    [self.view.window.rootViewController presentViewController:actionSheet animated:YES completion:nil];
    
}

/*
 #pragma mark --------------------------------
 #pragma mark UIActionSheet Delegate Methods
 
 - (void)actionSheet:(UIActionSheet *)actionSheet clickedButtonAtIndex:(NSInteger)buttonIndex
 {
 if (buttonIndex == [actionSheet cancelButtonIndex]) {
 
 // User pressed cancel -- abort...
 return;
 }
 
 //    if (buttonIndex == 0) {
 //        [self openImagePicker:btnFrame];
 //    }
 //    else if (buttonIndex == 1) {
 //        [self getPhotosFromiCloud];
 //    }
 
 }
 */

- (void)openImagePicker:(CGRect)aBtnFrame {
    
    // Open custom imagePicker...
    QBImagePickerController *aImgPickerController = [QBImagePickerController new];
    aImgPickerController.delegate = self;
    aImgPickerController.mediaType = QBImagePickerMediaTypeImage;
    aImgPickerController.allowsMultipleSelection = YES;
    aImgPickerController.showsNumberOfSelectedAssets = YES;
    aImgPickerController.minimumNumberOfSelection = 1;
    aImgPickerController.maximumNumberOfSelection = 10;
    aImgPickerController.numberOfColumnsInPortrait = 4;
    aImgPickerController.numberOfColumnsInLandscape = 4;
    
    // Specify the albums...
    aImgPickerController.assetCollectionSubtypes = @[
        @(PHAssetCollectionSubtypeSmartAlbumUserLibrary), // Camera Roll
        //                                                      @(PHAssetCollectionSubtypeAlbumMyPhotoStream), // My Photo Stream
        @(PHAssetCollectionSubtypeSmartAlbumScreenshots), // Screen Shots
        //                                                      @(PHAssetCollectionSubtypeSmartAlbumPanoramas), // Panoramas
        //                                                      @(PHAssetCollectionSubtypeSmartAlbumVideos), // Videos
        //                                                      @(PHAssetCollectionSubtypeSmartAlbumBursts) // Bursts
    ];
    aImgPickerController.modalPresentationStyle = UIModalPresentationPopover;
    //    aImgPickerController.view.frame = CGRectMake(0, 0, 370, 650);
    
    UIPopoverPresentationController *popoverController = aImgPickerController.popoverPresentationController;
    popoverController.permittedArrowDirections = UIPopoverArrowDirectionAny;
    popoverController.sourceView = self.view;
    popoverController.sourceRect = aBtnFrame;
    
    [self showViewController:aImgPickerController sender:nil];
}

- (void)getPhotosFromiCloud {
    
    NSArray *arrDocumentTypes = @[(NSString*)kUTTypeJPEG, (NSString *)kUTTypePNG];
    UIDocumentPickerViewController *documentPicker = [[UIDocumentPickerViewController alloc] initWithDocumentTypes:arrDocumentTypes inMode:UIDocumentPickerModeImport];
    
    //===================
    // Version 3.0 Changes
    //===================
    // Allow multiple selection in iCloud photos...
    if (@available(iOS 11.0, *)) {
        documentPicker.allowsMultipleSelection = YES;
    }
    
    //    documentPicker.
    // Call Delegate
    documentPicker.delegate = self;
    
    //Set the Document Open Style and Present the Controller
    documentPicker.modalPresentationStyle = UIModalPresentationFormSheet;
    [self presentViewController:documentPicker animated:YES completion:nil];
    
}

#pragma mark -
#pragma mark - UIDocumentPicker Delegate Methods

//===================
// Version 3.0 Changes
//===================
// Allow multiple selection in iCloud photos...
- (void)documentPicker:(UIDocumentPickerViewController *)controller didPickDocumentsAtURLs:(NSArray<NSURL *> *)urls {
    
    if (controller.documentPickerMode == UIDocumentPickerModeImport) {
        [self getAllImagesFromiCloud:urls];
    }
}

#pragma mark --------------------------------
#pragma mark Get image from iCloud Methods

- (void)getAllImagesFromiCloud:(NSArray<NSURL *> *)arrUrls {
    
    // Version 3.0 Changes
    // Remove old data to solve duplicate images (selected from gallery) issue...
    [mutArrSelectedImages removeAllObjects];
    
    for (NSURL *aUrl in arrUrls) {
        [self getImageFromiCloud:aUrl];
    }
    
    // Check if new Albumn should be created or not and Save data in database...
    [self checkForNewImageAlbumnAndSaveData];
}

- (void)getImageFromiCloud:(NSURL *)aFileUrl {
    
    UIImage *aImgOriginal = [UIImage imageWithContentsOfFile:aFileUrl.path];
    
    // Compress Image data and save in array...
    [self compressImageAndSaveImage:aImgOriginal withFileName:aFileUrl.lastPathComponent];
    
    // Remove iCloud downloaded image from "Temp" directory...
    NSError *error;
    BOOL successDelete = [[NSFileManager defaultManager] removeItemAtPath:aFileUrl.path error:&error];
    if (!successDelete) {
        NSLog(@"Error occurs in deleting iCloud File : %@", error.localizedDescription);
    }
    
}

- (void)compressImageAndSaveImage:(UIImage *)originalImage withFileName:(NSString *)aStrFileName {
    
    // Version 3.0 change - if condition is added to solve crash issue...
    if (originalImage != nil) {
        
        NSData *aDataImgCompressed = [Utility compressImageData:originalImage];
        UIImage *aImgCompressed = [UIImage imageWithData:aDataImgCompressed];
        
        NSDictionary *aDictImgData = @{ IMAGE_NAME : aStrFileName, IMAGE : aImgCompressed};
        [mutArrSelectedImages addObject:aDictImgData];
        
    }
    
}

#pragma mark --------------------------------
#pragma mark QBImagePickerController Delegate Methods

- (void)qb_imagePickerController:(QBImagePickerController *)imagePickerController didFinishPickingAssets:(NSArray *)assets {
    //    for (PHAsset *asset in assets) {
    //        // Do something with the asset
    //    }
    
    [imagePickerController.presentingViewController dismissViewControllerAnimated:YES completion:NULL];
    
    // Version 3.0 changes for Import image issue(iCloud image issue)...
    imgIndex = 0;
    [mutArrSelectedImages removeAllObjects];
    // Get info of selected assets...
    [SVProgressHUD showWithStatus:@"Downloading images"];
    [self getInfoFromAssets:assets];
    
}

- (void)qb_imagePickerControllerDidCancel:(QBImagePickerController *)imagePickerController {
    [imagePickerController.presentingViewController dismissViewControllerAnimated:YES completion:NULL];
}

#pragma mark --------------------------------
#pragma mark Save Imported Images Methods

- (void)getInfoFromAssets:(NSArray *)arrAssets {
    
    if (imgIndex == arrAssets.count) {
        
        [SVProgressHUD dismiss];
        
        // Check if new Albumn should be created or not and save data in database...
        [self checkForNewImageAlbumnAndSaveData];
        
    } else {
        
        // Get image data...
        [self getImageInfoFromAsset:arrAssets[imgIndex] completion:^{
            
            // Call function again...
            // We are fetching images one by one...
            self->imgIndex++;
            [self getInfoFromAssets:arrAssets];
        }];
        
    }
    
}

//===================
// Version 3.0 Changes
//===================
// Completion handler is added in method. This is done because requestImage function is asynchronous and we need image data one by one...
// If user has selected mutiple images which are in iCloud we need to download it one by one...
- (void)getImageInfoFromAsset:(PHAsset *)asset completion:(void (^)(void))completion {
    
    // Get photo info from this asset...
    NSArray *resources = [PHAssetResource assetResourcesForAsset:asset];
    
    if(resources.count == 0)
    {
        return;
    }
    
    NSString *aStrFileName = ((PHAssetResource*)resources[0]).originalFilename;
    NSLog(@"fileName : %@", aStrFileName);
    
    PHImageRequestOptions *aImageRequestOptions = [[PHImageRequestOptions alloc] init];
    aImageRequestOptions.resizeMode = PHImageRequestOptionsResizeModeExact;
    aImageRequestOptions.deliveryMode = PHImageRequestOptionsDeliveryModeHighQualityFormat;
    aImageRequestOptions.synchronous = NO;
    aImageRequestOptions.networkAccessAllowed = YES;
    
    //    aImageRequestOptions.progressHandler = ^(double progress, NSError * _Nullable error, BOOL * _Nonnull stop, NSDictionary * _Nullable info) {
    //        if(progress == 1.0) {
    //            [SVProgressHUD dismiss];
    //        } else {
    //            [SVProgressHUD showProgress:progress status:@"Downloading from iCloud"];
    //        }
    //    };
    
    [[PHImageManager defaultManager] requestImageForAsset:asset targetSize:PHImageManagerMaximumSize contentMode:PHImageContentModeDefault options:aImageRequestOptions resultHandler:^(UIImage *image, NSDictionary *info) {
        
        // Compress Image data and save in array...
        [self compressImageAndSaveImage:image withFileName:aStrFileName];
        // Call completion callback...
        completion();
    }];
}

- (void)checkForNewImageAlbumnAndSaveData {
    
    if (isCreateNewImageAlbum) {
        NSString *aStrImageGroupName = [self getImageAlbumName];
        [self showAlertForEditImageAlbumName:aStrImageGroupName];
        return;
    }
    
    [self saveImportedImageDataInDB];
    
}

NSString *oldStrAlbumName;
- (void)showAlertForEditImageAlbumName:(NSString *)aStrAlbumName {
    // tag : 501
    oldStrAlbumName = aStrAlbumName;
    UIAlertController * alertController = [UIAlertController alertControllerWithTitle: @"Specify Album Name"
                                                                              message: @""
                                                                       preferredStyle:UIAlertControllerStyleAlert];
    [alertController addTextFieldWithConfigurationHandler:^(UITextField *textField) {
        textField.placeholder = @"Enter Album Name";
        textField.text = [self removePrefix:aStrAlbumName];
        textField.keyboardAppearance = UIKeyboardAppearanceAlert;
        textField.returnKeyType = UIReturnKeyDone;
        textField.tag = self->intCurrentRowIndex;
        textField.clearButtonMode = UITextFieldViewModeWhileEditing;
        textField.borderStyle = UITextBorderStyleRoundedRect;
        textField.delegate = (id<UITextFieldDelegate>)self;
    }];
    
    [alertController addAction:[UIAlertAction actionWithTitle:@"Save" style:UIAlertActionStyleDefault handler:^(UIAlertAction *action) {
        NSArray * textfields = alertController.textFields;
        UITextField * nameField = textfields[0];
        if ([self isValidImageAlbumName:nameField.text]) {
            if (self->isEditImageAlbumName) {
                [self updateImageAlbumName: [self transferCollectionNumberToNewString:oldStrAlbumName newStr:nameField.text]];
            }
            else
            {
                [self saveImageAlbumName:nameField.text];
            }
        }
        else
        {
            self->isCreateNewImageAlbum = NO;
            self->isEditImageAlbumName = NO;
        }
        
    }]];
    
    [alertController addAction:[UIAlertAction actionWithTitle:@"Cancel" style:UIAlertActionStyleDefault handler:^(UIAlertAction *action) {
        self->isCreateNewImageAlbum = NO;
        self->isEditImageAlbumName = NO;
        
    }]];
    [self presentViewController:alertController animated:YES completion:nil];
    
}

- (long)getNextImageCollectionNumber {
    // Retrieve the current value of the number
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    return [defaults integerForKey:COLLECTION_NUMBER];
}

- (long)setNextImageCollectionNumber {
    // Retrieve the current value of the number
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    NSInteger currentValue = [defaults integerForKey:COLLECTION_NUMBER];
    [defaults setInteger:currentValue+1 forKey:COLLECTION_NUMBER];
    [defaults synchronize]; // Make sure it writes it out now instead of just caching it for writing later.
    return currentValue;
}

- (NSString *)getImageAlbumName {
    NSString *aStrImageAlbumName = [NSString stringWithFormat:@"Import (%ld)", (long)[self getNextImageCollectionNumber]];
    
    return aStrImageAlbumName;
}

- (void)updateImageAlbumName:(NSString *)aStrAlbumName {
    
    NSString *aStrCollectionId = [NSString stringWithFormat:@"%@",(appDelegate.arrScrollViewCount)[intCurrentRowIndex][0][@"collectionID"]];
    
    NSString *aStrUpdateQuery = [NSString stringWithFormat:@"update CollectionMaster SET collectionDisplayName = \"%@\" where collectionID = '%@'", aStrAlbumName, aStrCollectionId];
    [[Database sharedDatabase]Update:aStrUpdateQuery];
    
    [mutDicDisplayMvGroupName setValue:[NSString stringWithFormat:@"%@", aStrAlbumName] forKey:aStrCollectionId];
    [dictGropLockImages setValue:[NSString stringWithFormat:@"%@", aStrAlbumName] forKey:aStrCollectionId];
    [dictGropImages setValue:[NSString stringWithFormat:@"%@", aStrAlbumName] forKey:aStrCollectionId];
    
    [self.tblPartAnimationArea reloadRowsAtIndexPaths:@[[NSIndexPath indexPathForRow:intCurrentRowIndex inSection:0]] withRowAnimation:UITableViewRowAnimationFade];
    
    isEditImageAlbumName = NO;
}

- (void)saveImageAlbumName:(NSString *)aStrAlbumName {
    long newCollectionNumber = [self setNextImageCollectionNumber]; // gets current Collection Number then it increments the number and saves it to App Defaults
    NSString *aStrAlbumNameWithID = [NSString stringWithFormat:@"#%ld %@", newCollectionNumber, aStrAlbumName];
    NSString *aStrInsertInfo = [NSString stringWithFormat:@"insert into CollectionMaster ('collectionName', 'collectionDisplayName', 'fileType', 'isUserDefine', 'infoFilePath') values (\"%@\", \"%@\", \'%@\', '1', '')",aStrAlbumName, aStrAlbumNameWithID, mediaImage];
    
    [[Database sharedDatabase] Insert:aStrInsertInfo];
    
    if (isCreateNewImageAlbum) {
        
        NSString *aStrSelectQuery = [NSString stringWithFormat:@"SELECT * FROM CollectionMaster WHERE collectionID = (SELECT MAX(collectionID) FROM CollectionMaster)"];
        
        NSMutableArray *aMutArrImageGroups = [[Database sharedDatabase] getAllDataForQuery:aStrSelectQuery];
        
        strCollectionId = aMutArrImageGroups[0][@"collectionID"];
        
        isCreateNewImageAlbum = NO;
    }

    [self saveImportedImageDataInDB];
}

- (void)saveImportedImageDataInDB {
    
    // Save all images of array...
    for (NSDictionary *aDictImgData in mutArrSelectedImages) {
        [self createThumbnailImageAndSaveImageInDB:aDictImgData];
    }
    
    [self getDataForImageSection];
    [self setImageTabSelected];
    
    // This is done regarding client comment the picture panel jumps to the top on click of hide/unhide so we also do same for import image functionality..
    appDelegate.isScrollToSelectedIndex = NO;
    [self setSelectedIndexColor];
    appDelegate.isScrollToSelectedIndex = YES;

}

- (void)createThumbnailImageAndSaveImageInDB:(NSDictionary *)imgData {
    
    NSString *aStrImageName = imgData[IMAGE_NAME];
    UIImage *aCompressedImg = imgData[IMAGE];
    
    // Save original image in "Images" folder...
    NSString *aStrDirPath = [NSString stringWithFormat:@"%@/%@/%@", DOCUMENT_DIRECTORY_PATH, BITEFXV2, IMAGES];
    NSString *aStrImagePath = [aStrDirPath stringByAppendingPathComponent:aStrImageName];
    
    NSData *aData = UIImageJPEGRepresentation(aCompressedImg, 1.0);
    [aData writeToFile:aStrImagePath atomically:YES];
    
    //===================
    // Version 3.0 Changes
    //===================
    // Commented below line to solve no thumbnail created issue...
    //    UIImage *aImgThumbnail = [Utility imageByScalingToSize:CGSizeMake(68.0, 51.0) forImage:aCompressedImg];
    UIImage *aImgThumbnail = [UIImage imageWithData:UIImageJPEGRepresentation(aCompressedImg, 0.5)];
    
    // Save Thumbnail image in "Thumbnails/Images" folder...
    aStrDirPath = [NSString stringWithFormat:@"%@/%@/%@/%@", DOCUMENT_DIRECTORY_PATH, BITEFXV2, THUMBNAILS, IMAGES];
    aStrImagePath = [aStrDirPath stringByAppendingPathComponent:aStrImageName];
    
    aData = UIImageJPEGRepresentation(aImgThumbnail, 1.0);
    [aData writeToFile:aStrImagePath atomically:YES];
    
    // Insert data in LocalFileMaster table...
    NSString *aStrImagePathToSave = [NSString stringWithFormat:@"%@/%@/%@", BITEFXV2, IMAGES, aStrImageName];
    NSString *aStrThumbImagePathToSave = [NSString stringWithFormat:@"%@/%@/%@/%@", BITEFXV2, THUMBNAILS, IMAGES, aStrImageName];
    
    NSString *aStrName = aStrImageName.stringByDeletingPathExtension;
    
    NSString *aStrInsertQuery = [NSString stringWithFormat:@"insert into LocalFileMaster ('fileTitle', 'fileName', 'fileShortDispName', 'isDownloaded', 'isPlayed', 'filePath', 'infoFilePath','thumbnailPath','fileType') values (\'%@\', \'%@\', \'%@\', '1', '1', \'%@\', \'%@\', \'%@\', \'%@\')",aStrImageName,aStrName,aStrName,aStrImagePathToSave, @" ", aStrThumbImagePathToSave, mediaImage];
    
    [[Database sharedDatabase] Insert:aStrInsertQuery];
    
    // Get last inserted collectionID...
    int aIntCollectionId = strCollectionId.intValue;
    
    NSString *aStrSelectQuery = @"";
    
    // Get last inserted fileID...
    aStrSelectQuery = [NSString stringWithFormat:@"SELECT * FROM LocalFileMaster WHERE fileID = (SELECT MAX(fileID) FROM LocalFileMaster)"];
    NSMutableArray *aMutArrFilesData = [[Database sharedDatabase] getAllDataForQuery:aStrSelectQuery];
    
    int aIntFileId = [aMutArrFilesData[0][@"fileID"] intValue];
    
    // Insert data in CollectionFilesMaster...
    NSString *aStrInsert = [NSString stringWithFormat:@"insert into CollectionFilesMaster ('collectionID', 'filesID', 'order_file') values (\'%d\', \'%d\', '0')",aIntCollectionId, aIntFileId];
    
    [[Database sharedDatabase] Insert:aStrInsert];
    
}

-(void)showAlertMessageWithOK:(NSString *)strAlertTitle alertMessage:(NSString *)strAlertMessage
{
    [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:strAlertMessage cancelButtonTitle:@"OK" destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
    
}

- (BOOL)isValidImageAlbumName:(NSString *)aStrAlbumName {
    
    NSString *aStrText = [aStrAlbumName stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
    if (aStrText.length == 0) {
        
        [self showAlertMessageWithOK:APP_NAME alertMessage:ENTER_IMAGE_ALBUM_NAME];
        return NO;
    }
    
    // New validation Image album name can not contain " (double quotation) in name...
    if ([aStrText containsString:@"\""]) {
        [self showAlertMessageWithOK:APP_NAME alertMessage:INVALID_IMAGE_ALBUM_NAME];
        return NO;
    }
    
    // An image album may not contain PICTURES_HIDDEN string (i.e., "Pictures hidden")
    if ([aStrText containsString:PICTURES_HIDDEN]) {
        [self showAlertMessageWithOK:APP_NAME alertMessage:INVALID_IMAGE_ALBUM_PICTURES_HIDDEN];
        return NO;
    }
    
    return YES;
}

//===================
// Version 3.1 Changes
//===================
// Feature MVI file changes.

- (void)showFeatureSets {
    
    self.featureSetVC = [[FeatureSetVC alloc] initWithNibName:@"FeatureSetVC" bundle:nil];
    [self.featureSetVC.view setTranslatesAutoresizingMaskIntoConstraints:YES];
    self.featureSetVC.view.frame = CGRectMake(0, HeightSegment, 929, 661);
    
    [self.view addSubview:self.featureSetVC.view];
    [self addChildViewController:self.featureSetVC];
    [self.featureSetVC didMoveToParentViewController:self];
    
    //    [self removeFeatureSetDropDownView];
    
    __block typeof(self) bself = self; // To avoid retain cycles.
    self.featureSetVC.btnFeatureSetClickBlock = ^(int featureID) {
        
        // Remove FeatureSets view...
        [bself removeFeatureSets];
        
        /// Note: Refresh Presentations data based on selected FeatureSet...
        [bself resetPresentationsForSelectedFeatureSet:featureID fromMainView:YES];
        
    };
    
}

- (void)removeFeatureSets {
    [self.featureSetVC.view removeFromSuperview];
    [self.featureSetVC willMoveToParentViewController:nil];
    [self.featureSetVC removeFromParentViewController];
}

- (void)checkAndShowFeatureSetMainOrDetailView {
    
    /// Note: These changes are only for logged in user...
    if (userSubStat == Subscribed || userSubStat == InAppSubscribed) {
        /// Get FeatureID...
        NSInteger aIntFeatureId = [[NSUserDefaults standardUserDefaults] integerForKey:SELECTED_FEATURE_SET_ID];
        
        if (aIntFeatureId <= 0) {
            /// Note: User has not selected any previous FeatureSet or close the detail screen dropdown...
            /// Show Main FeatureSets View...
            [self showFeatureSets];
        } else {
            /// Show Detail screen dropdown...
            [self showFeatureSetDropDownView];
        }
    }
}

- (void)showFeatureSetDropDownView {
    
    // First remove view if already added...
    [self removeFeatureSetDropDownView];
    
    // Show FeatureSets DropDownView below Segment Control...
    self.viewFeatureSetDropDown = [[FeatureSetDropDownView alloc] initWithFrame:CGRectMake(669, HeightSegment, 260, 190)];
    //    self.viewFeatureSetDropDown = [[FeatureSetDropDownView alloc] initWithFrame:CGRectMake(0, HeightSegment, 929, 661)]; // Full View...
    
    __block typeof(self) bself = self; // To avoid retain cycles.
    self.viewFeatureSetDropDown.btnFeatureSetClickBlock = ^(int featureID) {
        
        /// Note: Refresh Presentations data based on selected FeatureSet...
        dispatch_async(dispatch_get_main_queue(), ^{
            [bself resetPresentationsForSelectedFeatureSet:featureID fromMainView:NO];
        });
    };
    
    self.viewFeatureSetDropDown.btnCloseClickBlock = ^{
        dispatch_async(dispatch_get_main_queue(), ^{
            // Remove DropDown View...
            [bself removeFeatureSetDropDownView];
            
            appDelegate.intSelectedFeatureId = 0;
            
            // Reset FeatureID...
            [[NSUserDefaults standardUserDefaults] setInteger:0 forKey:SELECTED_FEATURE_SET_ID];
            [[NSUserDefaults standardUserDefaults] synchronize];
            
            /// Show Main FeatureSets View...
            [bself showFeatureSets];
        });
    };
    
    [self.view addSubview:self.viewFeatureSetDropDown];
}

- (void)resetPresentationsForSelectedFeatureSet:(int)featureID fromMainView:(BOOL)isFromMainView {
    
    appDelegate.intSelectedFeatureId = featureID;
    
    // Save FeatureID...
    [[NSUserDefaults standardUserDefaults] setInteger:featureID forKey:SELECTED_FEATURE_SET_ID];
    
    if (isFromMainView) {
        /// Note: For Detail screen DropDownView is already added. So add view if it is from main screen...
        [self showFeatureSetDropDownView];
    }
    
    /// Note: Remove presentation info view...
    /// Close presentation info as Presentation data is changed so to avoid data inconsistency...
    if (appDelegate.isPresentationInfoOpen) {
        [self btnCloseInfoClick:nil];
    }
    [self getDataForPresentationSection:NO];
    [self MySegmentControlAction:btnPresentation];
    
//    [self setPresentationTabSelected];
//    [self setSelectedIndexColor];
}

- (void)removeFeatureSetDropDownView {
    
    /// Note: If FeatureSet main view is added remove it...
    if (self.featureSetVC.view) {
        // Remove FeatureSets view...
        [self removeFeatureSets];
    }
    
    if (self.viewFeatureSetDropDown) {
        [self.viewFeatureSetDropDown removeFromSuperview];
    }
}

- (void)getUserSubscriptionStatus {
    
    switch ([[NSUserDefaults standardUserDefaults] integerForKey:@"UserStatusSubscription"]) {
        case 1:
            userSubStat = InAppSubscribed;
            break;
        case 2:
            userSubStat = Subscribed;
            break;
        default:
            userSubStat = NotSubscribed;
            break;
    }
    
}

#pragma mark - UITouch Methods
- (void)touchesBegan:(NSSet *)touches withEvent:(UIEvent *)event {
    
    //    UITouch *touch  = [[event allTouches] anyObject];
    //    if (touch.view != self.viewFeatureSetDropDown) {
    //        // Remove FeatureSetsDropDownView for outside touch...
    //        [self removeFeatureSetDropDownView];
    //    }
}

- (void)touchesEnded:(NSSet *)touches withEvent:(UIEvent *)event {
    
}

#pragma mark --------------------------------
#pragma mark Notification Handling Methods

- (void)refreshData {
    
    //===================
    // Version 3.1 Changes
    //===================
    // Feature MVI file changes.
    [self getUserSubscriptionStatus];
    
    /// Note: Remove presentation info view...
    /// Close presentation info as Presentation data is changed so to avoid data inconsistency...
    
    if (appDelegate.isPresentationInfoOpen) {
        [self btnCloseInfoClick:nil];
    }

    // Get all new data...
    [self refreshDataAfterSearch];
}
- (void)handleSearchArraysDidUpdate:(NSNotification *)notification {
    self.hasSearchResults = [notification.userInfo[@"hasResults"] boolValue];
    if (self.hasSearchResults) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [self refreshDataAfterSearch];
        });
    }
}

- (void)handleSearchSelection:(NSNotification *)notification {
    appDelegate.selectedObj = [NSString stringWithFormat:@"%@",notification.userInfo[@"selectedObject"]];
    NSLog(@"self.selectedObj: %@",appDelegate.selectedObj);
    if ([appDelegate.selectedObj isEqualToString:@""]) {
        appDelegate.selectedObj = nil;
    }
    
    [self setMovieSelectedWithDispName:appDelegate.selectedObj];
    [self setImageSelectedWithDispName:appDelegate.selectedObj];
    [self setPresenationSelectedWithDispName:appDelegate.selectedObj];
}

- (void)setMovieSelectedWithDispName:(NSString *)dispName {
    NSDictionary *matchedMovie = [self findMovieObjectWithDispName:dispName inArray:appDelegate.arrMovieCopy];
    if (matchedMovie) {
        appDelegate.arrSelectedMovie = [NSMutableArray arrayWithObject:matchedMovie];
        NSLog(@"Set arrSelectedMovie to matchedObject. Count: %lu", (unsigned long)appDelegate.arrSelectedMovie.count);
        if (appDelegate.arrSelectedMovie.count > 0 && [btnMovie isSelected]) {
            [self setMovieTabSelected];
        }
    } else {
        [appDelegate.arrSelectedMovie removeAllObjects];
        if (appDelegate.intSelectedTab == 0) {
            [self setMovieTabSelected];
        }
    }
}

- (void)setImageSelectedWithDispName:(NSString *)dispName {
    NSDictionary *matchedImage = [self findMovieObjectWithDispName:dispName inArray:appDelegate.arrImageCopy];
    if (matchedImage) {
        appDelegate.arrSelectedImage = [NSMutableArray arrayWithObject:matchedImage];
        NSLog(@"Set arrSelectedImage to matchedObject. Count: %lu", (unsigned long)appDelegate.arrSelectedImage.count);
        if (appDelegate.arrSelectedImage.count > 0 && [btnPicture isSelected]) {
            [self setImageTabSelected];
        }
    } else {
        [appDelegate.arrSelectedImage removeAllObjects];
        if (appDelegate.intSelectedTab == 1) {
            [self setImageTabSelected];
        }
    }
}

- (void)setPresenationSelectedWithDispName:(NSString *)dispName {
    NSDictionary *matchedImage = [self findMovieObjectWithDispName:dispName inArray:appDelegate.arrPresentationCopy];
    if (matchedImage) {
        
        appDelegate.arrSelectedPresenation = [NSMutableArray arrayWithObject:matchedImage];
        
        NSLog(@"Set arrSelectedPresenation to matchedObject. Count: %lu", (unsigned long)appDelegate.arrSelectedPresenation.count);
        
            for (int aIntSeqDetail = 0; aIntSeqDetail < appDelegate.arrPresentationFavourite.count; aIntSeqDetail++)
            {
                NSString* str = [NSString stringWithFormat:@"%@",appDelegate.arrPresentationFavourite[aIntSeqDetail][@"collectionDisplayName"]];
                [mutDicDisplayMvGroupName setValue:str forKey:[NSString stringWithFormat:@"%@",appDelegate.arrPresentationFavourite[aIntSeqDetail][@"collectionID"]]];
                
                NSString* isFavourite = [NSString stringWithFormat:@"%@",appDelegate.arrPresentationFavourite[aIntSeqDetail][@"isFavourite"]];
                [mutDicDisplayMvGroupFav setValue:isFavourite forKey:[NSString stringWithFormat:@"%@",appDelegate.arrPresentationFavourite[aIntSeqDetail][@"collectionID"]]];
            }
            
            dictGropPresentation = [mutDicDisplayMvGroupFav mutableCopy];
            mutArrDisplayMvGroupFavourite = (NSMutableArray*)[mutDicDisplayMvGroupFav allKeysForObject:@"1"];
        
        if (appDelegate.arrSelectedPresenation.count > 0 && [btnPresentation isSelected]) {
            [self setPresentationTabSelected];
        }
    } else {
        [appDelegate.arrSelectedPresenation removeAllObjects];
        if (appDelegate.intSelectedTab == 2) {
            [self setPresentationTabSelected];
        }
    }
}

-(void)clearPreviousSelectedArray {
    [appDelegate.arrSearchMovie removeAllObjects];
    [appDelegate.arrSearchImage removeAllObjects];
    [appDelegate.arrSearchPresentation removeAllObjects];
}

- (NSDictionary *)findMovieObjectWithDispName:(NSString *)dispName inArray:(NSArray *)arrSearch {
    for (NSArray *collection in arrSearch) {
        for (NSDictionary *item in collection) {
            NSString *shortName = item[@"fileShortDispName"];
            NSString *cleanShortName = [shortName stringByReplacingOccurrencesOfString:@"\r" withString:@" "];
            if ([cleanShortName isKindOfClass:[NSString class]] && [cleanShortName isEqualToString:dispName]) {
                return item;
            }
        }
    }
    return nil;
}

- (void)handleSearchBarFavUnFavTapped:(NSNotification *)notification {
    
    NSDictionary *userInfo = notification.userInfo;
    appDelegate.isFavourite = [userInfo[@"isFavourite"] boolValue];
    
    if (appDelegate.intSelectedTab == 0) {
        appDelegate.arrScrollViewCount = appDelegate.isFavourite ? appDelegate.arrMovieFavourite : appDelegate.arrMovieCopy;
        dictGropMovie = mutDicDisplayMvGroupName;
    } else if (appDelegate.intSelectedTab == 1) {
        appDelegate.arrScrollViewCount = appDelegate.isFavourite ? appDelegate.arrImageFavourite : appDelegate.arrImageCopy;
        dictGropImages = [mutDicDisplayMvGroupName mutableCopy];
    } else {
        if (appDelegate.isFavourite) {
            NSMutableArray *favouriteArray = [NSMutableArray array];
            for (NSArray *collection in appDelegate.arrPresentation) {
                NSMutableArray *favouriteArray1 = [NSMutableArray array];
                NSUInteger index = [collection indexOfObjectPassingTest:^BOOL(NSDictionary *obj, NSUInteger idx, BOOL *stop) {
                    return [mutArrDisplayMvGroupFavourite containsObject:obj[@"collectionID"]];
                }];
                if (index != NSNotFound) {
                    favouriteArray1 = (NSMutableArray*)[collection subarrayWithRange:NSMakeRange(index, collection.count - index)];
                }
                [favouriteArray addObject:favouriteArray1];
            }
            
            NSMutableArray *tempArr1 = [NSMutableArray array];
            for (NSArray *arr in favouriteArray) {
                if (arr.count > 0) {
                    [tempArr1 addObject:arr];
                }
            }
            
            appDelegate.arrPresentationFavourite = tempArr1;
            appDelegate.arrScrollViewCount = appDelegate.arrPresentationFavourite;
        } else {
            [self getDataForPresentationSection:YES];
        }
    }
    
    NSIndexSet *sections = [NSIndexSet indexSetWithIndexesInRange:NSMakeRange(0, [self.tblPartAnimationArea numberOfSections])];
    [self.tblPartAnimationArea reloadSections:sections withRowAnimation:UITableViewRowAnimationFade];
}

- (NSArray *)filterFavouritesInArray:(NSArray *)array {
    NSMutableArray *filteredArray = [NSMutableArray array];
    for (NSArray *collection in array) {
        NSMutableArray *filteredCollection = [NSMutableArray array];
        for (NSDictionary *item in collection) {
            NSNumber *isFavourite = item[@"isFavourite"];
            if (isFavourite && [isFavourite boolValue]) {
                [filteredCollection addObject:item];
            }
        }
        if (filteredCollection.count > 0) {
            [filteredArray addObject:filteredCollection];
        }
    }
    return filteredArray;
}

- (void)handleSearchBarFilteredItemsEmpty:(NSNotification *)notification {
    [self refreshDataAfterSearch];
}

- (void)refreshDataAfterSearch {
    [self getDataForAllTabs];
    [self setTabSelection];
}

-(void)clearSearchPresentation:(NSNotification *)notification {
    [self clearPreviousSelectedArray];
    [self setTabSelection];
}


- (void)setTabSelection {
    if (appDelegate.intSelectedTab == 0) {
        [self MySegmentControlAction:btnMovie];
    } else if (appDelegate.intSelectedTab == 1) {
        [self MySegmentControlAction:btnPicture];
    } else {
        [self MySegmentControlAction:btnPresentation];
    }
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

@end
