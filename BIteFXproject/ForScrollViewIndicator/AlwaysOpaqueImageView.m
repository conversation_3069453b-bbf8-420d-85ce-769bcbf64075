//
//  AlwaysOpaqueImageView.m
//  BIteFXproject
//
//  Created by ind563 on 7/14/17.
//
//

#import "AlwaysOpaqueImageView.h"

@implementation AlwaysOpaqueImageView

/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/

- (void)setAlpha:(CGFloat)alpha {
    super.alpha = 1.0;
}


@end
