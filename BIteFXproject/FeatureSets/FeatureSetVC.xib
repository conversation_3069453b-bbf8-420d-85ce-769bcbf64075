<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="19162" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="19144"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="FeatureSetVC">
            <connections>
                <outlet property="view" destination="wca-P7-UWh" id="hC1-8v-8Hg"/>
                <outletCollection property="imgViewFeatureSets" destination="uTD-Cx-GPN" id="7y1-DE-AKu"/>
                <outletCollection property="imgViewFeatureSets" destination="03E-hl-rGi" id="8Hm-bb-LPN"/>
                <outletCollection property="imgViewFeatureSets" destination="tiq-5U-8UM" id="3kt-f1-TRG"/>
                <outletCollection property="imgViewFeatureSets" destination="EzT-1U-sXm" id="oFn-oG-AMJ"/>
                <outletCollection property="imgViewFeatureSets" destination="BOs-eD-TNi" id="kJE-nA-AfT"/>
                <outletCollection property="lblFeatureSetNames" destination="Ajn-yg-rBb" id="9yU-Co-Lc0"/>
                <outletCollection property="lblFeatureSetNames" destination="znI-OK-35z" id="nxC-Sw-h7l"/>
                <outletCollection property="lblFeatureSetNames" destination="vMk-fb-zv7" id="3K2-nf-MEb"/>
                <outletCollection property="lblFeatureSetNames" destination="6Xz-Dl-8tB" id="u7N-0i-Ezd"/>
                <outletCollection property="lblFeatureSetNames" destination="HBX-sK-EMc" id="4oO-zM-xGz"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="wca-P7-UWh">
            <rect key="frame" x="0.0" y="0.0" width="929" height="661"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <subviews>
                <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="1" translatesAutoresizingMaskIntoConstraints="NO" id="qIh-9f-Yna">
                    <rect key="frame" x="1" y="1" width="927" height="659"/>
                    <subviews>
                        <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" spacing="1" translatesAutoresizingMaskIntoConstraints="NO" id="8jL-HK-sWv">
                            <rect key="frame" x="0.0" y="0.0" width="463" height="659"/>
                            <subviews>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="a25-K6-HUw">
                                    <rect key="frame" x="0.0" y="0.0" width="463" height="219"/>
                                    <userGuides>
                                        <userLayoutGuide location="127" affinity="minY"/>
                                    </userGuides>
                                    <subviews>
                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="uTD-Cx-GPN">
                                            <rect key="frame" x="0.0" y="0.0" width="463" height="219"/>
                                        </imageView>
                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="rvN-fq-V1Y">
                                            <rect key="frame" x="396.5" y="8" width="58.5" height="25"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Ajn-yg-rBb">
                                                    <rect key="frame" x="8" y="0.0" width="42.5" height="25"/>
                                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <fontDescription key="fontDescription" type="boldSystem" pointSize="16"/>
                                                    <color key="textColor" red="0.99215686270000003" green="0.80000000000000004" blue="0.33725490200000002" alpha="1" colorSpace="calibratedRGB"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                            </subviews>
                                            <color key="backgroundColor" red="0.14509803921568626" green="0.14509803921568626" blue="0.14509803921568626" alpha="0.5" colorSpace="calibratedRGB"/>
                                            <constraints>
                                                <constraint firstAttribute="trailing" secondItem="Ajn-yg-rBb" secondAttribute="trailing" constant="8" id="0NP-hi-LEE"/>
                                                <constraint firstAttribute="height" constant="25" id="2xC-2F-WJ5"/>
                                                <constraint firstItem="Ajn-yg-rBb" firstAttribute="top" secondItem="rvN-fq-V1Y" secondAttribute="top" id="Oyx-2D-u3M"/>
                                                <constraint firstItem="Ajn-yg-rBb" firstAttribute="leading" secondItem="rvN-fq-V1Y" secondAttribute="leading" constant="8" id="Su6-Mu-JpV"/>
                                                <constraint firstAttribute="bottom" secondItem="Ajn-yg-rBb" secondAttribute="bottom" id="VNO-f8-oVD"/>
                                            </constraints>
                                        </view>
                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="7XD-48-KWr">
                                            <rect key="frame" x="0.0" y="0.0" width="463" height="219"/>
                                            <connections>
                                                <action selector="btnFeatureSetClicked:" destination="-1" eventType="touchUpInside" id="N3D-Q5-uZk"/>
                                            </connections>
                                        </button>
                                    </subviews>
                                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                    <constraints>
                                        <constraint firstAttribute="trailing" secondItem="rvN-fq-V1Y" secondAttribute="trailing" constant="8" id="GJM-PW-6lO"/>
                                        <constraint firstAttribute="trailing" secondItem="7XD-48-KWr" secondAttribute="trailing" id="GnW-rU-W4f"/>
                                        <constraint firstAttribute="bottom" secondItem="uTD-Cx-GPN" secondAttribute="bottom" id="HqY-XV-27a"/>
                                        <constraint firstAttribute="trailing" secondItem="uTD-Cx-GPN" secondAttribute="trailing" id="M35-GX-gpK"/>
                                        <constraint firstItem="rvN-fq-V1Y" firstAttribute="top" secondItem="a25-K6-HUw" secondAttribute="top" constant="8" id="Nph-cV-O3m"/>
                                        <constraint firstItem="uTD-Cx-GPN" firstAttribute="leading" secondItem="a25-K6-HUw" secondAttribute="leading" id="dGH-Q7-5zv"/>
                                        <constraint firstItem="7XD-48-KWr" firstAttribute="top" secondItem="a25-K6-HUw" secondAttribute="top" id="hJZ-C7-O1c"/>
                                        <constraint firstItem="uTD-Cx-GPN" firstAttribute="top" secondItem="a25-K6-HUw" secondAttribute="top" id="mD3-AY-EYi"/>
                                        <constraint firstAttribute="bottom" secondItem="7XD-48-KWr" secondAttribute="bottom" id="rZg-rp-Z1a"/>
                                        <constraint firstItem="7XD-48-KWr" firstAttribute="leading" secondItem="a25-K6-HUw" secondAttribute="leading" id="vbx-0j-6md"/>
                                    </constraints>
                                </view>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="tHk-X0-VnP">
                                    <rect key="frame" x="0.0" y="220" width="463" height="219"/>
                                    <subviews>
                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="tiq-5U-8UM">
                                            <rect key="frame" x="0.0" y="0.0" width="463" height="219"/>
                                        </imageView>
                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="zN3-gf-6rG">
                                            <rect key="frame" x="396.5" y="8" width="58.5" height="25"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="vMk-fb-zv7">
                                                    <rect key="frame" x="8" y="0.0" width="42.5" height="25"/>
                                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <fontDescription key="fontDescription" type="boldSystem" pointSize="16"/>
                                                    <color key="textColor" red="0.99215686270000003" green="0.80000000000000004" blue="0.33725490200000002" alpha="1" colorSpace="calibratedRGB"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                            </subviews>
                                            <color key="backgroundColor" red="0.1450980392" green="0.1450980392" blue="0.1450980392" alpha="0.5" colorSpace="calibratedRGB"/>
                                            <constraints>
                                                <constraint firstAttribute="height" constant="25" id="52Y-AI-LVg"/>
                                                <constraint firstItem="vMk-fb-zv7" firstAttribute="leading" secondItem="zN3-gf-6rG" secondAttribute="leading" constant="8" id="9nI-bg-UKF"/>
                                                <constraint firstItem="vMk-fb-zv7" firstAttribute="top" secondItem="zN3-gf-6rG" secondAttribute="top" id="FB6-IW-Zhm"/>
                                                <constraint firstAttribute="trailing" secondItem="vMk-fb-zv7" secondAttribute="trailing" constant="8" id="K0T-2W-eAa"/>
                                                <constraint firstAttribute="bottom" secondItem="vMk-fb-zv7" secondAttribute="bottom" id="gsl-pR-U4d"/>
                                            </constraints>
                                        </view>
                                        <button opaque="NO" tag="2" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="M3D-4E-smK">
                                            <rect key="frame" x="0.0" y="0.0" width="463" height="219"/>
                                            <connections>
                                                <action selector="btnFeatureSetClicked:" destination="-1" eventType="touchUpInside" id="q4e-ds-sIg"/>
                                            </connections>
                                        </button>
                                    </subviews>
                                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                    <constraints>
                                        <constraint firstAttribute="trailing" secondItem="tiq-5U-8UM" secondAttribute="trailing" id="7k6-zE-UCj"/>
                                        <constraint firstAttribute="bottom" secondItem="tiq-5U-8UM" secondAttribute="bottom" id="7mC-4W-PyE"/>
                                        <constraint firstItem="M3D-4E-smK" firstAttribute="top" secondItem="tHk-X0-VnP" secondAttribute="top" id="GeN-eo-R9T"/>
                                        <constraint firstAttribute="trailing" secondItem="M3D-4E-smK" secondAttribute="trailing" id="NZ6-VF-SnE"/>
                                        <constraint firstItem="tiq-5U-8UM" firstAttribute="top" secondItem="tHk-X0-VnP" secondAttribute="top" id="YOH-8z-esX"/>
                                        <constraint firstItem="tiq-5U-8UM" firstAttribute="leading" secondItem="tHk-X0-VnP" secondAttribute="leading" id="cby-Ip-EY3"/>
                                        <constraint firstItem="zN3-gf-6rG" firstAttribute="top" secondItem="tHk-X0-VnP" secondAttribute="top" constant="8" id="eEc-oG-2is"/>
                                        <constraint firstAttribute="trailing" secondItem="zN3-gf-6rG" secondAttribute="trailing" constant="8" id="eyh-Wg-1xO"/>
                                        <constraint firstAttribute="bottom" secondItem="M3D-4E-smK" secondAttribute="bottom" id="hvl-zl-FVD"/>
                                        <constraint firstItem="M3D-4E-smK" firstAttribute="leading" secondItem="tHk-X0-VnP" secondAttribute="leading" id="jFN-E6-JEL"/>
                                    </constraints>
                                </view>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Hde-v6-Y8u">
                                    <rect key="frame" x="0.0" y="440" width="463" height="219"/>
                                    <subviews>
                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="BOs-eD-TNi">
                                            <rect key="frame" x="0.0" y="0.0" width="463" height="219"/>
                                        </imageView>
                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="3cf-lW-qnH">
                                            <rect key="frame" x="396.5" y="8" width="58.5" height="25"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="HBX-sK-EMc">
                                                    <rect key="frame" x="8" y="0.0" width="42.5" height="25"/>
                                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <fontDescription key="fontDescription" type="boldSystem" pointSize="16"/>
                                                    <color key="textColor" red="0.99215686270000003" green="0.80000000000000004" blue="0.33725490200000002" alpha="1" colorSpace="calibratedRGB"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                            </subviews>
                                            <color key="backgroundColor" red="0.1450980392" green="0.1450980392" blue="0.1450980392" alpha="0.5" colorSpace="calibratedRGB"/>
                                            <constraints>
                                                <constraint firstAttribute="bottom" secondItem="HBX-sK-EMc" secondAttribute="bottom" id="J1d-1z-keW"/>
                                                <constraint firstItem="HBX-sK-EMc" firstAttribute="leading" secondItem="3cf-lW-qnH" secondAttribute="leading" constant="8" id="Pqs-T8-mtd"/>
                                                <constraint firstAttribute="height" constant="25" id="PtF-V8-TS8"/>
                                                <constraint firstAttribute="trailing" secondItem="HBX-sK-EMc" secondAttribute="trailing" constant="8" id="Yge-eG-sxK"/>
                                                <constraint firstItem="HBX-sK-EMc" firstAttribute="top" secondItem="3cf-lW-qnH" secondAttribute="top" id="mw9-z8-bOW"/>
                                            </constraints>
                                        </view>
                                        <button opaque="NO" tag="4" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="EoI-PF-Q48">
                                            <rect key="frame" x="0.0" y="0.0" width="463" height="219"/>
                                            <connections>
                                                <action selector="btnFeatureSetClicked:" destination="-1" eventType="touchUpInside" id="LfP-19-Q9z"/>
                                            </connections>
                                        </button>
                                    </subviews>
                                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                    <constraints>
                                        <constraint firstItem="BOs-eD-TNi" firstAttribute="leading" secondItem="Hde-v6-Y8u" secondAttribute="leading" id="3Ix-cw-qqW"/>
                                        <constraint firstAttribute="trailing" secondItem="BOs-eD-TNi" secondAttribute="trailing" id="5Fx-lk-Mr0"/>
                                        <constraint firstAttribute="bottom" secondItem="EoI-PF-Q48" secondAttribute="bottom" id="GEC-rd-T40"/>
                                        <constraint firstAttribute="trailing" secondItem="EoI-PF-Q48" secondAttribute="trailing" id="IfF-L1-Wdk"/>
                                        <constraint firstItem="EoI-PF-Q48" firstAttribute="leading" secondItem="Hde-v6-Y8u" secondAttribute="leading" id="KsL-h5-InB"/>
                                        <constraint firstItem="3cf-lW-qnH" firstAttribute="top" secondItem="Hde-v6-Y8u" secondAttribute="top" constant="8" id="Rd5-jv-mYi"/>
                                        <constraint firstItem="EoI-PF-Q48" firstAttribute="top" secondItem="Hde-v6-Y8u" secondAttribute="top" id="UcL-ve-GD9"/>
                                        <constraint firstAttribute="trailing" secondItem="3cf-lW-qnH" secondAttribute="trailing" constant="8" id="bPU-z9-yQC"/>
                                        <constraint firstItem="BOs-eD-TNi" firstAttribute="top" secondItem="Hde-v6-Y8u" secondAttribute="top" id="ixN-br-UZr"/>
                                        <constraint firstAttribute="bottom" secondItem="BOs-eD-TNi" secondAttribute="bottom" id="uRZ-y8-VC5"/>
                                    </constraints>
                                </view>
                            </subviews>
                        </stackView>
                        <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillProportionally" spacing="1" translatesAutoresizingMaskIntoConstraints="NO" id="iNn-8p-HGq">
                            <rect key="frame" x="464" y="0.0" width="463" height="659"/>
                            <subviews>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Gl6-3Q-Vdn">
                                    <rect key="frame" x="0.0" y="0.0" width="463" height="218.5"/>
                                    <subviews>
                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="03E-hl-rGi">
                                            <rect key="frame" x="0.0" y="0.0" width="463" height="218.5"/>
                                            <constraints>
                                                <constraint firstAttribute="height" constant="218.66999999999999" id="B4A-dc-CnT"/>
                                            </constraints>
                                        </imageView>
                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="8f7-1e-bnV">
                                            <rect key="frame" x="396.5" y="8" width="58.5" height="25"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="znI-OK-35z">
                                                    <rect key="frame" x="8" y="0.0" width="42.5" height="25"/>
                                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <fontDescription key="fontDescription" type="boldSystem" pointSize="16"/>
                                                    <color key="textColor" red="0.99215686270000003" green="0.80000000000000004" blue="0.33725490200000002" alpha="1" colorSpace="calibratedRGB"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                            </subviews>
                                            <color key="backgroundColor" red="0.1450980392" green="0.1450980392" blue="0.1450980392" alpha="0.5" colorSpace="calibratedRGB"/>
                                            <constraints>
                                                <constraint firstAttribute="trailing" secondItem="znI-OK-35z" secondAttribute="trailing" constant="8" id="7QB-sx-AjI"/>
                                                <constraint firstItem="znI-OK-35z" firstAttribute="top" secondItem="8f7-1e-bnV" secondAttribute="top" id="7kU-FS-Vbo"/>
                                                <constraint firstItem="znI-OK-35z" firstAttribute="leading" secondItem="8f7-1e-bnV" secondAttribute="leading" constant="8" id="Ved-io-3DJ"/>
                                                <constraint firstAttribute="bottom" secondItem="znI-OK-35z" secondAttribute="bottom" id="WSr-g1-11G"/>
                                                <constraint firstAttribute="height" constant="25" id="n8B-9h-4Hc"/>
                                            </constraints>
                                        </view>
                                        <button opaque="NO" tag="1" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="sfo-Pr-6Dr">
                                            <rect key="frame" x="0.0" y="0.0" width="463" height="218.5"/>
                                            <connections>
                                                <action selector="btnFeatureSetClicked:" destination="-1" eventType="touchUpInside" id="pdj-LG-lMF"/>
                                            </connections>
                                        </button>
                                    </subviews>
                                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                    <constraints>
                                        <constraint firstAttribute="trailing" secondItem="sfo-Pr-6Dr" secondAttribute="trailing" id="0dj-yl-h1j"/>
                                        <constraint firstItem="sfo-Pr-6Dr" firstAttribute="top" secondItem="Gl6-3Q-Vdn" secondAttribute="top" id="KsQ-09-TDO"/>
                                        <constraint firstItem="8f7-1e-bnV" firstAttribute="top" secondItem="Gl6-3Q-Vdn" secondAttribute="top" constant="8" id="Oik-Iu-0Iw"/>
                                        <constraint firstAttribute="trailing" secondItem="03E-hl-rGi" secondAttribute="trailing" id="UPh-Zx-AI9"/>
                                        <constraint firstItem="sfo-Pr-6Dr" firstAttribute="leading" secondItem="Gl6-3Q-Vdn" secondAttribute="leading" id="WKD-gD-laG"/>
                                        <constraint firstAttribute="bottom" secondItem="03E-hl-rGi" secondAttribute="bottom" id="XPa-H9-YBm"/>
                                        <constraint firstAttribute="trailing" secondItem="8f7-1e-bnV" secondAttribute="trailing" constant="8" id="m01-PQ-FEj"/>
                                        <constraint firstAttribute="bottom" secondItem="sfo-Pr-6Dr" secondAttribute="bottom" id="mJR-ia-pPS"/>
                                        <constraint firstItem="03E-hl-rGi" firstAttribute="top" secondItem="Gl6-3Q-Vdn" secondAttribute="top" id="nRN-ID-i3E"/>
                                        <constraint firstItem="03E-hl-rGi" firstAttribute="leading" secondItem="Gl6-3Q-Vdn" secondAttribute="leading" id="oFp-rc-9nf"/>
                                    </constraints>
                                </view>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="LDP-0G-Lf1">
                                    <rect key="frame" x="0.0" y="219.5" width="463" height="439.5"/>
                                    <subviews>
                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="EzT-1U-sXm">
                                            <rect key="frame" x="0.0" y="0.0" width="463" height="439.5"/>
                                        </imageView>
                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="WdC-zv-hVQ">
                                            <rect key="frame" x="396.5" y="8" width="58.5" height="25"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="6Xz-Dl-8tB">
                                                    <rect key="frame" x="8" y="0.0" width="42.5" height="25"/>
                                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <fontDescription key="fontDescription" type="boldSystem" pointSize="16"/>
                                                    <color key="textColor" red="0.99215686270000003" green="0.80000000000000004" blue="0.33725490200000002" alpha="1" colorSpace="calibratedRGB"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                            </subviews>
                                            <color key="backgroundColor" red="0.1450980392" green="0.1450980392" blue="0.1450980392" alpha="0.5" colorSpace="calibratedRGB"/>
                                            <constraints>
                                                <constraint firstItem="6Xz-Dl-8tB" firstAttribute="leading" secondItem="WdC-zv-hVQ" secondAttribute="leading" constant="8" id="WXz-0w-YXs"/>
                                                <constraint firstAttribute="trailing" secondItem="6Xz-Dl-8tB" secondAttribute="trailing" constant="8" id="Y6t-V9-ohs"/>
                                                <constraint firstItem="6Xz-Dl-8tB" firstAttribute="top" secondItem="WdC-zv-hVQ" secondAttribute="top" id="ceF-qT-8hs"/>
                                                <constraint firstAttribute="height" constant="25" id="eam-Co-XxN"/>
                                                <constraint firstAttribute="bottom" secondItem="6Xz-Dl-8tB" secondAttribute="bottom" id="moT-pD-bSM"/>
                                            </constraints>
                                        </view>
                                        <button opaque="NO" tag="3" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="LN5-DT-wK2">
                                            <rect key="frame" x="0.0" y="0.0" width="463" height="439.5"/>
                                            <connections>
                                                <action selector="btnFeatureSetClicked:" destination="-1" eventType="touchUpInside" id="LUo-CJ-Wve"/>
                                            </connections>
                                        </button>
                                    </subviews>
                                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                    <constraints>
                                        <constraint firstAttribute="trailing" secondItem="WdC-zv-hVQ" secondAttribute="trailing" constant="8" id="5DE-ds-fZ9"/>
                                        <constraint firstItem="EzT-1U-sXm" firstAttribute="top" secondItem="LDP-0G-Lf1" secondAttribute="top" id="9CA-zp-Chq"/>
                                        <constraint firstAttribute="bottom" secondItem="LN5-DT-wK2" secondAttribute="bottom" id="gh9-cZ-2gq"/>
                                        <constraint firstItem="WdC-zv-hVQ" firstAttribute="top" secondItem="LDP-0G-Lf1" secondAttribute="top" constant="8" id="gmW-K8-TQt"/>
                                        <constraint firstItem="LN5-DT-wK2" firstAttribute="top" secondItem="LDP-0G-Lf1" secondAttribute="top" id="gnB-BU-c1b"/>
                                        <constraint firstAttribute="trailing" secondItem="EzT-1U-sXm" secondAttribute="trailing" id="jDY-HS-Wgx"/>
                                        <constraint firstItem="LN5-DT-wK2" firstAttribute="leading" secondItem="LDP-0G-Lf1" secondAttribute="leading" id="oTY-Rl-cTT"/>
                                        <constraint firstAttribute="bottom" secondItem="EzT-1U-sXm" secondAttribute="bottom" id="xGu-MP-NAM"/>
                                        <constraint firstItem="EzT-1U-sXm" firstAttribute="leading" secondItem="LDP-0G-Lf1" secondAttribute="leading" id="xJK-Gl-8lW"/>
                                        <constraint firstAttribute="trailing" secondItem="LN5-DT-wK2" secondAttribute="trailing" id="xlq-KA-Hia"/>
                                    </constraints>
                                </view>
                            </subviews>
                        </stackView>
                    </subviews>
                </stackView>
            </subviews>
            <viewLayoutGuide key="safeArea" id="ckl-sa-ygp"/>
            <color key="backgroundColor" red="1" green="0.81960784310000001" blue="0.35294117650000001" alpha="1" colorSpace="calibratedRGB"/>
            <constraints>
                <constraint firstItem="qIh-9f-Yna" firstAttribute="top" secondItem="wca-P7-UWh" secondAttribute="top" constant="1" id="63a-bX-n0W"/>
                <constraint firstAttribute="bottom" secondItem="qIh-9f-Yna" secondAttribute="bottom" constant="1" id="h02-0A-y3X"/>
                <constraint firstItem="qIh-9f-Yna" firstAttribute="leading" secondItem="wca-P7-UWh" secondAttribute="leading" constant="1" id="itE-jA-g5q"/>
                <constraint firstAttribute="trailing" secondItem="qIh-9f-Yna" secondAttribute="trailing" constant="1" id="l1d-6X-oVj"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <point key="canvasLocation" x="-1120" y="-1546"/>
        </view>
    </objects>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
