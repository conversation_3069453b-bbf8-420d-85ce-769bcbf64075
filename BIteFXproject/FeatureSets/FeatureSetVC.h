//
//  FeatureSetVC.h
//  BIteFXproject
//
//  Created by indianic on 08/12/21.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

//===================
// Version 3.1 Changes
//===================
// Feature MVI file changes.
// FeatureSetVC is created to show toplevel presentation sets called FeatureSets...
typedef void(^btnClickBlock)(int featureID);

@interface FeatureSetVC: UIViewController {
    
    // IBOutlets...
//    IBOutlet UILabel *lblFeatureSetName1;
//    IBOutlet UILabel *lblFeatureSetName2;
//    IBOutlet UILabel *lblFeatureSetName3;
//    IBOutlet UILabel *lblFeatureSetName4;
//    IBOutlet UILabel *lblFeatureSetName5;
//    
//    IBOutlet UIImageView *imgViewFeatureSet1;
//    IBOutlet UIImageView *imgViewFeatureSet2;
//    IBOutlet UIImageView *imgViewFeatureSet3;
//    IBOutlet UIImageView *imgViewFeatureSet4;
//    IBOutlet UIImageView *imgViewFeatureSet5;
}

@property (nonatomic, strong) IBOutletCollection(UIImageView) NSArray *imgViewFeatureSets;
@property (nonatomic, strong) IBOutletCollection(UILabel) NSArray *lblFeatureSetNames;

@property (nonatomic, strong) NSMutableArray *arrFeatureSets;
@property (nonatomic, strong) btnClickBlock btnFeatureSetClickBlock;


// IBActions
-(IBAction)btnFeatureSetClicked:(UIButton*)sender;

@end

NS_ASSUME_NONNULL_END
