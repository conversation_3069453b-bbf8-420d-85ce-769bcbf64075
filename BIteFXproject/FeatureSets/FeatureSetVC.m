//
//  FeatureSetVC.m
//  BIteFXproject
//
//  Created by indianic on 08/12/21.
//

#import "FeatureSetVC.h"
#import "Database.h"

@interface FeatureSetVC ()

@end

@implementation FeatureSetVC

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view from its nib.
    
    // Get FeatureSets Data...
    [self getFeatureSets];
    
    // Set Features data in UI...
    [self setData];
    
}

/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/

#pragma mark - IBActions

-(IBAction)btnFeatureSetClicked:(UIButton*)sender {
    int aIntFeatureId = [[(self.arrFeatureSets)[sender.tag] valueForKey:@"featureID"] intValue];
    self.btnFeatureSetClickBlock(aIntFeatureId);
}

#pragma mark - Setup UI methods
-(void)setData {
    
    for(int index=0;index<(self.arrFeatureSets).count;index++) {
        
        UIImageView *imgView = self.imgViewFeatureSets[index];
        NSString *aStrPath = [(self.arrFeatureSets)[index] valueForKey:@"imageFilePath"];
        NSString *aStrImagePath = [NSString stringWithFormat:@"%@/%@/%@", DOCUMENT_DIRECTORY_PATH, BITEFXV2, aStrPath];
        imgView.image = [UIImage imageWithContentsOfFile:aStrImagePath];
        
        UILabel *aLbl = self.lblFeatureSetNames[index];
        aLbl.text = [(self.arrFeatureSets)[index] valueForKey:@"featureName"];
    }
}

#pragma mark - Database methods

-(void)getFeatureSets {
    
    NSString *aStrQuery = [NSString stringWithFormat:@"select * from FeatureMaster"];
    [self.arrFeatureSets removeAllObjects];
    self.arrFeatureSets = [[Database sharedDatabase] getAllDataForQuery:aStrQuery];
}

@end
