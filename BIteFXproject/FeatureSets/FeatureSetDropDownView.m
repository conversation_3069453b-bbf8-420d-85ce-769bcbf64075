//
//  FeatureSetDropDownView.m
//  BIteFXproject
//
//  Created by indianic on 14/12/21.
//

#import "FeatureSetDropDownView.h"
#import "Database.h"

#define darkGrayColor [UIColor colorWithRed:(92.0/255.0) green:(98.0/255.0) blue:(103.0/255.0) alpha:1.0];
#define yellowColor [UIColor colorWithRed:(253.0/255.0) green:(204.0/255.0) blue:(86.0/255.0) alpha:1.0];

@implementation FeatureSetDropDownView

- (instancetype)initWithCoder:(NSCoder *)coder {
    
    self = [super initWithCoder:coder];
    if (self) {
        [self customInit];
    }
    
    return self;
}

- (instancetype)initWithFrame:(CGRect)frame {
    
    self = [super initWithFrame:frame];
    if (self) {
        [self customInit];
    }
    
    return self;
}

- (void)customInit {
 
    [[NSBundle mainBundle] loadNibNamed:@"FeatureSetDropDownView" owner:self options:nil];
    [self addSubview:self.viewContent];
    self.viewContent.frame = self.bounds;
    
    // Get FeatureSets Data...
    [self getFeatureSets];
    
    // Set Features data in UI...
    [self setData];
    
    // Setup Gestures...
    [self setupGestures];

}

#pragma mark - IBActions

-(IBAction)btnDownArrowClicked:(UIButton*)sender {
    self.btnDownArrow.selected = !self.btnDownArrow.selected;
    if (self.btnDownArrow.selected) {
        self.btnDownArrow.tintColor = darkGrayColor;
    } else {
        self.btnDownArrow.tintColor = yellowColor;
    }
    self.viewFeatureNames.hidden = !self.btnDownArrow.selected;
}

-(IBAction)btnCloseClicked:(UIButton*)sender {    
    self.btnCloseClickBlock();
}

-(IBAction)btnFeatureSetClicked:(UIButton*)sender {
    int aIntFeatureId = [[(self.arrFeatureSets)[sender.tag] valueForKey:@"featureID"] intValue];
    self.btnDownArrow.selected = NO;
    self.btnDownArrow.tintColor = yellowColor;
    self.viewFeatureNames.hidden = YES;
    
    self.lblSelectedFeatureName.text = [(self.arrFeatureSets)[sender.tag] valueForKey:@"featureName"];
    self.btnFeatureSetClickBlock(aIntFeatureId);
}

#pragma mark - Setup UI methods
-(void)setData {
    
    // Get FeatureID...
    NSInteger aIntFeatureId = [[NSUserDefaults standardUserDefaults] integerForKey:SELECTED_FEATURE_SET_ID];
    
    for(int index=0;index<(self.arrFeatureSets).count;index++) {
        
        UIButton *aBtn = self.btnFeatureSetNames[index];
        [aBtn setTitle:[(self.arrFeatureSets)[index] valueForKey:@"featureName"] forState:UIControlStateNormal];
        
        NSInteger aId = [[(self.arrFeatureSets)[index] valueForKey:@"featureID"] integerValue];
        // Show selected featureset name...
        if (aIntFeatureId == aId) {
            self.lblSelectedFeatureName.text = [(self.arrFeatureSets)[index] valueForKey:@"featureName"];
        }
    }
    
}

#pragma mark - Database methods

-(void)getFeatureSets {
    
    NSString *aStrQuery = [NSString stringWithFormat:@"select * from FeatureMaster Order By featureName ASC"];
    [self.arrFeatureSets removeAllObjects];
    self.arrFeatureSets = [[Database sharedDatabase] getAllDataForQuery:aStrQuery];
}

#pragma mark - UITapGestureRecognizer methods

- (void)setupGestures {
    
    UITapGestureRecognizer *tapGestureRecognizer = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(handleTapGesture:)];
    tapGestureRecognizer.numberOfTapsRequired = 1;
    tapGestureRecognizer.delegate = self;
    [self addGestureRecognizer:tapGestureRecognizer];
    
}

- (void)handleTapGesture:(UITapGestureRecognizer *)tapGestureRecognizer {
   
    // Close the dropdown for anytouch outside the dropdown view...
    if (self.btnDownArrow.selected) {
        [self btnDownArrowClicked:self.btnDownArrow];
    }
    
}

# pragma mark -----------------------
# pragma mark UIGestureRecognizerDelegate Methods

- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldReceiveTouch:(UITouch *)touch
{
    // Disable tap gesture for main dropdown view...
    if ([touch.view isDescendantOfView:self.viewDropDown]) {
        return NO;
    }
    return YES;

}

@end
