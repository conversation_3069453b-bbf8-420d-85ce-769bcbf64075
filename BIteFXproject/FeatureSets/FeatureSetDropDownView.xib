<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="17701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="17703"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="FeatureSetDropDownView">
            <connections>
                <outlet property="btnDownArrow" destination="4gZ-U6-1d4" id="RMU-oh-e5s"/>
                <outlet property="lblSelectedFeatureName" destination="C5G-3I-j69" id="jmp-hH-DR6"/>
                <outlet property="viewContent" destination="iN0-l3-epB" id="6cv-cr-Oi2"/>
                <outlet property="viewDropDown" destination="nlL-TY-yaQ" id="293-LA-xHj"/>
                <outlet property="viewFeatureNames" destination="SlO-YS-CPQ" id="21V-dS-sIz"/>
                <outletCollection property="btnFeatureSetNames" destination="A6s-Uf-8Bu" id="Ato-TS-He7"/>
                <outletCollection property="btnFeatureSetNames" destination="kGh-fm-lqt" id="47C-0W-jMv"/>
                <outletCollection property="btnFeatureSetNames" destination="oD8-Da-2gZ" id="MDg-Qk-1DW"/>
                <outletCollection property="btnFeatureSetNames" destination="KUd-Ed-K2g" id="tkc-KV-Wji"/>
                <outletCollection property="btnFeatureSetNames" destination="9OC-0e-rGA" id="jjx-4Y-w2Z"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="iN0-l3-epB">
            <rect key="frame" x="0.0" y="0.0" width="400" height="190"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="nlL-TY-yaQ">
                    <rect key="frame" x="140" y="0.0" width="260" height="190"/>
                    <subviews>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="eAR-aA-SYD" userLabel="Selected Name View">
                            <rect key="frame" x="0.0" y="0.0" width="230" height="30"/>
                            <subviews>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Presentations" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="C5G-3I-j69">
                                    <rect key="frame" x="0.0" y="0.0" width="188" height="30"/>
                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <fontDescription key="fontDescription" type="boldSystem" pointSize="17"/>
                                    <color key="textColor" red="0.99215686274509807" green="0.80000000000000004" blue="0.33725490196078434" alpha="1" colorSpace="calibratedRGB"/>
                                    <nil key="highlightedColor"/>
                                </label>
                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="4gZ-U6-1d4">
                                    <rect key="frame" x="198" y="0.0" width="30" height="30"/>
                                    <constraints>
                                        <constraint firstAttribute="width" secondItem="4gZ-U6-1d4" secondAttribute="height" multiplier="1:1" id="1hN-ZV-WD9"/>
                                        <constraint firstAttribute="width" constant="30" id="jsR-rE-kV2"/>
                                    </constraints>
                                    <color key="tintColor" red="0.99215686274509807" green="0.80000000000000004" blue="0.33725490196078434" alpha="1" colorSpace="calibratedRGB"/>
                                    <state key="normal" image="down"/>
                                    <connections>
                                        <action selector="btnDownArrowClicked:" destination="-1" eventType="touchUpInside" id="rt4-Gz-ClZ"/>
                                    </connections>
                                </button>
                            </subviews>
                            <color key="backgroundColor" red="0.098039215686274508" green="0.098039215686274508" blue="0.098039215686274508" alpha="0.5" colorSpace="custom" customColorSpace="calibratedRGB"/>
                            <constraints>
                                <constraint firstAttribute="trailing" secondItem="4gZ-U6-1d4" secondAttribute="trailing" constant="2" id="0Zk-ro-qXf"/>
                                <constraint firstAttribute="bottom" secondItem="4gZ-U6-1d4" secondAttribute="bottom" id="3SR-Sk-f8I"/>
                                <constraint firstItem="4gZ-U6-1d4" firstAttribute="leading" secondItem="C5G-3I-j69" secondAttribute="trailing" constant="10" id="82g-34-Vqa"/>
                                <constraint firstAttribute="bottom" secondItem="C5G-3I-j69" secondAttribute="bottom" id="GGP-Pa-Jo9"/>
                                <constraint firstItem="C5G-3I-j69" firstAttribute="top" secondItem="eAR-aA-SYD" secondAttribute="top" id="cW3-IT-Xc0"/>
                                <constraint firstAttribute="height" constant="30" id="crR-OM-GTP"/>
                                <constraint firstItem="C5G-3I-j69" firstAttribute="leading" secondItem="eAR-aA-SYD" secondAttribute="leading" id="m3D-G7-9cO"/>
                                <constraint firstItem="4gZ-U6-1d4" firstAttribute="top" secondItem="eAR-aA-SYD" secondAttribute="top" id="yel-JB-EfT"/>
                            </constraints>
                        </view>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="bSN-eA-lTy">
                            <rect key="frame" x="230" y="0.0" width="30" height="30"/>
                            <color key="backgroundColor" red="0.098039215690000001" green="0.098039215690000001" blue="0.098039215690000001" alpha="0.5" colorSpace="custom" customColorSpace="calibratedRGB"/>
                            <constraints>
                                <constraint firstAttribute="width" secondItem="bSN-eA-lTy" secondAttribute="height" multiplier="1:1" id="3ey-JH-rQB"/>
                                <constraint firstAttribute="width" constant="30" id="Q4d-sn-BPr"/>
                                <constraint firstAttribute="width" secondItem="bSN-eA-lTy" secondAttribute="height" multiplier="1:1" id="nxm-YA-a50"/>
                            </constraints>
                            <color key="tintColor" red="0.99215686270000003" green="0.80000000000000004" blue="0.33725490200000002" alpha="1" colorSpace="calibratedRGB"/>
                            <state key="normal" image="close"/>
                            <connections>
                                <action selector="btnCloseClicked:" destination="-1" eventType="touchUpInside" id="q5J-7d-WYe"/>
                            </connections>
                        </button>
                        <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="SlO-YS-CPQ" userLabel="Feature Options">
                            <rect key="frame" x="0.0" y="30" width="230" height="160"/>
                            <subviews>
                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" spacing="2" translatesAutoresizingMaskIntoConstraints="NO" id="69l-nH-292">
                                    <rect key="frame" x="0.0" y="0.0" width="230" height="160"/>
                                    <subviews>
                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="trailing" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="A6s-Uf-8Bu">
                                            <rect key="frame" x="0.0" y="0.0" width="230" height="30.5"/>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <fontDescription key="fontDescription" type="boldSystem" pointSize="17"/>
                                            <state key="normal" title="Button">
                                                <color key="titleColor" red="0.99215686274509807" green="0.80000000000000004" blue="0.33725490196078434" alpha="1" colorSpace="calibratedRGB"/>
                                            </state>
                                            <connections>
                                                <action selector="btnFeatureSetClicked:" destination="-1" eventType="touchUpInside" id="LrA-OK-q0l"/>
                                            </connections>
                                        </button>
                                        <button opaque="NO" tag="1" contentMode="scaleToFill" contentHorizontalAlignment="trailing" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="kGh-fm-lqt">
                                            <rect key="frame" x="0.0" y="32.5" width="230" height="30.5"/>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <fontDescription key="fontDescription" type="boldSystem" pointSize="17"/>
                                            <state key="normal" title="Button">
                                                <color key="titleColor" red="0.99215686270000003" green="0.80000000000000004" blue="0.33725490200000002" alpha="1" colorSpace="calibratedRGB"/>
                                            </state>
                                            <connections>
                                                <action selector="btnFeatureSetClicked:" destination="-1" eventType="touchUpInside" id="Kox-sb-Umi"/>
                                            </connections>
                                        </button>
                                        <button opaque="NO" tag="2" contentMode="scaleToFill" contentHorizontalAlignment="trailing" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="oD8-Da-2gZ">
                                            <rect key="frame" x="0.0" y="65" width="230" height="30"/>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <fontDescription key="fontDescription" type="boldSystem" pointSize="17"/>
                                            <state key="normal" title="Button">
                                                <color key="titleColor" red="0.99215686270000003" green="0.80000000000000004" blue="0.33725490200000002" alpha="1" colorSpace="calibratedRGB"/>
                                            </state>
                                            <connections>
                                                <action selector="btnFeatureSetClicked:" destination="-1" eventType="touchUpInside" id="GuS-yW-94z"/>
                                            </connections>
                                        </button>
                                        <button opaque="NO" tag="3" contentMode="scaleToFill" contentHorizontalAlignment="trailing" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="KUd-Ed-K2g">
                                            <rect key="frame" x="0.0" y="97" width="230" height="30.5"/>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <fontDescription key="fontDescription" type="boldSystem" pointSize="17"/>
                                            <state key="normal" title="Button">
                                                <color key="titleColor" red="0.99215686270000003" green="0.80000000000000004" blue="0.33725490200000002" alpha="1" colorSpace="calibratedRGB"/>
                                            </state>
                                            <connections>
                                                <action selector="btnFeatureSetClicked:" destination="-1" eventType="touchUpInside" id="AF7-Bd-rNO"/>
                                            </connections>
                                        </button>
                                        <button opaque="NO" tag="4" contentMode="scaleToFill" contentHorizontalAlignment="trailing" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="9OC-0e-rGA">
                                            <rect key="frame" x="0.0" y="129.5" width="230" height="30.5"/>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <fontDescription key="fontDescription" type="boldSystem" pointSize="17"/>
                                            <state key="normal" title="Button">
                                                <color key="titleColor" red="0.99215686270000003" green="0.80000000000000004" blue="0.33725490200000002" alpha="1" colorSpace="calibratedRGB"/>
                                            </state>
                                            <connections>
                                                <action selector="btnFeatureSetClicked:" destination="-1" eventType="touchUpInside" id="Bya-4c-mfX"/>
                                            </connections>
                                        </button>
                                    </subviews>
                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                </stackView>
                            </subviews>
                            <color key="backgroundColor" red="0.098039215686274508" green="0.098039215686274508" blue="0.098039215686274508" alpha="0.5" colorSpace="custom" customColorSpace="calibratedRGB"/>
                            <constraints>
                                <constraint firstItem="69l-nH-292" firstAttribute="top" secondItem="SlO-YS-CPQ" secondAttribute="top" id="AZN-RG-kuq"/>
                                <constraint firstAttribute="trailing" secondItem="69l-nH-292" secondAttribute="trailing" id="BPv-86-YyJ"/>
                                <constraint firstAttribute="bottom" secondItem="69l-nH-292" secondAttribute="bottom" id="hb8-DQ-P4e"/>
                                <constraint firstItem="69l-nH-292" firstAttribute="leading" secondItem="SlO-YS-CPQ" secondAttribute="leading" id="oGS-rG-eKW"/>
                            </constraints>
                        </view>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstItem="SlO-YS-CPQ" firstAttribute="leading" secondItem="nlL-TY-yaQ" secondAttribute="leading" id="050-gk-orf"/>
                        <constraint firstAttribute="height" constant="190" id="0Kg-X7-GNY"/>
                        <constraint firstItem="eAR-aA-SYD" firstAttribute="leading" secondItem="nlL-TY-yaQ" secondAttribute="leading" id="98a-S2-h5X"/>
                        <constraint firstItem="bSN-eA-lTy" firstAttribute="top" secondItem="nlL-TY-yaQ" secondAttribute="top" id="AYD-Zg-Cnd"/>
                        <constraint firstItem="SlO-YS-CPQ" firstAttribute="top" secondItem="eAR-aA-SYD" secondAttribute="bottom" id="E7j-eS-lPM"/>
                        <constraint firstItem="eAR-aA-SYD" firstAttribute="top" secondItem="nlL-TY-yaQ" secondAttribute="top" id="Esy-jg-aKw"/>
                        <constraint firstAttribute="width" constant="260" id="L97-JN-tZs"/>
                        <constraint firstAttribute="trailing" secondItem="bSN-eA-lTy" secondAttribute="trailing" id="M30-fY-G3Z"/>
                        <constraint firstAttribute="bottom" secondItem="SlO-YS-CPQ" secondAttribute="bottom" id="YDo-rF-oOj"/>
                        <constraint firstItem="bSN-eA-lTy" firstAttribute="leading" secondItem="eAR-aA-SYD" secondAttribute="trailing" id="uft-Gl-SuK"/>
                        <constraint firstItem="SlO-YS-CPQ" firstAttribute="trailing" secondItem="eAR-aA-SYD" secondAttribute="trailing" id="vw9-ea-Ady"/>
                    </constraints>
                </view>
            </subviews>
            <viewLayoutGuide key="safeArea" id="vUN-kp-3ea"/>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstAttribute="trailing" secondItem="nlL-TY-yaQ" secondAttribute="trailing" id="0sB-0p-VK9"/>
                <constraint firstItem="nlL-TY-yaQ" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" id="d0J-c1-U16"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <point key="canvasLocation" x="-397.10144927536237" y="36.160714285714285"/>
        </view>
    </objects>
    <resources>
        <image name="close" width="15" height="15"/>
        <image name="down" width="20" height="12"/>
    </resources>
</document>
