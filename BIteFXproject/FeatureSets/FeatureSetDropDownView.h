//
//  FeatureSetDropDownView.h
//  BIteFXproject
//
//  Created by indianic on 14/12/21.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

//===================
// Version 3.1 Changes
//===================
// Feature MVI file changes.
// FeatureSetDropDownView is created to show dropdown menu options for FeatureSets...
typedef void(^btnClickBlock)(int featureID);
typedef void(^btnCloseBlock)(void);

@interface FeatureSetDropDownView : UIView <UIGestureRecognizerDelegate>

@property (strong, nonatomic) IBOutlet UIView *viewContent;
@property (strong, nonatomic) IBOutlet UIView *viewDropDown;
@property (strong, nonatomic) IBOutlet UIView *viewFeatureNames;

@property (strong, nonatomic) IBOutlet UILabel *lblSelectedFeatureName;
@property (strong, nonatomic) IBOutlet UIButton *btnDownArrow;
@property (nonatomic, strong) IBOutletCollection(UIButton) NSArray *btnFeatureSetNames;

@property (nonatomic, strong) NSMutableArray *arrFeatureSets;
@property (nonatomic, strong) btnClickBlock btnFeatureSetClickBlock;
@property (nonatomic, strong) btnCloseBlock btnCloseClickBlock;

// IBActions
-(IBAction)btnDownArrowClicked:(UIButton*)sender;
-(IBAction)btnCloseClicked:(UIButton*)sender;
-(IBAction)btnFeatureSetClicked:(UIButton*)sender;

@end

NS_ASSUME_NONNULL_END
