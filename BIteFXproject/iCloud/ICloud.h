//
//  ICloud.h
//  iCloudCheck
//
//  Created by IndiaNIC on 6/20/12.
//  Copyright (c) 2012 __MyCompanyName__. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "iCloudDoc.h"


@protocol ICloudProtocol <NSObject>

@optional
-(void) loadFiles:(NSArray *)arrFileList;
-(void) didLoadData:(NSData *)data forItem:(NSMetadataItem *)item;
-(void) didSaveFile:(NSDictionary *)info;

@end


@interface ICloud : NSObject <iCloudDocProtocol>{
    
    BOOL isICloudAvilable;
    iCloudDoc *cloudDocument;
    NSDictionary *dicInfo;
}

@property (nonatomic,readonly) NSInteger tag;
@property (nonatomic,readonly) BOOL isICloudAvilable;
@property (nonatomic,strong)id <ICloudProtocol> delegate; 

+(ICloud *) shareCloud;

-(void) getAllFileList;
-(void) getFileListWithPredictor:(NSString *)strPredictor;
-(void) createFile:(NSString *)strFileName;
-(void) createFile:(NSString *)strFileName withData:(NSData *)data; 
-(void) openFile:(NSMetadataItem *) item;
-(void) saveFile:(NSMetadataItem *)item withData:(NSData *)data;
-(void) deleteFile:(NSString *)item;
@end
