//
//  iCloudDoc.h
//  LeadRunner
//
//  Created by indianic on 2/10/12.
//  Copyright (c) 2012 __MyCompanyName__. All rights reserved.
//

#import <Foundation/Foundation.h>


@class AppDelegate;

@protocol iCloudDocProtocol <NSObject>

@optional
-(void) fileListDidLoad:(NSArray *)arrFileList;
-(void) fileDidSaved;
//-(void) fileDidLoadWithData:(NSData *)data;
-(void) fileDidLoadWithData:(NSData *)data forItem:(NSMetadataItem *)item;
@end


@interface iCloudDoc : UIDocument{

}
+(iCloudDoc *) shareCloudDoc;

@property (nonatomic,strong) NSString * noteContent;
@property (nonatomic,strong) NSData *dataToSave;
@property (nonatomic,strong) NSMetadataItem *item;
@property (nonatomic,strong) NSDictionary *info;
@property (nonatomic,strong) id <iCloudDocProtocol> delegate;



@end
