//
//  iCloudDoc.m
//  LeadRunner
//
//  Created by indianic on 2/10/12.
//  Copyright (c) 2012 __MyCompanyName__. All rights reserved.
//

#import "iCloudDoc.h"
//#import "ToDoListAppDelegate.h"
#import "AppDelegate.h"

@implementation iCloudDoc
static iCloudDoc *iDoc = nil;
@synthesize noteContent;
@synthesize delegate;
@synthesize dataToSave;
@synthesize info;
@synthesize item;
static NSString *strFileName = @"BITEFXDatabase.sqlite";
+(iCloudDoc *) shareCloudDoc
{
    if(!iDoc)
    {
        NSURL *ubiq = [[NSFileManager defaultManager] 
                       URLForUbiquityContainerIdentifier:nil];
        NSURL *ubiquitousPackage = [[ubiq URLByAppendingPathComponent:
                                     @"Documents"] URLByAppendingPathComponent:strFileName];
        iDoc = [[iCloudDoc alloc] initWithFileURL:ubiquitousPackage];
    }
    return iDoc;
}

// Called whenever the application reads data from the file system
- (BOOL)loadFromContents:(id)contents ofType:(NSString *)typeName error:(NSError **)outError
{
    NSData *data1 = [NSData dataWithBytes:[contents bytes] length:[contents length]]; 
    NSArray  *paths        = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory , NSUserDomainMask, YES);
    NSString *documentsDir = paths[0];
    
    NSString *filePath = [documentsDir stringByAppendingPathComponent:strFileName];
    [data1 writeToFile:filePath atomically:YES];
    NSLog(@"---->>>loadFromContents<<<----");
    [[NSNotificationCenter defaultCenter] postNotificationName:@"ReloadData" object:nil];
    
//    if (appDelegate.intStatusCheckForiCloud == 1)
//    {
//        [appDelegate checkForRestore];
//        
//        if (appDelegate.intStatusCheckForiCloud == 2)
//        {
//            [appDelegate showAlertView];
//        }
//        
//        appDelegate.intStatusCheckForiCloud = 0;
//
//    }
    return YES;
}

// Called whenever the application (auto)saves the content of a note
- (id)contentsForType:(NSString *)typeName error:(NSError **)outError 
{  
   
  // NSLog(@"data size %d for file %@",[dataToSave length],[self fileURL]);
 
    NSArray  *paths        = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory , NSUserDomainMask, YES);
	NSString *documentsDir = paths[0];
    
    NSString *filePath = [documentsDir stringByAppendingPathComponent:strFileName];
    NSData *data1 = [NSData dataWithContentsOfFile:filePath];
    return data1;
    
}




@end
