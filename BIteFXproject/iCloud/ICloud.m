    //
//  ICloud.m
//  iCloudCheck
//
//  Created by IndiaNIC on 6/20/12.
//  Copyright (c) 2012 __MyCompanyName__. All rights reserved.
//

#import "ICloud.h"

@interface ICloud (private)



@end

static ICloud *iCloud = nil;
static NSString *strFileNAme = @"BITEFXDatabase.sqlite";
@implementation ICloud

@synthesize delegate;
@synthesize isICloudAvilable;   
@synthesize tag;
+(ICloud *) shareCloud{
    if (iCloud == nil) {
        iCloud = [[ICloud alloc] init];
    }
    return iCloud;
}

-(instancetype) init{
    
    if (self = [super init]) {

        if ([[NSFileManager defaultManager] respondsToSelector:@selector(URLForUbiquityContainerIdentifier:)]) {
           
            NSURL *ubiq = [[NSFileManager defaultManager] 
                           URLForUbiquityContainerIdentifier:nil];
            isICloudAvilable = TRUE;
            NSLog(@"iCloud access at %@", ubiq);
        }
        else {
            NSLog(@"No iCloud access");
        }
    }
    
    return self;
}

-(void) getAllFileList{
   
    NSString *formatter = [NSString stringWithFormat:@"%@ like '%@*'",NSMetadataItemFSNameKey,strFileNAme];

    [self getFileListWithPredictor:formatter]; 
}


-(void) getFileListWithPredictor:(NSString *)strPredictor{

    NSMetadataQuery *query = [[NSMetadataQuery alloc] init];
    query.searchScopes = @[NSMetadataQueryUbiquitousDocumentsScope];
    NSPredicate *pred = [NSPredicate predicateWithFormat:
                         strPredictor];
    query.predicate = pred;
    
    [[NSNotificationCenter defaultCenter] 
     addObserver:self 
     selector:@selector(queryDidFinishGathering:) 
     name:NSMetadataQueryDidFinishGatheringNotification 
     object:query];
    
    [query startQuery];
    
}

- (void)queryDidFinishGathering:(NSNotification *)notification {
       
    NSMetadataQuery *aQuery = notification.object;
    [aQuery disableUpdates];
    [aQuery stopQuery];
    
    [[NSNotificationCenter defaultCenter] removeObserver:self 
                                                    name:NSMetadataQueryDidFinishGatheringNotification
                                                  object:aQuery];
    

    
	NSArray *arrResult = aQuery.results;
    NSLog(@"%@",arrResult);
    if ([delegate respondsToSelector:@selector(loadFiles:)]) {
        [delegate loadFiles:arrResult];
    }
    
}


-(void) createFile:(NSString *)strFileName{
    
    //NSLog(@"strFileName:%@",strFileName);
    if ([[NSFileManager defaultManager] respondsToSelector:@selector(URLForUbiquityContainerIdentifier:)]) {
//        
//        NSURL *ubiq = [[NSFileManager defaultManager] 
//                       URLForUbiquityContainerIdentifier:nil];
//        NSURL *ubiquitousPackage = [[ubiq URLByAppendingPathComponent:
//                                     @"Documents"] URLByAppendingPathComponent:strFileName];
        
        iCloudDoc *doc = [iCloudDoc shareCloudDoc];
      //  doc.dataToSave = [NSData data];
        [doc saveToURL:doc.fileURL forSaveOperation:UIDocumentSaveForCreating  completionHandler:^(BOOL success) {            
            if (success) {
               // [doc release];
                //[self getAllFileList];
            }
        }];
    }
}

-(void) createFile:(NSString *)strFileName withData:(NSData *)data{

    if ([[NSFileManager defaultManager] respondsToSelector:@selector(URLForUbiquityContainerIdentifier:)]) {
    
//        NSURL *ubiq = [[NSFileManager defaultManager] 
//                       URLForUbiquityContainerIdentifier:nil];
//        NSURL *ubiquitousPackage = [[ubiq URLByAppendingPathComponent:
//                                     @"Documents"] URLByAppendingPathComponent:strFileName];
        
        iCloudDoc *doc = [iCloudDoc shareCloudDoc];
        doc.dataToSave = data;
        [doc saveToURL:doc.fileURL forSaveOperation:UIDocumentSaveForCreating  completionHandler:^(BOOL success) {            
            if (success) {
              //  [doc release];

            }
        }];
    
    }
    
    
} 

-(void) openFile:(NSMetadataItem *) item{
       
   // NSURL *url = [item valueForAttribute:NSMetadataItemURLKey];
   iCloudDoc *doc = [iCloudDoc shareCloudDoc];
    doc.delegate = self;
    doc.item = item;
//    doc.delegate = self;
    
    //NSLog(@"openFile  item %@",[item valueForAttribute:NSMetadataItemFSNameKey]);
    
    [doc openWithCompletionHandler:^(BOOL success) {
        NSLog(@"@---->>>Not Success<<<----@");
       // [doc release];
        if (success) {  
                NSLog(@"---->>>openFile<<<----");
              [[NSNotificationCenter defaultCenter] postNotificationName:@"ReloadData" object:nil];                 
        } else {                
              
        }
    }];

}

-(void) saveFile:(NSMetadataItem *)item withData:(NSData *)data{
    if ([[NSFileManager defaultManager] respondsToSelector:@selector(URLForUbiquityContainerIdentifier:)]) {
    
//        NSURL *ubiq = [[NSFileManager defaultManager] 
//                       URLForUbiquityContainerIdentifier:nil];
//        NSURL *ubiquitousPackage = [[ubiq URLByAppendingPathComponent:
//                                     @"Documents"] URLByAppendingPathComponent:strFileNAme];
        
       iCloudDoc *doc = [iCloudDoc shareCloudDoc];
      //  cloud.dataToSave = data;
        
        [doc saveToURL:doc.fileURL forSaveOperation:UIDocumentSaveForCreating completionHandler:^(BOOL success) {            
            if (success) {
                NSLog(@"---->>>saveFile<<<----");
            //    [[NSNotificationCenter defaultCenter] postNotificationName:@"ReloadData" object:nil];                 
               // [doc release];
                 
            }
        }];
    }
}


-(void) deleteFile:(NSString *)fileName{
    
    NSURL *ubiq = [[NSFileManager defaultManager] URLForUbiquityContainerIdentifier:nil];
    NSURL *ubiquitousPackage = [[ubiq URLByAppendingPathComponent:@"Documents"] URLByAppendingPathComponent:fileName];
    NSFileManager *filemgr = [NSFileManager defaultManager];
    [filemgr removeItemAtURL:ubiquitousPackage error:nil];
    
}


@end
