//
//  AnimationPanelVC.h
//  BIteFXproject
//
//  Created by IndiaNIC Infotech Ltd on 19/12/11.
//  Copyright (c) 2011 __MyCompanyName__. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "AppDelegate.h"
#import "AnimationView.h"
#import "BiteFXMainScreenVC.h"
#import <MobileCoreServices/MobileCoreServices.h>
#import "InfoWebView.h"
#import "VideoPlay.h"
#import "FeatureSetVC.h"
#import "FeatureSetDropDownView.h"
#import "SearchBar.h"


/*
@protocol VideoPlay

- (void)PlayVideoSelected:(id)sender;
- (void)playVideoAfterLockingorRemoving:(id)sender;

@end
*/
@interface AnimationPanelVC : UIViewController<UIGestureRecognizerDelegate,AnimationViewDelegate, UIGestureRecognizerDelegate,UIScrollViewDelegate,UITableViewDataSource,UITableViewDelegate,UICollectionViewDataSource,UICollectionViewDelegate,UIAlertViewDelegate, UIScrollViewDelegate, UIDocumentPickerDelegate, UIImagePickerControllerDelegate, UINavigationControllerDelegate, UICollectionViewDelegateFlowLayout>{

    id <VideoPlay> videoPlayDelegate;    // used for call video protocol methods , set nil from BiteFXMainScreenvc

    BOOL hasSearchResults; 

   // NSMutableArray appdelegate.arrScrollViewCount;
    /*
     viewdidload - comes with count array,
     end of viewdidload - replace int with objects
     viewdidDisappear - assign first object to mutarrPlatvideo
     initwithui method - for change color if isdownload= 1
     uses in collectionview and tableview methods for animation panel
     saving images into collection file master
     clearcolor method
     */
    NSMutableArray *mutArrFilesData;
    /*
     from local db, response objects save in this variable
     */
    NSMutableArray *mutArrAnimationData;
    // response from local db for animation panel data ,  viewdidload
    NSMutableDictionary *mutDictCurrentAnimatedObj;
    // dict of object from local db

    NSMutableDictionary *mutDicSequence;
    // MOVE VIDEO IN user define section
    NSMutableDictionary *mutDicDisplayMvGroupName;
    // collectionview cell ,   used in fetch records for user section collectionview
    NSMutableDictionary *mutDicDisplayMvGroupFav;
    // collectionview cell ,   used in fetch presentation favourite adde records for user section collectionview
    
    NSMutableArray *mutArrDisplayMvGroupFavourite;
    // collectionview cell ,   used in fetch presentation favourite adde records for user section collectionview

    
    NSTimer *timer;
    UIPanGestureRecognizer *panRecognizer;

    NSInteger intSelected;
    NSInteger intSelectedPresentation;
    
    float tb1_previousY;
    float tb2_previousY;

    int cntMovement;
    int gestureCount;
    int intScrollIndex;
    int intSelectedPanl;

    int  x , y ,firstX , firstY , X , Y,pX ;

    BOOL canPan;
    BOOL isAnimationArea;
    BOOL rowRemovedinsequence;
    BOOL iscustomSequenceModified;
    BOOL isAdded;

    CAShapeLayer *circleLayer;
    BOOL animationInProgress;

    UIButton *btnMovie,*btnPicture,*btnPresentation;
    UIView *searchView;
    SearchBar  *searchbarBaseView;
    NSInteger selectedTabIndex;
    NSMutableArray *arrMovie, *arrImage, *arrPresentation, *arrLockImage;
    NSMutableDictionary *dictGropMovie, *dictGropImages, *dictGropPresentation, *dictGropLockImages, *dictGropPresentationFav, *arrMovieFilter, *arrImageFilter, *arrPresentationFilter;
    
    BOOL isCreateNewImageAlbum;
    NSString *strCollectionId;
    int intCurrentRowIndex;
    UIImage *imgImported;
    NSString *strImageName;
    BOOL isEditImageAlbumName;
    InfoWebView *wbView;
    NSInteger prevTabSelected;
    NSMutableArray *mutArrSelectedImages;
    UILabel *lbl;
    
    // Version 3.0 Changes
    // Thumbnail image buttons background image...
    UIImageView *imgViewBgSizeButtons;
    int imgIndex; // This is used to get image assets info
    
    int intAnimationAreaHeight; // Height of the animation panel upper tableview...
    
    //===================
    // Version 3.1 Changes
    //===================
    // Feature MVI file changes.
    UserSubscribeStatus userSubStat;
}

@property (nonatomic) BOOL isLocked;
@property (nonatomic, strong) AnimationView *viewAnimation;
@property (nonatomic, strong) UIView *viewPresentationInfo;
@property (nonatomic, strong) NSMutableDictionary *mutDictCurrentAnimatedObj;
@property (nonatomic, strong) NSMutableDictionary *mutDictCurrPlaylist;
@property (nonatomic, strong)id <VideoPlay> videoPlayDelegate;
@property (nonatomic, strong) IBOutlet UITableView *tblPartAnimationArea;
@property (nonatomic, strong) IBOutlet UITableView *tblPartUserSequences;
@property (nonatomic, strong) IBOutlet UIView *viewPresentationTitle;
@property (nonatomic, strong) IBOutlet UIButton *btnClose;

//===================
// Version 3.1 Changes
//===================
// Feature MVI file changes.
@property (nonatomic, strong) FeatureSetVC *featureSetVC;
@property (nonatomic, strong) FeatureSetDropDownView *viewFeatureSetDropDown;

//@property (nonatomic, strong) UIAlertView *alert;

- (void)btnLockClicked;
- (void)initWithUI;
- (void)tapVideo : (id)sender;
@property (NS_NONATOMIC_IOSONLY, getter=getNoOfCollectionsAndItsContents, readonly, copy) NSMutableArray *noOfCollectionsAndItsContents;
- (void)getNoOfCollectionsAndItsContentsUD;
- (void)setCurrentDictWithColor:(int)aIdColor tempValue:(NSString *)aStrVal;
@property (NS_NONATOMIC_IOSONLY, getter=getDictionaryForCurrentSelectedView, readonly, copy) NSMutableDictionary *dictionaryForCurrentSelectedView;
@property (nonatomic, assign) BOOL hasSearchResults;
@property (nonatomic, assign) NSInteger filteredCount;

- (void)savingImagesIntoCollectionFilesMaster;
- (void)deletePlaylistData;
- (void)clearAllColor;


@end
