//
//  XMLParser.h
//  XML
//
//  Created by iPhone SDK Articles on 11/23/08.
//  Copyright 2008 www.iPhoneSDKArticles.com.
//

#import <UIKit/UIKit.h>
#import "AppDelegate.h"
@class AppDelegate, Book;

@interface XMLParser : NSObject <NSXMLParserDelegate>{

	NSMutableString *currentElementValue;
    
    NSMutableString *strCollectionName;
    
    BOOL boolIsGroupOn;
	int aIntRegistered;
    BOOL isExists;
    NSString *currGroup;
}

- (XMLParser *) initXMLParser NS_DESIGNATED_INITIALIZER;

//- (void)insertFilesInfoWithName:(NSString *)aStrNm DisplayNm:(NSString *)aStrDN shortName:(NSString *)aStrSN;

- (void)insertUpdatedFilesInfoWithName:(NSString *)aStrNm DisplayNm:(NSString *)aStrDN shortName:(NSString *)aStrSN;

- (void)insertImageInfoWithImageName:(NSString *)aStrImNm Name:(NSString *)aStrNm;

- (void)insertUpdatedImageInfoWithImageName:(NSString *)aStrImNm Name:(NSString *)aStrNm;

//- (void)insertCollectionDeatilWithName:(NSString *)aStrCollNm DisplayName:(NSString *)aStrDSNm;

//- (int)getLocalIdFromFileMasterWithTitle:(NSString *)aStrFileTitle;

//- (int)getCollectionIdFromCollectionMaster:(NSString *)aStrCollectionNm;

//- (void)insertInfoIntoCollectionFileMaster:(int)aIntCollid fileId:(int)aIntFileId;

@end
