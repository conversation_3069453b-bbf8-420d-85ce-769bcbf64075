//
//  XMLParserFeature.m
//  BIteFXproject
//
//  Created by indianic on 23/11/21.
//

#import "XMLParserFeature.h"
#import "AppDelegate.h"
#import "Database.h"

@implementation XMLParserFeature

- (XMLParserFeature *) initXMLParserFeature {
    
    if (!(self = [super init])) return nil;
    
    return self;
}

- (void)parser:(NSXMLParser *)parser didStartElement:(NSString *)elementName
  namespaceURI:(NSString *)namespaceURI qualifiedName:(NSString *)qualifiedName
    attributes:(NSDictionary *)attributeDict {
    
    NSLog(@"start Element = %@",elementName);
    
    if([elementName isEqualToString:@"BITEFX"]) {
        
    }
    else if ([elementName isEqualToString:@"FEATURES"])
    {
        
    }
    else if ([elementName isEqualToString:@"FEATURE"])
    {
        if(!strFeatureName)
            strFeatureName = [[NSMutableString alloc] initWithString:[attributeDict valueForKey:@"NAME"]];
        else
            [strFeatureName appendString:[attributeDict valueForKey:@"NAME"]];
        
        NSLog(@"Feature Name Value: %@", strFeatureName);
        
        [self insertFeatureDeatilWithName:attributeDict[@"NAME"] imagePath:attributeDict[@"PREVIEW"]];
    }
    else if ([elementName isEqualToString:@"PRESENTATION"])
    {
        int aIntFeatureId = [self getFeatureIdFromFeatureMaster:strFeatureName];
        if (aIntFeatureId != -1) {
            [self insertFeatureIdInCollectionMaster:aIntFeatureId collectionName:attributeDict[@"NAME"]];
        }
    }
    
}

- (void)parser:(NSXMLParser *)parser foundCharacters:(NSString *)string {
    if(!currentElementValue)
        currentElementValue = [[NSMutableString alloc] initWithString:string];
    else
        [currentElementValue appendString:string];

}

- (void)parser:(NSXMLParser *)parser didEndElement:(NSString *)elementName
  namespaceURI:(NSString *)namespaceURI qualifiedName:(NSString *)qName {
    
    if([elementName isEqualToString:@"BITEFX"]) {
        NSLog(@"parsing is completed ");
    }
    else if([elementName isEqualToString:@"FEATURES"]) {
        NSLog(@"data is inserted");
    }
    else if([elementName isEqualToString:@"FEATURE"])
    {
        strFeatureName = nil;
    }
    else if([elementName isEqualToString:@"PRESENTATION"])
    {
        
    }
    else ;
    
    currentElementValue = nil;
}



#pragma mark - Database Methods

#pragma mark Insert Feature Info Details

- (void)insertFeatureDeatilWithName:(NSString *)aStrName imagePath:(NSString*)aStrImagePath
{
    // Replace "\" with "/" in imagepath...
    aStrImagePath = [aStrImagePath stringByReplacingOccurrencesOfString:@"\\" withString:@"/"];
    
    NSString *aStrFeatureExist = [NSString stringWithFormat:@"select count(featureID) from FeatureMaster where featureName = \'%@\'", aStrName];
    
    NSMutableArray *aMutArrExist = [[Database sharedDatabase] getAllDataForQuery:aStrFeatureExist];
    
    if(aMutArrExist.count > 0)
    {
        if ([aMutArrExist[0][@"count(featureID)"] intValue] == 0)
        {
            NSString *aStrInsertInfo = [NSString stringWithFormat:@"insert into FeatureMaster ('featureName', 'imageFilePath') values (\"%@\", \"%@\")",aStrName, aStrImagePath];
            
            [[Database sharedDatabase] Insert:aStrInsertInfo];
        }
    }
}

- (int)getFeatureIdFromFeatureMaster:(NSString *)aStrFeatureName
{
    
    int aIntResult;
    
    NSString *aStrGetId = [NSString stringWithFormat:@"select featureID from FeatureMaster where featureName = \'%@\'",aStrFeatureName];
    
    NSMutableArray *aMutArrResult = [[Database sharedDatabase] getAllDataForQuery:aStrGetId];

    if (aMutArrResult.count > 0) {
        aIntResult = [aMutArrResult[0][@"featureID"] intValue];
    } else {
        aIntResult = -1;
    }
    
    return aIntResult;
}


#pragma mark Insert FeatureID Info Into CollectionMaster

- (void)insertFeatureIdInCollectionMaster:(int)aIntFeatureId collectionName:(NSString *)aStrCollectionName
{
    NSString *aStrUpdateQuery = [NSString stringWithFormat:@"update CollectionMaster set featureID = %d where collectionName = \"%@\"", aIntFeatureId, aStrCollectionName];
    [[Database sharedDatabase] Update:aStrUpdateQuery];
}

@end

