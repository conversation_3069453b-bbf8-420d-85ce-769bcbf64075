 //
//  XMLParser.m
//  XML
//
//  Created by iPhone SDK Articles on 11/23/08.
//  Copyright 2008 www.iPhoneSDKArticles.com.
//

#import "XMLParser.h"
#import "AppDelegate.h"
#import "Database.h"

@implementation XMLParser

- (XMLParser *) initXMLParser {
	
	if (!(self = [super init])) return nil;

    //aIntRegistered=0;
	
    boolIsGroupOn = FALSE;
	currGroup = @"";
	return self;
}

- (void)parser:(NSXMLParser *)parser didStartElement:(NSString *)elementName 
  namespaceURI:(NSString *)namespaceURI qualifiedName:(NSString *)qualifiedName 
	attributes:(NSDictionary *)attributeDict {
    
    //NSLog(@"start Element = %@",elementName);
	
	if([elementName isEqualToString:@"BITEFX"]) {
		//Initialize the array.
	}
	else if([elementName isEqualToString:@"MOVIES"]) 
    {
		
//		NSLog(@"Movies Tag");
	}
    else if([elementName isEqualToString:@"MOVIE"])
    {
        if (boolIsGroupOn)
        {
            if(!appDelegate.isUpdatedMVIfile)
            {
                NSString *aStrName=[NSString stringWithFormat:@"%@",attributeDict[@"NAME"]];
               // NSLog(@"%@",aStrName);
                [self insertImageInfoWithImageName:aStrName Name:aStrName];
            }
            else {
                [self insertUpdatedImageInfoWithImageName:attributeDict[@"NAME"] Name:attributeDict[@"NAME"]];
            }
            int aIntCollId = [self getCollectionIdFromCollectionMaster:strCollectionName withType:mediaMovie];
            int aIntFileId = [self getLocalIdFromFileMasterWithTitle:attributeDict[@"NAME"] withtype:mediaMovie];
            if (aIntFileId != -1) {
                [self insertInfoIntoCollectionFileMaster:aIntCollId fileId:aIntFileId withType:mediaMovie];
            }
        }
        else
        {
            if(appDelegate.isUpdatedMVIfile==FALSE)
            {
                [self insertFilesInfoWithName:attributeDict[@"NAME"] DisplayNm:attributeDict[@"DISPLAYNAME"] shortName:attributeDict[@"SHORTDISPLAYNAME"] filetype:mediaMovie];
            }
            else {
                [self insertFilesInfoWithName:attributeDict[@"NAME"] DisplayNm:attributeDict[@"DISPLAYNAME"] shortName:attributeDict[@"SHORTDISPLAYNAME"] filetype:mediaMovie];
            }
        }
    }
    else if ([elementName isEqualToString:@"MOVIEGROUPS"])
    {
        boolIsGroupOn = TRUE;
        currGroup = mediaMovie;
    }
    else if ([elementName isEqualToString:@"MOVIEGROUP"])
    {
        currGroup = mediaMovie;
        if(!strCollectionName) 
            strCollectionName = [[NSMutableString alloc] initWithString:[attributeDict valueForKey:@"NAME"]];
        else
            [strCollectionName appendString:[attributeDict valueForKey:@"NAME"]];
        
       // NSLog(@"Collection Name Value: %@", strCollectionName);
        
        
        [self insertCollectionDeatilWithName:attributeDict[@"NAME"] DisplayName:attributeDict[@"DISPLAYNAME"] withFiletype:mediaMovie];
    }
    else if([elementName isEqualToString:@"IMAGES"])
    {

        //		NSLog(@"Movies Tag");
    }
    else if([elementName isEqualToString:@"IMAGE"])
    {
        if (boolIsGroupOn)
        {
            if(!appDelegate.isUpdatedMVIfile)
            {
                NSString *aStrName=[NSString stringWithFormat:@"%@",attributeDict[@"NAME"]];
                // NSLog(@"%@",aStrName);
                [self insertImageInfoWithImageName:aStrName Name:aStrName];
            }
            else {
                [self insertUpdatedImageInfoWithImageName:attributeDict[@"NAME"] Name:attributeDict[@"NAME"]];
            }
            int aIntCollId = [self getCollectionIdFromCollectionMaster:strCollectionName withType:mediaImage];
            int aIntFileId = [self getLocalIdFromFileMasterWithTitle:attributeDict[@"NAME"] withtype:mediaImage];
            if (aIntFileId != -1) {
                [self insertInfoIntoCollectionFileMaster:aIntCollId fileId:aIntFileId withType:mediaImage];
            }
        }
        else
        {
            if(appDelegate.isUpdatedMVIfile==FALSE)
            {
                [self insertFilesInfoWithName:attributeDict[@"NAME"] DisplayNm:attributeDict[@"DISPLAYNAME"] shortName:attributeDict[@"SHORTDISPLAYNAME"] filetype:mediaImage];
            }
            else {
                [self insertFilesInfoWithName:attributeDict[@"NAME"] DisplayNm:attributeDict[@"DISPLAYNAME"] shortName:attributeDict[@"SHORTDISPLAYNAME"] filetype:mediaImage];
            }
        }
    }
    else if ([elementName isEqualToString:@"IMAGEGROUPS"])
    {
        boolIsGroupOn = TRUE;
        currGroup = mediaImage;
    }
    else if ([elementName isEqualToString:@"IMAGEGROUP"])
    {
        currGroup = mediaImage;
        if(!strCollectionName)
            strCollectionName = [[NSMutableString alloc] initWithString:[attributeDict valueForKey:@"NAME"]];
        else
            [strCollectionName appendString:[attributeDict valueForKey:@"NAME"]];

        // NSLog(@"Collection Name Value: %@", strCollectionName);


        [self insertCollectionDeatilWithName:attributeDict[@"NAME"] DisplayName:attributeDict[@"DISPLAYNAME"] withFiletype:mediaImage];
    }
    else if ([elementName isEqualToString:@"PRESENTATIONS"])
    {
        boolIsGroupOn = TRUE;
        currGroup = mediaPresentation;
    }
    else if ([elementName isEqualToString:@"PRESENTATION"])
    {
        currGroup = mediaPresentation;
        if(!strCollectionName)
            strCollectionName = [[NSMutableString alloc] initWithString:[attributeDict valueForKey:@"NAME"]];
        else
            [strCollectionName appendString:[attributeDict valueForKey:@"NAME"]];

        // NSLog(@"Collection Name Value: %@", strCollectionName);


        [self insertCollectionDeatilWithName:attributeDict[@"NAME"] DisplayName:attributeDict[@"DISPLAYNAME"] withFiletype:mediaPresentation];
    }

}

- (void)parser:(NSXMLParser *)parser foundCharacters:(NSString *)string { 
	
	if(!currentElementValue)
		currentElementValue = [[NSMutableString alloc] initWithString:string];
	else
		[currentElementValue appendString:string];

}

- (void)parser:(NSXMLParser *)parser didEndElement:(NSString *)elementName 
  namespaceURI:(NSString *)namespaceURI qualifiedName:(NSString *)qName {
    elementName = elementName.uppercaseString;
    
	if([elementName isEqualToString:@"BITEFX"]) {
		//Initialize the array.
        NSLog(@"parsing is completed ");
	}
	else if([elementName isEqualToString:@"MOVIES"]) {
		NSLog(@"data is inserted ");
    }
    else if([elementName isEqualToString:@"MOVIE"])
    {
        
    }
    else if ([elementName isEqualToString:@"MOVIEGROUPS"])
    {
        boolIsGroupOn = FALSE;
        currGroup = @"";
    }
    else if ([elementName isEqualToString:@"MOVIEGROUP"])
    {
        strCollectionName = nil;
    }
    else if ([elementName isEqualToString:@"IMAGEGROUPS"])
    {
        boolIsGroupOn = FALSE;
        currGroup = @"";
    }
    else if ([elementName isEqualToString:@"IMAGEGROUP"])
    {
        strCollectionName = nil;
    }
    else if ([elementName isEqualToString:@"PRESENTATIONS"])
    {
        boolIsGroupOn = FALSE;
        currGroup = @"";
    }
    else if ([elementName isEqualToString:@"PRESENTATION"])
    {
        strCollectionName = nil;
    }
	else 
	
	;
	currentElementValue = nil;
}



#pragma mark - Database Methods

#pragma mark Insert Info file into LocalFileMaster Methods

- (void)insertFilesInfoWithName:(NSString *)aStrNm DisplayNm:(NSString *)aStrDN shortName:(NSString *)aStrSN filetype:(NSString*)fileType{
    NSString* isType = @"";
    if([fileType isEqualToString:mediaMovie])
    {
        isType = VIDEO_EXTENSION;
    }
    else
    {
        isType = @"jpg";
    }
    aStrSN = [aStrSN stringByReplacingOccurrencesOfString:@"'" withString:@" "];
    
    isExists = TRUE;
    NSString *aStrExist = [NSString stringWithFormat:@"select count(fileTitle) from LocalFileMaster where fileTitle = \'%@.%@\'",aStrNm,isType];
    
    NSMutableArray *aMutArrExist = [[Database sharedDatabase] getAllDataForQuery:aStrExist];
//    NSLog(@"aMutArrExist=%@",aMutArrExist);
    NSString *aStrFilePath;
    NSString *aStrURLForVideo,*aStrURLForJpg,*aStrURLForHtml;
    
    NSString *aStrFilePathForHtml;
    aIntRegistered = [[NSUserDefaults standardUserDefaults]boolForKey:@"Registered"];

    if(aIntRegistered) {
        
        #pragma mark --------------------------------
        #pragma mark Version 2.4 Folder Structure Changes

        if (!appDelegate.isUpdatedMVIfile) {
           // NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES);
           // NSString *documentsDir = [paths objectAtIndex:0];
            
           // NSString *dir_path= [documentsDir stringByAppendingPathComponent:@"BiteFXiPadFull"];
            if([fileType isEqualToString:mediaMovie]) {
                
                aStrURLForVideo = [NSString stringWithFormat:@"%@/%@/%@.%@", BITEFXV2, MOVIES, aStrNm, VIDEO_EXTENSION];
                aStrURLForJpg = [NSString stringWithFormat:@"%@/%@/%@/%@.jpg", BITEFXV2, THUMBNAILS, MOVIES, aStrNm];
                aStrURLForHtml = [NSString stringWithFormat:@"%@/%@/%@.html", BITEFXV2, MOVIES, aStrNm];
                
            }
            else
            {
                aStrURLForVideo = [NSString stringWithFormat:@"%@/%@/%@.jpg", BITEFXV2, IMAGES, aStrNm];
                aStrURLForJpg = [NSString stringWithFormat:@"%@/%@/%@/%@.jpg", BITEFXV2, THUMBNAILS, IMAGES, aStrNm];
            }
//            aStrURLForVideo = [NSString stringWithFormat:@"BiteFXiPadFull/%@.%@",aStrNm,isType];
//            aStrURLForJpg = [NSString stringWithFormat:@"BiteFXiPadFull/%@.jpg",aStrNm];
//            aStrURLForHtml = [NSString stringWithFormat:@"BiteFXiPadFull/%@.html",aStrNm];
        
        } else {
            
            aStrURLForVideo = [NSString stringWithFormat:@"Update0001/%@.%@",aStrNm,isType];
            aStrURLForJpg = [NSString stringWithFormat:@"Update0001/%@.jpg",aStrNm];
            aStrURLForHtml = [NSString stringWithFormat:@"Update0001/%@.html",aStrNm];
        }

        NSString *sql = [NSString stringWithFormat:@"select count (fileTitle) from LocalFileMaster where fileTitle = '%@.%@'",aStrNm,isType];
        NSInteger count = [[Database shareDatabase] getCount:sql];
        
        if([fileType isEqualToString:mediaMovie]) {
            
            // Check for all three files...
            if ([[NSFileManager defaultManager] fileExistsAtPath:[NSString stringWithFormat:@"%@/%@", DOCUMENT_DIRECTORY_PATH, aStrURLForHtml]] && [[NSFileManager defaultManager] fileExistsAtPath:[NSString stringWithFormat:@"%@/%@", DOCUMENT_DIRECTORY_PATH,aStrURLForJpg]] && [[NSFileManager defaultManager] fileExistsAtPath:[NSString stringWithFormat:@"%@/%@", DOCUMENT_DIRECTORY_PATH,aStrURLForVideo]] && count == 0) {
                
                NSString *aStrIsPlayed = @"1";
                if (appDelegate.isUpdatedMVIfile) {
                    aStrIsPlayed = @"0";
                }
                
                NSString *aStrInsertQuery = [NSString stringWithFormat:@"insert into LocalFileMaster ('fileTitle', 'fileName', 'fileShortDispName', 'isDownloaded', 'isPlayed', 'filePath', 'infoFilePath','thumbnailPath','fileType') values (\'%@.%@\', \"%@\", \"%@\", '0', '%@', \'%@\', \'%@\', \'%@\', \'%@\')",aStrNm,isType,aStrDN,aStrSN, aStrIsPlayed,aStrURLForVideo,aStrURLForHtml,aStrURLForJpg,fileType];
                [[Database sharedDatabase] Insert:aStrInsertQuery];

            }
            else if ([[NSFileManager defaultManager] fileExistsAtPath:[NSString stringWithFormat:@"%@/%@", DOCUMENT_DIRECTORY_PATH, aStrURLForHtml]] && [[NSFileManager defaultManager] fileExistsAtPath:[NSString stringWithFormat:@"%@/%@", DOCUMENT_DIRECTORY_PATH,aStrURLForJpg]] && [[NSFileManager defaultManager] fileExistsAtPath:[NSString stringWithFormat:@"%@/%@", DOCUMENT_DIRECTORY_PATH,aStrURLForVideo]] && count == 1) {
//            else if (count == 1 && appDelegate.isUpdatedMVIfile) {
                
                NSString *aStrIsPlayed = @"1";
                if (appDelegate.isUpdatedMVIfile) {
                    aStrIsPlayed = @"0";
                }
                NSString *aStrUpdateQuery = [NSString stringWithFormat:@"update LocalFileMaster set fileName = \"%@\", fileShortDispName = \"%@\", filePath = '%@', infoFilePath = '%@',thumbnailPath = '%@',isPlayed = '%@' where fileTitle ='%@.%@'",aStrDN,aStrSN,aStrURLForVideo,aStrURLForHtml,aStrURLForJpg,aStrIsPlayed,aStrNm,isType];
                [[Database sharedDatabase] Update:aStrUpdateQuery];
            }
        }
        else
        {
            // Check for all two files...
           if ([[NSFileManager defaultManager] fileExistsAtPath:[NSString stringWithFormat:@"%@/%@", DOCUMENT_DIRECTORY_PATH,aStrURLForJpg]] && [[NSFileManager defaultManager] fileExistsAtPath:[NSString stringWithFormat:@"%@/%@", DOCUMENT_DIRECTORY_PATH,aStrURLForVideo]] && count == 0) {
                
                NSString *aStrIsPlayed = @"1";
                if (appDelegate.isUpdatedMVIfile) {
                    aStrIsPlayed = @"0";
                }
                
                NSString *aStrInsertQuery = [NSString stringWithFormat:@"insert into LocalFileMaster ('fileTitle', 'fileName', 'fileShortDispName', 'isDownloaded', 'isPlayed', 'filePath', 'infoFilePath','thumbnailPath','fileType') values (\'%@.%@\', \'%@\', \'%@\', '0', '%@', \'%@\', \'%@\', \'%@\', \'%@\')",aStrNm,isType,aStrDN,aStrSN, aStrIsPlayed,aStrURLForVideo,@"",aStrURLForJpg,fileType];
                [[Database sharedDatabase] Insert:aStrInsertQuery];
                
           }
           else if ([[NSFileManager defaultManager] fileExistsAtPath:[NSString stringWithFormat:@"%@/%@", DOCUMENT_DIRECTORY_PATH,aStrURLForJpg]] && [[NSFileManager defaultManager] fileExistsAtPath:[NSString stringWithFormat:@"%@/%@", DOCUMENT_DIRECTORY_PATH,aStrURLForVideo]] && count == 1) {
//           else if (count == 1 && appDelegate.isUpdatedMVIfile) {
        
               NSString *aStrIsPlayed = @"1";
               if (appDelegate.isUpdatedMVIfile) {
                   aStrIsPlayed = @"0";
               }

               NSString *aStrUpdateQuery = [NSString stringWithFormat:@"update LocalFileMaster set fileName = \"%@\", fileShortDispName = \"%@\",  filePath = '%@',thumbnailPath = '%@',isPlayed = '%@' where fileTitle ='%@.%@'",aStrDN,aStrSN,aStrURLForVideo,aStrURLForJpg,aStrIsPlayed,aStrNm,isType];
               [[Database sharedDatabase] Update:aStrUpdateQuery];
               
           }

        }
        
    }else  {
      
//       aStrFilePath = [NSString stringWithFormat:@"%@",[[NSBundle mainBundle]
//                                                         pathForResource:aStrNm ofType:@"mov"]];
//       aStrFilePathForHtml = [NSString stringWithFormat:@"%@",[[NSBundle mainBundle] 
//                                                                          pathForResource:aStrNm ofType:@"html"]];

        aStrFilePath = aStrNm;
        aStrFilePathForHtml = aStrNm;

        if(aMutArrExist.count > 0)
        {
            if ([aMutArrExist[0][@"count(fileTitle)"] intValue] == 0) {

                NSString *aStrInsertQuery = [NSString stringWithFormat:@"insert into LocalFileMaster ('fileTitle', 'fileName', 'fileShortDispName', 'isDownloaded', 'isPlayed', 'filePath', 'infoFilePath','thumbnailPath','fileType') values (\'%@.%@\', \"%@\", \"%@\", '0', '1', \'%@\', \'%@\', \'%@\', \'%@\')",aStrNm,isType,aStrDN,aStrSN,aStrFilePath,aStrFilePathForHtml,aStrFilePath,fileType];
                [[Database sharedDatabase] Insert:aStrInsertQuery];
            }else{
            
                NSString *aStrUpdateQuery = [NSString stringWithFormat:@"update LocalFileMaster SET infoFilePath = '%@' where fileTitle = '%@.%@'",aStrFilePathForHtml,aStrNm,isType];
                [[Database sharedDatabase]Update:aStrUpdateQuery];
                
            }
        }
    }
    // ---
}


#pragma mark ---
#pragma mark NewupdatedFile Insertion

-(void)insertUpdatedFilesInfoWithName:(NSString *)aStrNm DisplayNm:(NSString *)aStrDN shortName:(NSString *)aStrSN
{
    aStrSN = [aStrSN stringByReplacingOccurrencesOfString:@"'" withString:@" "];
    
    NSString *aStrExist = [NSString stringWithFormat:@"select count(fileTitle) from LocalFileMaster where fileTitle = \'%@.%@\'",aStrNm, VIDEO_EXTENSION];
    
    NSMutableArray *aMutArrExist = [[Database sharedDatabase] getAllDataForQuery:aStrExist];
    
    // --- Getting Path for file
    
//    NSArray *aArrpaths = NSSearchPathForDirectoriesInDomains(NSCachesDirectory, NSUserDomainMask, YES);
//    
//    NSString *aStrCacheDir = [aArrpaths objectAtIndex:0];
//    
//    NSString *aStrFilePath = [aStrCacheDir stringByAppendingPathComponent:[NSString stringWithFormat:@"%@.mov",aStrNm]];
    
   // NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES);
   // NSString *documentsDir = [paths objectAtIndex:0];
    
   // NSString *documentsDir= [NSHomeDirectory() stringByAppendingPathComponent:@"Library"];
    
    //    NSString *librarry = [documentsDir 
  //  NSString *dir_path= [documentsDir stringByAppendingPathComponent:@"Update0001"];
    // ---
    NSString *aStrFilePath = [NSString stringWithFormat:@"Update0001/%@.%@",aStrNm, VIDEO_EXTENSION];
    
    NSString *aStrFilePathForHtml =[NSString stringWithFormat:@"Update0001/%@.html",aStrNm];
;
    
    NSString *aStrimage = [NSString stringWithFormat:@"Update0001/%@.jpg",aStrNm];
//
 //   NSString *aStrFilePathForHtml = [NSString stringWithFormat:@"%@",[[NSBundle mainBundle] 
   //                                                              pathForResource:aStrNm ofType:@"html"]];
//    
    //NSString *aStrimage = [NSString stringWithFormat:@"%@",[[NSBundle mainBundle] 
      //                                                      pathForResource:aStrNm ofType:@"jpg"]];

//;

    //Changes in mov files

    if ([aMutArrExist[0][@"count(fileTitle)"] intValue] >=0) {
        
        if (appDelegate.isUpdatedMVIfile) {
            
            NSString *aStrInsertQuery = [NSString stringWithFormat:@"insert into LocalFileMaster ('fileTitle', 'fileName', 'fileShortDispName', 'isDownloaded', 'isPlayed', 'filePath', 'infoFilePath','thumbnailPath') values (\'%@.%@\', \"%@\", \"%@\", '1', '0', \'%@\', \'%@\',\'%@\')",aStrNm, VIDEO_EXTENSION ,aStrDN,aStrSN,aStrFilePath,aStrFilePathForHtml,aStrimage];
            
            [[Database sharedDatabase] Insert:aStrInsertQuery];
            
        } else {
            
            NSString *aStrInsertQuery = [NSString stringWithFormat:@"insert into LocalFileMaster ('fileTitle', 'fileName', 'fileShortDispName', 'isDownloaded', 'isPlayed', 'filePath', 'infoFilePath','thumbnailPath') values (\'%@.%@\', \"%@\", \"%@\", '1', '1', \'%@\', \'%@\',\'%@\')",aStrNm, VIDEO_EXTENSION, aStrDN,aStrSN,aStrFilePath,aStrFilePathForHtml,aStrimage];
            
            [[Database sharedDatabase] Insert:aStrInsertQuery];
        }
    }
}

- (void)insertImageInfoWithImageName:(NSString *)aStrImNm Name:(NSString *)aStrNm {
   
    // --- Getting Path for Image
    NSString *aStrImagePath=@"";
    
    aIntRegistered = [[NSUserDefaults standardUserDefaults]boolForKey:@"Registered"];
    if(aIntRegistered) {
       
        NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES);
        NSString *documentsDir = paths[0];

        // NSString *documentsDir= [NSHomeDirectory() stringByAppendingPathComponent:@"Library"];
        
        //    NSString *librarry = [documentsDir 
     //   NSString *dir_path= [documentsDir stringByAppendingPathComponent:@"BiteFXiPadFull"];
        // ---
        aStrImagePath =[NSString stringWithFormat:@"BiteFXiPadFull/%@",[NSString stringWithFormat:@"%@.jpg",aStrNm]];
       // aStrImagePath = [NSString stringWithFormat:@"%@",[[NSBundle mainBundle] 
          //                                                pathForResource:aStrNm ofType:@"jpg"]];
        
          if([[NSFileManager defaultManager]fileExistsAtPath:[NSString stringWithFormat:@"%@/%@",documentsDir,aStrImagePath]]) {
              
              isExists=TRUE;
          
          } else {
              
              isExists = FALSE;
          }
        
    } else  {
        
        /*NSArray *aArrpaths = NSSearchPathForDirectoriesInDomains(NSCachesDirectory, NSUserDomainMask, YES);
        
        NSString *aStrCacheDir = [aArrpaths objectAtIndex:0];
        aStrImagePath = [aStrCacheDir stringByAppendingPathComponent:[NSString stringWithFormat:@"%@.jpg",aStrImNm]];*/
        
        aStrImagePath = [NSString stringWithFormat:@"%@",aStrNm];
        
        NSLog(@"aStrImagePath=%@",aStrImagePath);
      /*  NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES);
        NSString *documentsDir = [paths objectAtIndex:0];
      //  NSString *documentsDir= [NSHomeDirectory() stringByAppendingPathComponent:@"Library"];
        
        //NSString *librarry = [documentsDir 
        NSString *dir_path= [documentsDir stringByAppendingPathComponent:@"BiteFXiPadFull"];
        aStrImagePath = [dir_path stringByAppendingPathComponent:[NSString stringWithFormat:@"%@.jpg",aStrImNm]];
        NSFileManager *manager = [NSFileManager defaultManager];
        isExists = [manager fileExistsAtPath:aStrImagePath];*/
    }
    
    // ---
    
    if(aIntRegistered)
    {
        if(isExists)
        {
            NSString *aStrUpdateImage = [NSString stringWithFormat:@"update LocalFileMaster set thumbnailPath = \'%@\' where fileTitle = \'%@.%@\'",aStrImagePath,aStrNm, VIDEO_EXTENSION];
            //NSLog(@"insert 8");
            [[Database sharedDatabase] Insert:aStrUpdateImage];
        }
    }
    else {
        NSString *aStrUpdateImage = [NSString stringWithFormat:@"update LocalFileMaster set thumbnailPath = \'%@\' where fileTitle = \'%@.%@\'",aStrImagePath,aStrNm, VIDEO_EXTENSION];
        
        [[Database sharedDatabase] Insert:aStrUpdateImage];
    }

}

- (void)insertUpdatedImageInfoWithImageName:(NSString *)aStrImNm Name:(NSString *)aStrNm; {
    
   // NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES);
    //NSString *documentsDir = [paths objectAtIndex:0];
    //NSString *documentsDir= [NSHomeDirectory() stringByAppendingPathComponent:@"Library"];
    
    //    NSString *librarry = [documentsDir 
   // NSString *dir_path= [documentsDir stringByAppendingPathComponent:@"Update0001"];
    
//    NSString *aStrImagePath = [NSString stringWithFormat:@"Update0001/%@.jpg",aStrNm];

//    NSString *aStrImagePath =  [NSString stringWithFormat:@"%@",[[NSBundle mainBundle] 
//                                                                 pathForResource:aStrNm ofType:@"jpg"]];   
    // --- 001
    
   // NSString *aStrUpdateImage = [NSString stringWithFormat:@"update LocalFileMaster set thumbnailPath = \'%@\' where fileTitle = \'%@.mov\'",aStrImagePath,aStrNm];
    
   // [[Database sharedDatabase] Insert:aStrUpdateImage];
    
}

#pragma mark Insert Collection Info and its detail file Methods

- (void)insertCollectionDeatilWithName:(NSString *)aStrCollNm DisplayName:(NSString *)aStrDSNm withFiletype:(NSString*)fileType
{
    NSString *aStrCollectionExist = [NSString stringWithFormat:@"select count(collectionID) from CollectionMaster where collectionName = \'%@\'",aStrCollNm];
    
    // Version 2.4 changes
    // If "DisplayName" not given, use "Name" as "DisplayName"
    if ((aStrDSNm == nil) || ([aStrDSNm isEqualToString:@""])) {
        aStrDSNm = aStrCollNm;
    }
    
    NSString *aStrInfoPath = @"";
    
    if([fileType isEqualToString:mediaPresentation]) {
        
        if (appDelegate.isUpdatedMVIfile) {
            
            // For updates(When new presentation is added in Updates)...
            aStrInfoPath = [NSString stringWithFormat:@"Update0001/%@.html", aStrCollNm];
        }
        else
        {
            aStrInfoPath = [NSString stringWithFormat:@"%@/%@/%@.html", BITEFXV2, PRESENTATIONS, aStrCollNm];
        }
        
    }
    
    NSMutableArray *aMutArrExist = [[Database sharedDatabase] getAllDataForQuery:aStrCollectionExist];
    
    if(aMutArrExist.count > 0)
    {
        if ([aMutArrExist[0][@"count(collectionID)"] intValue] == 0)
        {
            NSString *aStrInsertInfo = [NSString stringWithFormat:@"insert into CollectionMaster ('collectionName', 'collectionDisplayName', 'fileType', 'isUserDefine', 'infoFilePath') values (\"%@\", \"%@\", \'%@\', '0', \'%@\')",aStrCollNm, aStrDSNm, fileType, aStrInfoPath];
            
            [[Database sharedDatabase] Insert:aStrInsertInfo];
        }
    }
}

#pragma mark Get Collection and File Ids Methods

- (int)getLocalIdFromFileMasterWithTitle:(NSString *)aStrFileTitle withtype:(NSString*)fileType
{
    int aIntResult;
    NSString *mediaType = @"";
    if ([fileType isEqualToString:mediaMovie])
    {
        mediaType = VIDEO_EXTENSION;
    }
    else
    {
        mediaType = @"jpg";
    }
    NSString *aStrGetId = [NSString stringWithFormat:@"select fileID from LocalFileMaster where fileTitle = \'%@.%@\'",aStrFileTitle,mediaType];

    
    
    NSMutableArray *aMutArrResult = [[Database sharedDatabase] getAllDataForQuery:aStrGetId];
    if (aMutArrResult.count>0) {
        aIntResult = [aMutArrResult[0][@"fileID"] intValue];
    }
    else{
        aIntResult = -1;
    }
    
    
    return aIntResult;
}

- (int)getCollectionIdFromCollectionMaster:(NSString *)aStrCollectionNm withType:(NSString*)fileType
{
    
    int aIntResult;

    if([currGroup  isEqual: mediaPresentation] && ![currGroup  isEqual: @""])
    {
        fileType = mediaPresentation;
    }
    
    NSString *aStrGetId = [NSString stringWithFormat:@"select collectionID from CollectionMaster where collectionName = \'%@\' and fileType = \'%@\'",aStrCollectionNm,fileType];
    
    NSMutableArray *aMutArrResult = [[Database sharedDatabase] getAllDataForQuery:aStrGetId];

    if (aMutArrResult.count > 0)
    {
        aIntResult = [aMutArrResult[0][@"collectionID"] intValue];
    }
    else
    {
        aIntResult = -1;
    }

//    aIntResult = [[[aMutArrResult objectAtIndex:0] objectForKey:@"collectionID"] intValue];

//    if(aIntResult>6)
//    {
//        aIntResult=6;
//    }
    return aIntResult;
}

#pragma mark Insert collectionFiles Info Into Products

- (void)insertInfoIntoCollectionFileMaster:(int)aIntCollid fileId:(int)aIntFileId withType:(NSString*)fileType
{
    if([currGroup  isEqual: mediaPresentation] && ![currGroup  isEqual: @""])
    {
        fileType = mediaPresentation;
    }
    NSString *aStrExists = [NSString stringWithFormat:@"select count(filesID) from CollectionFilesMaster where collectionID = %d AND filesID = \'%d\'",aIntCollid,aIntFileId];
  
    NSString *getData = [NSString stringWithFormat:@"select * from CollectionFilesMaster where collectionID = %d AND filesID = %d",aIntCollid, aIntFileId];
    NSMutableArray *aMutarrVal = [[Database sharedDatabase]getAllDataForQuery:getData];

    NSMutableArray *aMutArrExists = [[Database sharedDatabase] getAllDataForQuery:aStrExists];
    
    if(aMutArrExists.count > 0)
    {
        if ([aMutArrExists[0][@"count(filesID)"] intValue] == 0)
        {
            NSString *aStrInsert = [NSString stringWithFormat:@"insert into CollectionFilesMaster ('collectionID', 'filesID', 'order_file') values (\'%d\', \'%d\', '0')",aIntCollid,aIntFileId];
            NSLog(@"insert=======\n%@\n",aStrInsert);
            [[Database sharedDatabase] Insert:aStrInsert];
       }
    }
    
    int fullDownload = [[NSUserDefaults standardUserDefaults] boolForKey:@"FullDownloaded"];
    int updateDownloaded = [[NSUserDefaults standardUserDefaults] boolForKey:@"UpdateDownloaded"];
    
    NSLog(@"FullDownloaded ------ %d, UpdateDownloaded --------%d", fullDownload, updateDownloaded);
    
    if([aMutArrExists[0][@"count(filesID)"]intValue] >= 1)
    {
       if([[NSUserDefaults standardUserDefaults]boolForKey:@"FullDownloaded"] && ![[NSUserDefaults standardUserDefaults]boolForKey:@"UpdateDownloaded"])
       {
           
           NSString *aStrDelete = [NSString stringWithFormat:@"Delete from CollectionFilesMaster where filesID = '%@'",aMutarrVal[0][@"filesID"]];
            NSLog(@"Delete=======\n%@\n",aStrDelete);
           /*
            NSString *aStrDelete = [NSString stringWithFormat:@"Delete from CollectionFilesMaster where filesID = '%@'",[[aMutarrVal objectAtIndex:0]objectForKey:@"filesID"]];
            */
           [[Database sharedDatabase]Delete:aStrDelete];
           NSString *aStrInsert = [NSString stringWithFormat:@"insert into CollectionFilesMaster ('collectionID', 'filesID', 'order_file') values (\'%d\', \'%d\', '0')",aIntCollid,aIntFileId];
           NSLog(@"Insert =======\n%@\n",aStrInsert);
           [[Database sharedDatabase] Insert:aStrInsert];
       }
   }
}

@end
