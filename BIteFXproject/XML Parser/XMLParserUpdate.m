//
//  XMLParserUpdate.m
//  BIteFXproject
//
//  Created by indianic on 10/3/17.
//
//

#import "XMLParserUpdate.h"
#import "AppDelegate.h"
#import "Database.h"

@implementation XMLParserUpdate

- (XMLParserUpdate *) initXMLParserUpdate {
    
    if (!(self = [super init])) return nil;
    
    boolIsGroupOn = FALSE;
    boolIsNewGroup = FALSE;
    strCurrGroup = @"";
    return self;
}

- (void)parser:(NSXMLParser *)parser didStartElement:(NSString *)elementName
  namespaceURI:(NSString *)namespaceURI qualifiedName:(NSString *)qualifiedName
    attributes:(NSDictionary *)attributeDict {
    
    NSLog(@"start Element = %@",elementName);
    
    if([elementName isEqualToString:@"BITEFX"]) {
        
    }
    else if([elementName isEqualToString:@"UPGRADE"]) {
        
        // Version 2.5 change support for app upgrade with updates list...
        // This tag in MVI file represents that new app version is available. Show message to user...
        appDelegate.isUpgradeAvailable = YES;
    }
    else if([elementName isEqualToString:@"MOVIES"])
    {
        
    }
    else if([elementName isEqualToString:@"MOVIE"])
    {
        if (boolIsGroupOn)
        {
            int aIntCollId = [self getCollectionIdFromCollectionMaster:strCollectionName withType:mediaMovie];
            int aIntFileId = [self getLocalIdFromFileMasterWithTitle:attributeDict[@"NAME"] withtype:mediaMovie];
            if (aIntCollId != -1 && aIntFileId != -1) {
                [self insertInfoIntoCollectionFileMaster:aIntCollId fileId:aIntFileId];
                
                // When new group is created using existing Movies, we need to show blue background and border...
                if (boolIsNewGroup) {
                    [self updateIsPlayedIntoLocalFileMaster:aIntFileId];
                }
            }
        }
        else
        {
            // Insert or update data...
            [self insertOrUpdateFilesInfoWithName:attributeDict[@"NAME"] DisplayNm:attributeDict[@"DISPLAYNAME"] shortName:attributeDict[@"SHORTDISPLAYNAME"] filetype:mediaMovie];
        }
    }
    else if([elementName isEqualToString:@"bitefx:HERE_MOVIE"]) {
        
        // This tag is used to update Movie data...
        [self insertOrUpdateFilesInfoWithName:attributeDict[@"NAME"] DisplayNm:attributeDict[@"DISPLAYNAME"] shortName:attributeDict[@"SHORTDISPLAYNAME"] filetype:mediaMovie];
    }
    else if([elementName isEqualToString:@"bitefx:SKIP_MOVIE"]) {
        
        // This tag will come inside <bitefx:HERE_MOVIEGROUP> or <bitefx:HERE_PRESENTATION> tag...
        // Skip this Movie (Update Order Index)...
        if (boolIsGroupOn)
        {
            intOrderIndex = intOrderIndex + 1;
            int aIntCollId = [self getCollectionIdFromCollectionMaster:strCollectionName withType:mediaMovie];
            int aIntFileId = [self getLocalIdFromFileMasterWithTitle:attributeDict[@"NAME"] withtype:mediaMovie];
            if (aIntCollId != -1 && aIntFileId != -1) {
                [self updateOrderIndexIntoCollectionFileMaster:aIntCollId fileId:aIntFileId];
            }
        }
        
    }
    else if([elementName isEqualToString:@"bitefx:PLUS_MOVIE"]) {
        
        // This tag will come inside <bitefx:HERE_MOVIEGROUP> or <bitefx:HERE_PRESENTATION> tag...
        // Add new Movie. Movie data is already inserted. Insert data in CollectionFilesMaster...
        if (boolIsGroupOn)
        {
            intOrderIndex = intOrderIndex + 1;
            int aIntCollId = [self getCollectionIdFromCollectionMaster:strCollectionName withType:mediaMovie];
            int aIntFileId = [self getLocalIdFromFileMasterWithTitle:attributeDict[@"NAME"] withtype:mediaMovie];
            if (aIntCollId != -1 && aIntFileId != -1) {
                [self insertInfoIntoCollectionFileMaster:aIntCollId fileId:aIntFileId];
            }
        }
    }
    else if([elementName isEqualToString:@"bitefx:DELE_MOVIE"]) {
        
        // This tag will come inside <bitefx:HERE_MOVIEGROUP> or <bitefx:HERE_PRESENTATION> tag...
        // Delete this Movie from MovieGroup...
        if (boolIsGroupOn)
        {
            int aIntCollId = [self getCollectionIdFromCollectionMaster:strCollectionName withType:mediaMovie];
            int aIntFileId = [self getLocalIdFromFileMasterWithTitle:attributeDict[@"NAME"] withtype:mediaMovie];
            if (aIntCollId != -1 && aIntFileId != -1) {
                [self deleteDataFromCollectionFileMaster:aIntCollId fileId:aIntFileId];
            }
        }
        
    }
    else if ([elementName isEqualToString:@"MOVIEGROUPS"])
    {
        boolIsGroupOn = TRUE;
        strCurrGroup = mediaMovie;
        
        // Set collection index...
        intCollectionIndex = 0;
    }
//    else if ([elementName isEqualToString:@"MOVIEGROUP"])
//    {
//        strCurrGroup = mediaMovie;
//        if(!strCollectionName)
//            strCollectionName = [[NSMutableString alloc] initWithString:[attributeDict valueForKey:@"NAME"]];
//        else
//            [strCollectionName appendString:[attributeDict valueForKey:@"NAME"]];
//
//        [self insertCollectionDeatilWithName:[attributeDict objectForKey:@"NAME"] DisplayName:[attributeDict objectForKey:@"DISPLAYNAME"] withFiletype:mediaMovie];
//    }
    else if([elementName isEqualToString:@"bitefx:SKIP_MOVIEGROUP"]) {
        
        // Increase collection index...
        intCollectionIndex = intCollectionIndex + 1;
    }
    else if ([elementName isEqualToString:@"bitefx:PLUS_MOVIEGROUP"])
    {
        strCurrGroup = mediaMovie;
        if(!strCollectionName)
            strCollectionName = [[NSMutableString alloc] initWithString:[attributeDict valueForKey:@"NAME"]];
        else
            [strCollectionName appendString:[attributeDict valueForKey:@"NAME"]];

        //        [self insertCollectionDeatilWithName:[attributeDict objectForKey:@"NAME"] DisplayName:[attributeDict objectForKey:@"DISPLAYNAME"] withFiletype:mediaMovie];
        
        boolIsNewGroup = TRUE;
        NSMutableDictionary *aMutDictCollData = [[NSMutableDictionary alloc] init];
        aMutDictCollData[@"collectionName"] = attributeDict[@"NAME"];
        aMutDictCollData[@"collectionDisplayName"] = attributeDict[@"DISPLAYNAME"];
        
        // Insert data at specific index...
        [self insertCollectionDetailAtSpecificIndex:aMutDictCollData withFiletype:mediaMovie];
        
        // Increase collection index for next @"bitefx:PLUS_MOVIEGROUP" tag...
        intCollectionIndex = intCollectionIndex + 1;
    }
    else if ([elementName isEqualToString:@"bitefx:HERE_MOVIEGROUP"]) {
        // Set order index...
        intOrderIndex = 0;
        
        // Get MovieGroup name...
        strCurrGroup = mediaMovie;
        if(!strCollectionName)
            strCollectionName = [[NSMutableString alloc] initWithString:[attributeDict valueForKey:@"NAME"]];
        else
            [strCollectionName appendString:[attributeDict valueForKey:@"NAME"]];

    }
    else if ([elementName isEqualToString:@"bitefx:DELE_MOVIEGROUP"])
    {
        if(!strCollectionName)
            strCollectionName = [[NSMutableString alloc] initWithString:[attributeDict valueForKey:@"NAME"]];
        else
            [strCollectionName appendString:[attributeDict valueForKey:@"NAME"]];
        
        int aIntCollId = [self getCollectionIdFromCollectionMaster:strCollectionName withType:mediaMovie];
        
        if (aIntCollId != -1) {
            
            // Delete MovieGroup...
            [self deleteCollectionData:aIntCollId];
        }
        
    }
    else if([elementName isEqualToString:@"IMAGES"])
    {
        
    }
    else if([elementName isEqualToString:@"IMAGE"])
    {
        if (boolIsGroupOn)
        {
            int aIntCollId = [self getCollectionIdFromCollectionMaster:strCollectionName withType:mediaImage];
            int aIntFileId = [self getLocalIdFromFileMasterWithTitle:attributeDict[@"NAME"] withtype:mediaImage];
            if (aIntCollId != -1 && aIntFileId != -1) {
                [self insertInfoIntoCollectionFileMaster:aIntCollId fileId:aIntFileId];
                
                // When new group is created using existing Movies, we need to show blue background and border...
                if (boolIsNewGroup) {
                    [self updateIsPlayedIntoLocalFileMaster:aIntFileId];
                }

            }
        }
        else
        {
            // Insert or update data...
            [self insertOrUpdateFilesInfoWithName:attributeDict[@"NAME"] DisplayNm:attributeDict[@"DISPLAYNAME"] shortName:attributeDict[@"SHORTDISPLAYNAME"] filetype:mediaImage];
        }
        
    }
    else if([elementName isEqualToString:@"bitefx:HERE_IMAGE"]) {
        
        // This tag is used to update Image data...
        [self insertOrUpdateFilesInfoWithName:attributeDict[@"NAME"] DisplayNm:attributeDict[@"DISPLAYNAME"] shortName:attributeDict[@"SHORTDISPLAYNAME"] filetype:mediaImage];
    }
    else if([elementName isEqualToString:@"bitefx:SKIP_IMAGE"]) {
        
        // This tag will come inside <bitefx:HERE_IMAGEGROUP> or <bitefx:HERE_PRESENTATION> tag...
        // Skip this Image (Update Order Index)...
        if (boolIsGroupOn)
        {
            intOrderIndex = intOrderIndex + 1;
            int aIntCollId = [self getCollectionIdFromCollectionMaster:strCollectionName withType:mediaImage];
            int aIntFileId = [self getLocalIdFromFileMasterWithTitle:attributeDict[@"NAME"] withtype:mediaImage];
            if (aIntCollId != -1 && aIntFileId != -1) {
                [self updateOrderIndexIntoCollectionFileMaster:aIntCollId fileId:aIntFileId];
            }
        }
        
    }
    else if([elementName isEqualToString:@"bitefx:PLUS_IMAGE"]) {
        
        // This tag will come inside <bitefx:HERE_IMAGEGROUP> or <bitefx:HERE_PRESENTATION> tag...
        // Add new Image. Image data is already inserted. Insert data in CollectionFilesMaster...
        if (boolIsGroupOn)
        {
            intOrderIndex = intOrderIndex + 1;
            int aIntCollId = [self getCollectionIdFromCollectionMaster:strCollectionName withType:mediaImage];
            int aIntFileId = [self getLocalIdFromFileMasterWithTitle:attributeDict[@"NAME"] withtype:mediaImage];
            if (aIntCollId != -1 && aIntFileId != -1) {
                [self insertInfoIntoCollectionFileMaster:aIntCollId fileId:aIntFileId];
            }
        }
    }
    else if([elementName isEqualToString:@"bitefx:DELE_IMAGE"]) {
        
        // This tag will come inside <bitefx:HERE_IMAGEGROUP> or <bitefx:HERE_PRESENTATION> tag...
        // Delete this Image from ImageGroup...
        if (boolIsGroupOn)
        {
            int aIntCollId = [self getCollectionIdFromCollectionMaster:strCollectionName withType:mediaImage];
            int aIntFileId = [self getLocalIdFromFileMasterWithTitle:attributeDict[@"NAME"] withtype:mediaImage];
            if (aIntCollId != -1 && aIntFileId != -1) {
                [self deleteDataFromCollectionFileMaster:aIntCollId fileId:aIntFileId];
            }
        }
        
    }
    else if ([elementName isEqualToString:@"IMAGEGROUPS"])
    {
        boolIsGroupOn = TRUE;
        strCurrGroup = mediaImage;
        
        // Set collection index...
        intCollectionIndex = 0;

    }
//    else if ([elementName isEqualToString:@"IMAGEGROUP"])
//    {
//        strCurrGroup = mediaImage;
//        if(!strCollectionName)
//            strCollectionName = [[NSMutableString alloc] initWithString:[attributeDict valueForKey:@"NAME"]];
//        else
//            [strCollectionName appendString:[attributeDict valueForKey:@"NAME"]];
//
//        // NSLog(@"Collection Name Value: %@", strCollectionName);
//
//
//        [self insertCollectionDeatilWithName:[attributeDict objectForKey:@"NAME"] DisplayName:[attributeDict objectForKey:@"DISPLAYNAME"] withFiletype:mediaImage];
//    }
    else if([elementName isEqualToString:@"bitefx:SKIP_IMAGEGROUP"]) {
        
        // Increase collection index...
        intCollectionIndex = intCollectionIndex + 1;
    }
    else if ([elementName isEqualToString:@"bitefx:PLUS_IMAGEGROUP"])
    {
        strCurrGroup = mediaImage;
        if(!strCollectionName)
            strCollectionName = [[NSMutableString alloc] initWithString:[attributeDict valueForKey:@"NAME"]];
        else
            [strCollectionName appendString:[attributeDict valueForKey:@"NAME"]];
        
        //        [self insertCollectionDeatilWithName:[attributeDict objectForKey:@"NAME"] DisplayName:[attributeDict objectForKey:@"DISPLAYNAME"] withFiletype:mediaMovie];
        
        boolIsNewGroup = TRUE;
        NSMutableDictionary *aMutDictCollData = [[NSMutableDictionary alloc] init];
        aMutDictCollData[@"collectionName"] = attributeDict[@"NAME"];
        aMutDictCollData[@"collectionDisplayName"] = attributeDict[@"DISPLAYNAME"];
        
        // Insert data at specific index...
        [self insertCollectionDetailAtSpecificIndex:aMutDictCollData withFiletype:mediaImage];
        
        // Increase collection index for next @"bitefx:PLUS_IMAGEGROUP" tag...
        intCollectionIndex = intCollectionIndex + 1;

    }
    else if ([elementName isEqualToString:@"bitefx:HERE_IMAGEGROUP"]) {
        
        // Set order index...
        intOrderIndex = 0;
        
        // Get ImageGroup name...
        strCurrGroup = mediaImage;
        if(!strCollectionName)
            strCollectionName = [[NSMutableString alloc] initWithString:[attributeDict valueForKey:@"NAME"]];
        else
            [strCollectionName appendString:[attributeDict valueForKey:@"NAME"]];
        
    }
    else if ([elementName isEqualToString:@"bitefx:DELE_IMAGEGROUP"])
    {
        if(!strCollectionName)
            strCollectionName = [[NSMutableString alloc] initWithString:[attributeDict valueForKey:@"NAME"]];
        else
            [strCollectionName appendString:[attributeDict valueForKey:@"NAME"]];
        
        int aIntCollId = [self getCollectionIdFromCollectionMaster:strCollectionName withType:mediaImage];
        
        if (aIntCollId != -1) {
            
            // Delete ImageGroup...
            [self deleteCollectionData:aIntCollId];
        }
        
    }
    else if ([elementName isEqualToString:@"PRESENTATIONS"])
    {
        boolIsGroupOn = TRUE;
        strCurrGroup = mediaPresentation;
        
        // Set collection index...
        intCollectionIndex = 0;
    }
//    else if ([elementName isEqualToString:@"PRESENTATION"])
//    {
//        strCurrGroup = mediaPresentation;
//        if(!strCollectionName)
//            strCollectionName = [[NSMutableString alloc] initWithString:[attributeDict valueForKey:@"NAME"]];
//        else
//            [strCollectionName appendString:[attributeDict valueForKey:@"NAME"]];
//
//        [self insertCollectionDeatilWithName:[attributeDict objectForKey:@"NAME"] DisplayName:[attributeDict objectForKey:@"DISPLAYNAME"] withFiletype:mediaPresentation];
//    }
    else if([elementName isEqualToString:@"bitefx:SKIP_PRESENTATION"]) {
        
        // Increase collection index...
        intCollectionIndex = intCollectionIndex + 1;
    }
    else if ([elementName isEqualToString:@"bitefx:PLUS_PRESENTATION"])
    {
        strCurrGroup = mediaPresentation;
        if(!strCollectionName)
            strCollectionName = [[NSMutableString alloc] initWithString:[attributeDict valueForKey:@"NAME"]];
        else
            [strCollectionName appendString:[attributeDict valueForKey:@"NAME"]];
        
        //        [self insertCollectionDeatilWithName:[attributeDict objectForKey:@"NAME"] DisplayName:[attributeDict objectForKey:@"DISPLAYNAME"] withFiletype:mediaMovie];
        
        boolIsNewGroup = TRUE;
        NSMutableDictionary *aMutDictCollData = [[NSMutableDictionary alloc] init];
        aMutDictCollData[@"collectionName"] = attributeDict[@"NAME"];
        aMutDictCollData[@"collectionDisplayName"] = attributeDict[@"DISPLAYNAME"];
        
        // Insert data at specific index...
        [self insertCollectionDetailAtSpecificIndex:aMutDictCollData withFiletype:mediaPresentation];
        
        // Increase collection index for next @"bitefx:PLUS_PRESENTATION" tag...
        intCollectionIndex = intCollectionIndex + 1;

    }
    else if ([elementName isEqualToString:@"bitefx:HERE_PRESENTATION"]) {
        // Set order index...
        intOrderIndex = 0;
        
        // Get MovieGroup name...
        strCurrGroup = mediaPresentation;
        if(!strCollectionName)
            strCollectionName = [[NSMutableString alloc] initWithString:[attributeDict valueForKey:@"NAME"]];
        else
            [strCollectionName appendString:[attributeDict valueForKey:@"NAME"]];
        
    }
    else if ([elementName isEqualToString:@"bitefx:DELE_PRESENTATION"])
    {
        if(!strCollectionName)
            strCollectionName = [[NSMutableString alloc] initWithString:[attributeDict valueForKey:@"NAME"]];
        else
            [strCollectionName appendString:[attributeDict valueForKey:@"NAME"]];
        
        int aIntCollId = [self getCollectionIdFromCollectionMaster:strCollectionName withType:mediaPresentation];
        
        if (aIntCollId != -1) {
            
            // Delete Presentation...
            [self deleteCollectionData:aIntCollId];
        }
        
    }
    else if ([elementName isEqualToString:@"bitefx:HERE_FEATURE"]) {
        
        // Set collection index... (Start from zero.)
        intCollectionIndex = 0;

        if(!strFeatureName)
            strFeatureName = [[NSMutableString alloc] initWithString:[attributeDict valueForKey:@"NAME"]];
        else
            [strFeatureName appendString:[attributeDict valueForKey:@"NAME"]];
    }
    
}

- (void)parser:(NSXMLParser *)parser foundCharacters:(NSString *)string {
    
    if(!currentElementValue)
        currentElementValue = [[NSMutableString alloc] initWithString:string];
    else
        [currentElementValue appendString:string];
    
}

- (void)parser:(NSXMLParser *)parser didEndElement:(NSString *)elementName
  namespaceURI:(NSString *)namespaceURI qualifiedName:(NSString *)qName {
//    elementName = [elementName uppercaseString];
    
    if([elementName isEqualToString:@"BITEFX"]) {
        NSLog(@"parsing is completed ");
    }
    else if([elementName isEqualToString:@"MOVIES"]) {
        NSLog(@"data is inserted ");
    }
    else if([elementName isEqualToString:@"MOVIE"])
    {
        
    }
    else if ([elementName isEqualToString:@"MOVIEGROUPS"])
    {
        boolIsGroupOn = FALSE;
        strCurrGroup = @"";
    }
//    else if ([elementName isEqualToString:@"MOVIEGROUP"])
//    {
//        strCollectionName = nil;
//    }
    else if ([elementName isEqualToString:@"bitefx:PLUS_MOVIEGROUP"])
    {
        boolIsNewGroup = FALSE;
        strCollectionName = nil;
    }
    else if ([elementName isEqualToString:@"bitefx:HERE_MOVIEGROUP"])
    {
        strCollectionName = nil;
    }
    else if ([elementName isEqualToString:@"bitefx:DELE_MOVIEGROUP"])
    {
        strCollectionName = nil;
    }
    else if ([elementName isEqualToString:@"IMAGEGROUPS"])
    {
        boolIsGroupOn = FALSE;
        strCurrGroup = @"";
    }
//    else if ([elementName isEqualToString:@"IMAGEGROUP"])
//    {
//        strCollectionName = nil;
//    }
    else if ([elementName isEqualToString:@"bitefx:PLUS_IMAGEGROUP"])
    {
        boolIsNewGroup = FALSE;
        strCollectionName = nil;
    }
    else if ([elementName isEqualToString:@"bitefx:HERE_IMAGEGROUP"])
    {
        strCollectionName = nil;
    }
    else if ([elementName isEqualToString:@"bitefx:DELE_IMAGEGROUP"])
    {
        strCollectionName = nil;
    }
    else if ([elementName isEqualToString:@"PRESENTATIONS"])
    {
        boolIsGroupOn = FALSE;
        strCurrGroup = @"";
    }
//    else if ([elementName isEqualToString:@"PRESENTATION"])
//    {
//        strCollectionName = nil;
//    }
    else if ([elementName isEqualToString:@"bitefx:PLUS_PRESENTATION"])
    {
        boolIsNewGroup = FALSE;
        strCollectionName = nil;
    }
    else if ([elementName isEqualToString:@"bitefx:HERE_PRESENTATION"])
    {
        strCollectionName = nil;
    }
    else if ([elementName isEqualToString:@"bitefx:DELE_PRESENTATION"])
    {
        strCollectionName = nil;
    }
    else if ([elementName isEqualToString:@"bitefx:HERE_FEATURE"])
    {
        strFeatureName = nil;
    }
    else
        
        ;
    currentElementValue = nil;
}



#pragma mark - Database Methods

#pragma mark Insert Or Update FileInfo into LocalFileMaster Methods

- (void)insertOrUpdateFilesInfoWithName:(NSString *)aStrNm DisplayNm:(NSString *)aStrDN shortName:(NSString *)aStrSN filetype:(NSString*)fileType{
    NSString* isType = @"";
    if([fileType isEqualToString:mediaMovie])
    {
        isType = VIDEO_EXTENSION;
    }
    else
    {
        isType = @"jpg";
    }
    aStrSN = [aStrSN stringByReplacingOccurrencesOfString:@"'" withString:@" "];
    
    NSString *aStrURLForVideo,*aStrURLForJpg,*aStrURLForHtml;
    
    aIntRegistered = [[NSUserDefaults standardUserDefaults]boolForKey:@"Registered"];
    
    if(aIntRegistered) {
        
        aStrURLForVideo = [NSString stringWithFormat:@"Update0001/%@.%@",aStrNm,isType];
        aStrURLForJpg = [NSString stringWithFormat:@"Update0001/%@.jpg",aStrNm];
        aStrURLForHtml = [NSString stringWithFormat:@"Update0001/%@.html",aStrNm];

        NSString *sql = [NSString stringWithFormat:@"select count (fileTitle) from LocalFileMaster where fileTitle = '%@.%@'",aStrNm,isType];
        NSInteger count = [[Database shareDatabase] getCount:sql];
        
        if([fileType isEqualToString:mediaMovie]) { // fileType "Movie"
            
            // Check for all three files...
            if ([[NSFileManager defaultManager] fileExistsAtPath:[NSString stringWithFormat:@"%@/%@", DOCUMENT_DIRECTORY_PATH, aStrURLForHtml]] && [[NSFileManager defaultManager] fileExistsAtPath:[NSString stringWithFormat:@"%@/%@", DOCUMENT_DIRECTORY_PATH,aStrURLForJpg]] && [[NSFileManager defaultManager] fileExistsAtPath:[NSString stringWithFormat:@"%@/%@", DOCUMENT_DIRECTORY_PATH,aStrURLForVideo]] && count == 0) {
                
                // Insert new Movie data...
                NSString *aStrIsPlayed = @"0";
                NSString *aStrInsertQuery = [NSString stringWithFormat:@"insert into LocalFileMaster ('fileTitle', 'fileName', 'fileShortDispName', 'isDownloaded', 'isPlayed', 'filePath', 'infoFilePath','thumbnailPath','fileType') values (\'%@.%@\', \"%@\", \"%@\", '0', '%@', \'%@\', \'%@\', \'%@\', \'%@\')",aStrNm,isType,aStrDN,aStrSN, aStrIsPlayed,aStrURLForVideo,aStrURLForHtml,aStrURLForJpg,fileType];
                [[Database sharedDatabase] Insert:aStrInsertQuery];
                
            }
            else if ([[NSFileManager defaultManager] fileExistsAtPath:[NSString stringWithFormat:@"%@/%@", DOCUMENT_DIRECTORY_PATH, aStrURLForHtml]] && [[NSFileManager defaultManager] fileExistsAtPath:[NSString stringWithFormat:@"%@/%@", DOCUMENT_DIRECTORY_PATH,aStrURLForJpg]] && [[NSFileManager defaultManager] fileExistsAtPath:[NSString stringWithFormat:@"%@/%@", DOCUMENT_DIRECTORY_PATH,aStrURLForVideo]] && count == 1) {
                
                // Movie data alreay exists, update data...
                NSString *aStrIsPlayed = @"0";
                NSString *aStrUpdateQuery = [NSString stringWithFormat:@"update LocalFileMaster set fileName = \"%@\", fileShortDispName = \"%@\", filePath = '%@', infoFilePath = '%@',thumbnailPath = '%@',isPlayed = '%@' where fileTitle ='%@.%@'",aStrDN,aStrSN,aStrURLForVideo,aStrURLForHtml,aStrURLForJpg,aStrIsPlayed,aStrNm,isType];
                [[Database sharedDatabase] Update:aStrUpdateQuery];
            }
        }
        else // fileType "Image"
        {
            // Check for all two files...
            if ([[NSFileManager defaultManager] fileExistsAtPath:[NSString stringWithFormat:@"%@/%@", DOCUMENT_DIRECTORY_PATH,aStrURLForJpg]] && [[NSFileManager defaultManager] fileExistsAtPath:[NSString stringWithFormat:@"%@/%@", DOCUMENT_DIRECTORY_PATH,aStrURLForVideo]] && count == 0) {
                
                // Insert new Image data...
                NSString *aStrIsPlayed = @"0";
                NSString *aStrInsertQuery = [NSString stringWithFormat:@"insert into LocalFileMaster ('fileTitle', 'fileName', 'fileShortDispName', 'isDownloaded', 'isPlayed', 'filePath', 'infoFilePath','thumbnailPath','fileType') values (\'%@.%@\', \"%@\", \"%@\", '0', '%@', \'%@\', \'%@\', \'%@\', \'%@\')",aStrNm,isType,aStrDN,aStrSN, aStrIsPlayed,aStrURLForVideo,@"",aStrURLForJpg,fileType];
                [[Database sharedDatabase] Insert:aStrInsertQuery];
                
            }
            else if ([[NSFileManager defaultManager] fileExistsAtPath:[NSString stringWithFormat:@"%@/%@", DOCUMENT_DIRECTORY_PATH,aStrURLForJpg]] && [[NSFileManager defaultManager] fileExistsAtPath:[NSString stringWithFormat:@"%@/%@", DOCUMENT_DIRECTORY_PATH,aStrURLForVideo]] && count == 1) {
                
                // Image data alreay exists, update data...
                NSString *aStrIsPlayed = @"0";
                NSString *aStrUpdateQuery = [NSString stringWithFormat:@"update LocalFileMaster set fileName = \"%@\", fileShortDispName =  \"%@\",  filePath = '%@',thumbnailPath = '%@',isPlayed = '%@' where fileTitle ='%@.%@'",aStrDN,aStrSN,aStrURLForVideo,aStrURLForJpg,aStrIsPlayed,aStrNm,isType];
                [[Database sharedDatabase] Update:aStrUpdateQuery];
            }
            
        }
        
    }
    
}

#pragma mark Insert Collection Info and its detail file Methods

- (void)insertCollectionDeatilWithName:(NSString *)aStrCollNm DisplayName:(NSString *)aStrDSNm withFiletype:(NSString*)fileType
{
    NSString *aStrCollectionExist = [NSString stringWithFormat:@"select count(collectionID) from CollectionMaster where collectionName = \'%@\'",aStrCollNm];
    
    // Version 2.4 changes
    // If "DisplayName" not given, use "Name" as "DisplayName"
    if ((aStrDSNm == nil) || ([aStrDSNm isEqualToString:@""])) {
        aStrDSNm = aStrCollNm;
    }
    
    NSString *aStrInfoPath = @"";
    int aIntFeatureId = 0;
    
    if([fileType isEqualToString:mediaPresentation]) {
        
        // For updates(When new presentation is added in Updates)...
        aStrInfoPath = [NSString stringWithFormat:@"Update0001/%@.html", aStrCollNm];
        
        //===================
        // Version 3.1 Changes
        //===================
        // Feature MVI file changes.
        /// Note: The new functionality is added for Presentations which is FeatureSets means Presentations are now part of FeatureSets.
        /// So FetureSets contain one or more presentations.
        /// Presentation can be added in Predefined FeatureSets.
        /// Most Important: FeatureSets are predefined only five. These can not be change through XML. Only Presentations can be add, update or delete in predefined FeatureSets. Old logic will work for all other tags, but when new Presentation is added nowonwards we need to add FeatureID also for which FeatureSet it belongs.
        /// For HERE_FEATURE Tag insert FeatureID in CollectionMaster.
        
        // Get FeatureID for current FeatureName...
        aIntFeatureId = [self getFeatureIdFromFeatureMaster:strFeatureName];
        
    }
    
    NSMutableArray *aMutArrExist = [[Database sharedDatabase] getAllDataForQuery:aStrCollectionExist];
    
    if(aMutArrExist.count > 0)
    {
        if ([aMutArrExist[0][@"count(collectionID)"] intValue] == 0)
        {
            NSString *aStrInsertInfo = [NSString stringWithFormat:@"insert into CollectionMaster ('collectionName', 'collectionDisplayName', 'fileType', 'isUserDefine', 'infoFilePath', 'fileOrder') values (\"%@\", \"%@\", \'%@\', '0', \'%@\', '%d')",aStrCollNm, aStrDSNm, fileType, aStrInfoPath, intCollectionIndex + 1];
            
            //===================
            // Version 3.1 Changes
            //===================
            // Feature MVI file changes.
            /// For HERE_FEATURE Tag insert FeatureID in CollectionMaster.
            if([fileType isEqualToString:mediaPresentation]) {
                
                if (aIntFeatureId != -1) {
                    aStrInsertInfo = [NSString stringWithFormat:@"insert into CollectionMaster ('collectionName', 'collectionDisplayName', 'fileType', 'isUserDefine', 'infoFilePath', 'fileOrder', 'featureID') values (\"%@\", \"%@\", \'%@\', '0', \'%@\', '%d', '%d')",aStrCollNm, aStrDSNm, fileType, aStrInfoPath, intCollectionIndex + 1, aIntFeatureId];
                }

            }
            
            // Here +1 is added in intCollectionIndex to start fileOrder from 1...
            [[Database sharedDatabase] Insert:aStrInsertInfo];
        } else {

            //===================
            // Version 3.1 Changes
            //===================
            // Feature MVI file changes.
            /// For HERE_FEATURE Tag update FeatureID in CollectionMaster.
            if([fileType isEqualToString:mediaPresentation]) {
                if (aIntFeatureId != -1) {
                    int aIntCollId = [self getCollectionIdFromCollectionMaster:strCollectionName withType:mediaPresentation];
                    NSString *aStrUpdateQuery = [NSString stringWithFormat:@"update CollectionMaster set fileOrder = '%d', featureID = %d where collectionID = %d", intCollectionIndex + 1, aIntFeatureId, aIntCollId];
                    [[Database sharedDatabase] Update: aStrUpdateQuery];
                }
            }
        }
    }
}

#pragma mark Get Collection and File Ids Methods

- (int)getLocalIdFromFileMasterWithTitle:(NSString *)aStrFileTitle withtype:(NSString*)fileType
{
    int aIntResult;
    NSString *mediaType = @"";
    if ([fileType isEqualToString:mediaMovie])
    {
        mediaType = VIDEO_EXTENSION;
    }
    else
    {
        mediaType = @"jpg";
    }
    NSString *aStrGetId = [NSString stringWithFormat:@"select fileID from LocalFileMaster where fileTitle = \'%@.%@\'",aStrFileTitle,mediaType];
    
    NSMutableArray *aMutArrResult = [[Database sharedDatabase] getAllDataForQuery:aStrGetId];
    if (aMutArrResult.count>0) {
        aIntResult = [aMutArrResult[0][@"fileID"] intValue];
    }
    else{
        aIntResult = -1;
    }
    
    return aIntResult;
}

- (int)getCollectionIdFromCollectionMaster:(NSString *)aStrCollectionNm withType:(NSString*)fileType
{
    int aIntResult;
    
    if([strCurrGroup  isEqual: mediaPresentation] && ![strCurrGroup  isEqual: @""])
    {
        fileType = mediaPresentation;
    }
    
    NSString *aStrGetId = [NSString stringWithFormat:@"select collectionID from CollectionMaster where collectionName = \'%@\' and fileType = \'%@\'",aStrCollectionNm,fileType];
    
    NSMutableArray *aMutArrResult = [[Database sharedDatabase] getAllDataForQuery:aStrGetId];
    
    if (aMutArrResult.count > 0)
    {
        aIntResult = [aMutArrResult[0][@"collectionID"] intValue];
    }
    else
    {
        aIntResult = -1;
    }
    
    return aIntResult;
}

#pragma mark Insert collectionFiles Info Into CollectionFileMaster

- (void)insertInfoIntoCollectionFileMaster:(int)aIntCollId fileId:(int)aIntFileId
{
    NSString *aStrExists = [NSString stringWithFormat:@"select count(filesID) from CollectionFilesMaster where collectionID = %d AND filesID = \'%d\'",aIntCollId,aIntFileId];
    
    NSMutableArray *aMutArrExists = [[Database sharedDatabase] getAllDataForQuery:aStrExists];
    
    if(aMutArrExists.count > 0)
    {
        if ([aMutArrExists[0][@"count(filesID)"] intValue] == 0)
        {
            NSString *aStrInsert = [NSString stringWithFormat:@"insert into CollectionFilesMaster ('collectionID', 'filesID', 'order_file') values (\'%d\', \'%d\', \'%d\')",aIntCollId,aIntFileId, intOrderIndex];
            NSLog(@"insert=======\n%@\n",aStrInsert);
            [[Database sharedDatabase] Insert:aStrInsert];
        }
        else if ([aMutArrExists[0][@"count(filesID)"] intValue] >= 1) {
            
            // Data already exists, update order index...
            NSString *aStrUpdateQuery = [NSString stringWithFormat:@"update CollectionFilesMaster set order_file = '%d' where collectionID = %d AND filesID = %d", intOrderIndex, aIntCollId, aIntFileId];
            [[Database sharedDatabase] Update:aStrUpdateQuery];
            
        }
    }
}

- (void)updateOrderIndexIntoCollectionFileMaster:(int)aIntCollId fileId:(int)aIntFileId
{
    NSString *aStrExists = [NSString stringWithFormat:@"select count(filesID) from CollectionFilesMaster where collectionID = %d AND filesID = %d",aIntCollId,aIntFileId];
    
    NSMutableArray *aMutArrExists = [[Database sharedDatabase] getAllDataForQuery:aStrExists];
    
    if(aMutArrExists.count > 0)
    {
        if ([aMutArrExists[0][@"count(filesID)"] intValue] >= 1) {
            
            // Data already exists, update order index...
            NSString *aStrUpdateQuery = [NSString stringWithFormat:@"update CollectionFilesMaster set order_file = '%d' where collectionID = %d AND filesID = %d", intOrderIndex, aIntCollId, aIntFileId];
            [[Database sharedDatabase] Update:aStrUpdateQuery];
            
        }
    }
}

- (void)deleteDataFromCollectionFileMaster:(int)aIntCollId fileId:(int)aIntFileId {
    
    // Delete data from CollectionFileMaster. LocalFileMaster data will not be deleted because files may used in another movie or image group...
    NSString *aStrExists = [NSString stringWithFormat:@"select count(filesID) from CollectionFilesMaster where collectionID = %d AND filesID = %d",aIntCollId,aIntFileId];
    
    NSMutableArray *aMutArrExists = [[Database sharedDatabase] getAllDataForQuery:aStrExists];
    
    if(aMutArrExists.count > 0)
    {
        if ([aMutArrExists[0][@"count(filesID)"] intValue] >= 1) {
            
            NSString *aStrDelete = [NSString stringWithFormat:@"Delete from CollectionFilesMaster where collectionID = %d AND filesID = %d",aIntCollId,aIntFileId];
            NSLog(@"Delete=======\n%@\n",aStrDelete);
            
            [[Database sharedDatabase]Delete:aStrDelete];
            
        }
    }

}

#pragma mark Get Collection Data Methods
- (void)insertCollectionDetailAtSpecificIndex:(NSDictionary *)aDictCollData withFiletype:(NSString*)fileType {
    
    // First get all groups data for specific fileType...
    // Query to get UserDefined photos last...
    NSString *aStrAllGroups = [NSString stringWithFormat:@"select * from CollectionMaster where fileType = \'%@\' Order By isUserDefine Asc, fileOrder",fileType];
    
    //===================
    // Version 3.1 Changes
    //===================
    // Feature MVI file changes.
    /// Note: The new functionality is added for Presentations which is FeatureSets means Presentations are now part of FeatureSets.
    /// So FetureSets contain one or more presentations.
    /// Presentation can be added in Predefined FeatureSets.
    /// Most Important: FeatureSets are predefined only five. These can not be change through XML. Only Presentations can be add, update or delete in predefined FeatureSets. Old logic will work for all other tags, but when new Presentation is added nowonwards we need to add FeatureID also for which FeatureSet it belongs.
    /// For HERE_FEATURE Tag insert FeatureID in CollectionMaster.
    if([fileType isEqualToString:mediaPresentation]) {
        
        // Get FeatureID for current FeatureName...
        int aIntFeatureId = [self getFeatureIdFromFeatureMaster:strFeatureName];
        if (aIntFeatureId != -1) {
            // Get Presentations for current FeatureName...
            aStrAllGroups = [NSString stringWithFormat:@"select * from CollectionMaster where fileType = \'%@\' AND featureID = %d Order By isUserDefine Asc, fileOrder",fileType, aIntFeatureId];
        }
    }

    NSMutableArray *aMutArrGroups = [[Database sharedDatabase] getAllDataForQuery:aStrAllGroups];
    
    // Insert data at specific index...
    [aMutArrGroups insertObject:aDictCollData atIndex:intCollectionIndex];
    
    // Now update fileOrder for all groups and add new data...
    for (int aIntIndex = 0; aIntIndex < aMutArrGroups.count; aIntIndex++)
    {
        if (aIntIndex == intCollectionIndex)
        {
            // Add new group data...
            [self insertCollectionDeatilWithName:[aMutArrGroups[aIntIndex] valueForKey:@"collectionName"] DisplayName:[aMutArrGroups[aIntIndex] valueForKey:@"collectionDisplayName"] withFiletype:fileType];
        }
        else
        {
            int aIntCollId = [[aMutArrGroups[aIntIndex] valueForKey:@"collectionID"] intValue];
            
            // Update fileOrder for other groups...(fileOrder will start from 1)
            [self updateFileOrderIndexIntoCollectionMaster:aIntCollId fileOrder:aIntIndex + 1 withFiletype:fileType];
        }
        
    }

}

- (void)updateFileOrderIndexIntoCollectionMaster:(int)aIntCollId fileOrder:(int)aIntFileOrder withFiletype:(NSString*)fileType
{
    NSString *aStrExists = [NSString stringWithFormat:@"select count(collectionID) from CollectionMaster where collectionID = %d",aIntCollId];
    
    NSMutableArray *aMutArrExists = [[Database sharedDatabase] getAllDataForQuery:aStrExists];
    
    if(aMutArrExists.count > 0)
    {
        if ([aMutArrExists[0][@"count(collectionID)"] intValue] >= 1) {
            
            // Data already exists, update fileOrder index...
            NSString *aStrUpdateQuery = [NSString stringWithFormat:@"update CollectionMaster set fileOrder = '%d' where collectionID = %d", aIntFileOrder, aIntCollId];
            
            
            //===================
            // Version 3.1 Changes
            //===================
            // Feature MVI file changes.
            /// Note: The new functionality is added for Presentations which is FeatureSets means Presentations are now part of FeatureSets.
            /// So FetureSets contain one or more presentations.
            /// Presentation can be added in Predefined FeatureSets.
            /// Most Important: FeatureSets are predefined only five. These can not be change through XML. Only Presentations can be add, update or delete in predefined FeatureSets. Old logic will work for all other tags, but when new Presentation is added nowonwards we need to add FeatureID also for which FeatureSet it belongs.
            /// For HERE_FEATURE Tag insert FeatureID in CollectionMaster.
            if([fileType isEqualToString:mediaPresentation]) {
                
                // Get FeatureID for current FeatureName...
                int aIntFeatureId = [self getFeatureIdFromFeatureMaster:strFeatureName];
                if (aIntFeatureId != -1) {
                    aStrUpdateQuery = [NSString stringWithFormat:@"update CollectionMaster set fileOrder = '%d', featureID = %d where collectionID = %d", aIntFileOrder, aIntFeatureId, aIntCollId];
                }
            }
            
            [[Database sharedDatabase] Update:aStrUpdateQuery];
            
        }
    }
}

- (void)updateIsPlayedIntoLocalFileMaster:(int)aIntFileId {
    
    // When in updates new group is created using existing Movies and Images, we need to show blue background for newly created group...
    // New Image or Movie is added than no issue but for existing Movies and Images we need to update "isPlayed" to "0"...
    
    // Data already exists, update isPlayed to "0"...
    NSString *aStrUpdateQuery = [NSString stringWithFormat:@"update LocalFileMaster set isPlayed = '0' where fileID = %d", aIntFileId];
    [[Database sharedDatabase] Update:aStrUpdateQuery];

    // To update only one file of group...
    boolIsNewGroup = FALSE;
}

#pragma mark Delete Collection Data Methods

- (void)deleteCollectionData:(int)aIntCollId {
    
    NSString *aStrGetData = [NSString stringWithFormat:@"select filesID from CollectionFilesMaster where collectionID = %d",aIntCollId];
    NSMutableArray *aMutArrData = [[Database sharedDatabase] getAllDataForQuery:aStrGetData];
    
    if(aMutArrData.count > 0)
    {
//        for (int index = 0; index < aMutArrData.count; index++) {
//
//            // First delete data from LocalFileMaster...
//            int aIntFileId = [[[aMutArrData objectAtIndex:index] valueForKey:@"filesID"] intValue];
//            [self deleteDataFromLocalFileMaster:aIntCollId fileId:aIntFileId];
//        }
        
        // Now delete data from CollectionFilesMaster.
        NSString *aStrDelete = [NSString stringWithFormat:@"Delete from CollectionFilesMaster where collectionID = %d",aIntCollId];
        NSLog(@"Delete=======\n%@\n",aStrDelete);
        
        [[Database sharedDatabase]Delete:aStrDelete];
    }
    
    // Now delete data from CollectionMaster.
    NSString *aStrDelete = [NSString stringWithFormat:@"Delete from CollectionMaster where collectionID = %d",aIntCollId];
    
    NSLog(@"Delete=======\n%@\n",aStrDelete);
    [[Database sharedDatabase] Delete:aStrDelete];

}

- (void)deleteDataFromLocalFileMaster:(int)aIntCollId fileId:(int)aIntFileId {
    
    NSString *aStrExists = [NSString stringWithFormat:@"select count(filesID) from CollectionFilesMaster where collectionID != %d AND filesID = %d",aIntCollId,aIntCollId];
    
    NSMutableArray *aMutArrExists = [[Database sharedDatabase] getAllDataForQuery:aStrExists];
    
    if(aMutArrExists.count > 0)
    {
        if ([aMutArrExists[0][@"count(filesID)"] intValue] == 0) {
            
            // If file is not used in any other group, delete data from LocalFileMaster...
            NSString *aStrDelete = [NSString stringWithFormat:@"Delete from LocalFileMaster where fileID = %d", aIntFileId];
            NSLog(@"Delete=======\n%@\n",aStrDelete);

            [[Database sharedDatabase]Delete:aStrDelete];
            
        }
    }

}

//===================
// Version 3.1 Changes
//===================
// Feature MVI file changes.

- (int)getFeatureIdFromFeatureMaster:(NSString *)aStrFeatureName
{
    
    int aIntResult;
    
    NSString *aStrGetId = [NSString stringWithFormat:@"select featureID from FeatureMaster where featureName = \'%@\'",aStrFeatureName];
    
    NSMutableArray *aMutArrResult = [[Database sharedDatabase] getAllDataForQuery:aStrGetId];

    if (aMutArrResult.count > 0) {
        aIntResult = [aMutArrResult[0][@"featureID"] intValue];
    } else {
        aIntResult = -1;
    }
    
    return aIntResult;
}

@end
