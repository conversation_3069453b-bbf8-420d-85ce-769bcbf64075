//
//  XMLParserUpdate.h
//  BIteFXproject
//
//  Created by indianic on 10/3/17.
//
//

#import <Foundation/Foundation.h>

@interface XMLParserUpdate : NSObject <NSXMLParserDelegate>{
    
    NSMutableString *currentElementValue;
    
    NSMutableString *strCollectionName;
    NSMutableString *strFeatureName;
    
    BOOL boolIsGroupOn;
    BOOL boolIsNewGroup;
    int aIntRegistered;
    int intOrderIndex;
    int intCollectionIndex;
    BOOL isExists;
    NSString *strCurrGroup;
}

- (XMLParserUpdate *) initXMLParserUpdate NS_DESIGNATED_INITIALIZER;

@end
