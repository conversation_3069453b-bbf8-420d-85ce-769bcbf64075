//
//  UIDevice(Identifier).h
//  UIDeviceAddition
//
//  Created by <PERSON> on 20.08.11.
//  Copyright 2011 Aurora Apps. All rights reserved.
//

#import <Foundation/Foundation.h>


@interface UIDevice (IdentifierAddition)

/*
 * @method uniqueDeviceIdentifier
 * @description use this method when you need a unique identifier in one app.
 * It generates a hash from the MAC-address in combination with the bundle identifier
 * of your app.
 */

@property (NS_NONATOMIC_IOSONLY, readonly, copy) NSString *uniqueDeviceIdentifier;

/*
 * @method uniqueGlobalDeviceIdentifier
 * @description use this method when you need a unique global identifier to track a device
 * with multiple apps. as example a advertising network will use this method to track the device
 * from different apps.
 * It generates a hash from the MAC-address only.
 */

@property (NS_NONATOMIC_IOSONLY, readonly, copy) NSString *uniqueGlobalDeviceIdentifier;

@end
