//
//  SearchBar.h
//  BIteFXproject
//
//  Created by indianic on 29/05/25.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface SearchBar : UIView <UITableViewDelegate, UITableViewDataSource, UITextFieldDelegate>{
    UserSubscribeStatus userSubStat;
}

@property (strong, nonatomic) IBOutlet UIView *contentView;
@property (strong, nonatomic) IBOutlet UIButton *btnFavUnFav;
@property (strong, nonatomic) IBOutlet UITextField *txtSearch;
@property (strong, nonatomic) IBOutlet UIButton *btnSearchClose;
@property (strong, nonatomic) IBOutlet UIImageView *imgSearchBase;
@property (strong, nonatomic) IBOutlet UIImageView *imgSearchClose;
@property (strong, nonatomic) IBOutlet UIImageView *imgFavUnFav;
@property (strong, nonatomic) UITableView *suggestionTableView;
@property (strong, nonatomic) UIControl *overlayView;
@property (strong, nonatomic) NSMutableArray<NSString *> *arrFilteredItems;
@property (strong, nonatomic) UIView *suggestionContainerView;
@property (strong, nonatomic) UIButton *btnHideKeyboard;

@end

NS_ASSUME_NONNULL_END
