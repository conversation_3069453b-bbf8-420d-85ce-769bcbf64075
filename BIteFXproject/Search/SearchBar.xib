<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="24093.7" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="ipad10_9rounded" orientation="portrait" layout="fullscreen" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="24053.1"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="SearchBar" customModule="BiteFX" customModuleProvider="target">
            <connections>
                <outlet property="btnFavUnFav" destination="kbF-2w-4tX" id="Ggo-MH-htw"/>
                <outlet property="btnSearchClose" destination="KZX-Y0-XIo" id="hDn-dN-iPJ"/>
                <outlet property="contentView" destination="iN0-l3-epB" id="z4t-It-cOj"/>
                <outlet property="imgFavUnFav" destination="6VO-XK-DOc" id="Mcb-iT-MZA"/>
                <outlet property="imgSearchBase" destination="x3F-Fy-YUt" id="UVY-ET-GvT"/>
                <outlet property="imgSearchClose" destination="w7j-GU-j2a" id="o3Z-kq-bgG"/>
                <outlet property="txtSearch" destination="Jh5-JI-bkC" id="sIZ-h9-Lai"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="iN0-l3-epB">
            <rect key="frame" x="0.0" y="0.0" width="794" height="120"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <stackView opaque="NO" contentMode="scaleToFill" distribution="fillProportionally" translatesAutoresizingMaskIntoConstraints="NO" id="wLA-Ag-atd">
                    <rect key="frame" x="0.0" y="0.0" width="794" height="120"/>
                    <subviews>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="kbF-2w-4tX">
                            <rect key="frame" x="0.0" y="0.0" width="136" height="120"/>
                            <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <state key="normal" title="Button"/>
                            <buttonConfiguration key="configuration" style="plain"/>
                        </button>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="searchbar_base.png" translatesAutoresizingMaskIntoConstraints="NO" id="x3F-Fy-YUt">
                            <rect key="frame" x="136" y="0.0" width="522" height="120"/>
                        </imageView>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="KZX-Y0-XIo">
                            <rect key="frame" x="658" y="0.0" width="136" height="120"/>
                            <state key="normal" title="Button"/>
                            <buttonConfiguration key="configuration" style="plain"/>
                        </button>
                    </subviews>
                    <constraints>
                        <constraint firstItem="KZX-Y0-XIo" firstAttribute="width" secondItem="kbF-2w-4tX" secondAttribute="width" id="6Er-A2-6sx"/>
                        <constraint firstItem="kbF-2w-4tX" firstAttribute="width" secondItem="x3F-Fy-YUt" secondAttribute="width" multiplier="0.26" id="RGm-kr-4WE"/>
                    </constraints>
                </stackView>
                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="searchBtn.png" highlightedImage="clearSearch.png" translatesAutoresizingMaskIntoConstraints="NO" id="w7j-GU-j2a">
                    <rect key="frame" x="658" y="0.0" width="136" height="120"/>
                </imageView>
                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="unFav.png" highlightedImage="fav.png" highlighted="YES" translatesAutoresizingMaskIntoConstraints="NO" id="6VO-XK-DOc">
                    <rect key="frame" x="5" y="5" width="126" height="110"/>
                </imageView>
                <textField opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="248" contentHorizontalAlignment="left" contentVerticalAlignment="center" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="Jh5-JI-bkC">
                    <rect key="frame" x="146" y="0.0" width="502" height="120"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                    <textInputTraits key="textInputTraits"/>
                    <connections>
                        <outlet property="delegate" destination="-1" id="BFw-Y5-BLR"/>
                    </connections>
                </textField>
            </subviews>
            <viewLayoutGuide key="safeArea" id="vUN-kp-3ea"/>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="Jh5-JI-bkC" firstAttribute="bottom" secondItem="x3F-Fy-YUt" secondAttribute="bottom" id="9Ba-px-ilx"/>
                <constraint firstItem="Jh5-JI-bkC" firstAttribute="top" secondItem="x3F-Fy-YUt" secondAttribute="top" id="9pd-lp-dXn"/>
                <constraint firstAttribute="trailing" secondItem="wLA-Ag-atd" secondAttribute="trailing" id="BMF-BU-FYZ"/>
                <constraint firstItem="6VO-XK-DOc" firstAttribute="trailing" secondItem="kbF-2w-4tX" secondAttribute="trailing" constant="-5" id="HjP-iQ-D0b"/>
                <constraint firstItem="w7j-GU-j2a" firstAttribute="top" secondItem="KZX-Y0-XIo" secondAttribute="top" id="Kkq-eK-RPH"/>
                <constraint firstItem="6VO-XK-DOc" firstAttribute="bottom" secondItem="kbF-2w-4tX" secondAttribute="bottom" constant="-5" id="MGi-mI-jOt"/>
                <constraint firstItem="wLA-Ag-atd" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" id="OCv-AU-jDs"/>
                <constraint firstItem="Jh5-JI-bkC" firstAttribute="trailing" secondItem="x3F-Fy-YUt" secondAttribute="trailing" constant="-10" id="WWt-9q-0cs"/>
                <constraint firstItem="6VO-XK-DOc" firstAttribute="leading" secondItem="kbF-2w-4tX" secondAttribute="leading" constant="5" id="aV0-zz-WQm"/>
                <constraint firstItem="w7j-GU-j2a" firstAttribute="trailing" secondItem="KZX-Y0-XIo" secondAttribute="trailing" id="bRi-XG-hNm"/>
                <constraint firstItem="wLA-Ag-atd" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" id="c6d-9e-Eoz"/>
                <constraint firstAttribute="bottom" secondItem="wLA-Ag-atd" secondAttribute="bottom" id="dqi-M0-9KE"/>
                <constraint firstItem="w7j-GU-j2a" firstAttribute="leading" secondItem="KZX-Y0-XIo" secondAttribute="leading" id="jyZ-2r-WdY"/>
                <constraint firstItem="w7j-GU-j2a" firstAttribute="bottom" secondItem="KZX-Y0-XIo" secondAttribute="bottom" id="nrt-0E-evn"/>
                <constraint firstItem="6VO-XK-DOc" firstAttribute="top" secondItem="kbF-2w-4tX" secondAttribute="top" constant="5" id="s2V-jZ-APY"/>
                <constraint firstItem="Jh5-JI-bkC" firstAttribute="leading" secondItem="x3F-Fy-YUt" secondAttribute="leading" constant="10" id="sE9-D6-6VI"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <point key="canvasLocation" x="307.3170731707317" y="240.5084745762712"/>
        </view>
    </objects>
    <resources>
        <image name="clearSearch.png" width="99" height="82"/>
        <image name="fav.png" width="72" height="72"/>
        <image name="searchBtn.png" width="87" height="72"/>
        <image name="searchbar_base.png" width="494" height="124"/>
        <image name="unFav.png" width="72" height="72"/>
    </resources>
</document>
