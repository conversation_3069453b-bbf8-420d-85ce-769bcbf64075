//
//  SearchBar.m
//  BIteFXproject
//
//  Created by indianic on 29/05/25.
//

#import "SearchBar.h"

@implementation SearchBar

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) [self commonInit];
    return self;
}

- (instancetype)initWithCoder:(NSCoder *)coder {
    self = [super initWithCoder:coder];
    if (self) [self commonInit];
    return self;
}

- (void)commonInit {
    [[NSBundle mainBundle] loadNibNamed:@"SearchBar" owner:self options:nil];
    self.contentView.frame = self.bounds;
    self.contentView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
    [self addSubview:self.contentView];
    
    self.arrFilteredItems = [NSMutableArray array];
    
    self.txtSearch.accessibilityIdentifier = @"txtSearch";
    [self.txtSearch setTintColor: [UIColor colorWithRed:(253.0/255.0) green:(204.0/255.0) blue:(86.0/255.0) alpha:1.0]];
    self.txtSearch.text = appDelegate.selectedObj;
    
    // Setup text field
    self.txtSearch.delegate = self;
    [self.txtSearch addTarget:self action:@selector(textFieldDidChange:) forControlEvents:UIControlEventEditingChanged];
    
    // Setup close button action
    [self.btnSearchClose addTarget:self action:@selector(btnSearchCloseTapped:) forControlEvents:UIControlEventTouchUpInside];
    
    // Add tap gesture to imgFavUnFav
    self.imgFavUnFav.userInteractionEnabled = YES;
    UITapGestureRecognizer *favTap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(btnFavUnFavTapped:)];
    [self.imgFavUnFav addGestureRecognizer:favTap];
    
    // Setup table view
    self.suggestionTableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
    self.suggestionTableView.delegate = self;
    self.suggestionTableView.dataSource = self;
    self.suggestionTableView.hidden = YES;
    self.suggestionTableView.layer.borderColor = UIColor.lightGrayColor.CGColor;
    self.suggestionTableView.layer.borderWidth = 1.0;
    self.suggestionTableView.scrollEnabled = NO;
    
    // Set background image for dropdown
    UIImageView *bgImageView = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"searchbar_base.png"]];
    bgImageView.contentMode = UIViewContentModeScaleToFill;
    self.suggestionTableView.backgroundView = bgImageView;
    self.suggestionTableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    self.suggestionTableView.allowsSelection = YES;
    self.suggestionTableView.userInteractionEnabled = YES;
    [self addSubview:self.suggestionTableView];
    [self bringSubviewToFront:self.suggestionTableView];
    
    [self.imgFavUnFav setHighlighted:appDelegate.isFavourite];
}

#pragma mark - Buttons Action
- (IBAction)btnFavUnFavTapped:(id)sender {
    if (![[NSUserDefaults standardUserDefaults] boolForKey:@"Registered"]) {
        self.imgFavUnFav.highlighted = NO;
        return;
    }
    self.imgFavUnFav.highlighted = !self.imgFavUnFav.highlighted;
    int isFavourite = self.imgFavUnFav.highlighted ? 1 : 0;
    [[NSNotificationCenter defaultCenter] postNotificationName:@"SearchBarFavUnFavTapped" object:nil userInfo:@{@"isFavourite": @(isFavourite)}];
}

- (IBAction)btnSearchCloseTapped:(UIButton *)sender {
    if ([self.txtSearch.text isEqualToString:@""]) {
        return;
    }
    if (appDelegate.selectedObj != nil) {
        appDelegate.selectedObj = nil;
        self.txtSearch.text = @"";
        [self filterAllAppDelegateArraysWithSearchText:@""];
        [self clearAllSearchFilters];
        [self refreshSuggestionsTable];
        [self hideSuggestions:YES];
        [[NSNotificationCenter defaultCenter] postNotificationName:@"SearchBarFilteredItemsEmptyNotification" object:nil];
        [self.imgSearchClose setHighlighted:YES];
        return;
    }
    if ([self.imgSearchClose isHighlighted]) {
        self.txtSearch.text = @"";
        [self clearAllSearchFilters];
        [self refreshSuggestionsTable];
        self.suggestionTableView.hidden = YES;
        [self filterAllAppDelegateArraysWithSearchText:@""];
        
        NSString *userInfo = @"";
        [[NSNotificationCenter defaultCenter] postNotificationName:@"SearchDidSelectObject"
                                                            object:nil
                                                          userInfo:@{@"selectedObject": userInfo}];
    } else {
        [self.txtSearch becomeFirstResponder];
    }
}

#pragma mark - UITextFieldDelegate

- (void)textFieldDidBeginEditing:(UITextField *)textField {
    [self.imgSearchClose setHighlighted:YES];
    UIWindow *window = UIApplication.sharedApplication.keyWindow;
    BOOL shouldShow = (self.arrFilteredItems.count != 0);
    if (self.txtSearch.text.length == 0) {
        self.arrFilteredItems = [NSMutableArray array]; // Empty suggestions
        [self refreshSuggestionsTable];
        [self hideSuggestions:YES];
        return;
    }
    if (self.txtSearch.text.length >= 3) {
        NSString *query = self.txtSearch.text.lowercaseString ?: @"";
        [self filterAllAppDelegateArraysWithSearchText:query];
        self.arrFilteredItems = [[self allFileShortDispNamesFromAppDelegate] mutableCopy];
        [self refreshSuggestionsTable];
        [self updateSuggestionTableFrame];
        if (self.arrFilteredItems.count > 0) {
            self.suggestionTableView.hidden = NO;
        } else {
            [self hideSuggestions:YES];
        }
        return;
    }
    self.arrFilteredItems = [NSMutableArray array];
    [self refreshSuggestionsTable];
    [self hideSuggestions:YES];

    if (shouldShow) {
        // Add overlay
        if (!self.overlayView) {
            self.overlayView = [[UIControl alloc] initWithFrame:CGRectMake(0, 50, window.frame.size.width, window.frame.size.height)];
            self.overlayView.backgroundColor = [UIColor colorWithWhite:0 alpha:0.01];
            [self.overlayView addTarget:self action:@selector(hideSuggestions) forControlEvents:UIControlEventTouchUpInside];
            
            self.btnHideKeyboard = [[UIButton alloc] initWithFrame:window.bounds];
            self.btnHideKeyboard.backgroundColor = [UIColor clearColor];
            [self.btnHideKeyboard addTarget:self action:@selector(hideKeyboard) forControlEvents:UIControlEventTouchUpInside];
        }
        if (self.overlayView.superview != window) {
            [self.overlayView removeFromSuperview];
            [window addSubview:self.overlayView];
        }
        [window bringSubviewToFront:self.overlayView];
        // Add table view
        if (self.suggestionTableView.superview != window) {
            [self.suggestionTableView removeFromSuperview];
            [self.overlayView addSubview:self.btnHideKeyboard];
            [self.overlayView addSubview:self.suggestionTableView];
        }
        self.suggestionTableView.hidden = NO;
    } else {
        [self hideSuggestions:YES];
    }
    [self refreshSuggestionsTable];
}

- (void)textFieldDidEndEditing:(UITextField *)textField {
    [self.imgSearchClose setHighlighted:NO];
    [self refreshSuggestionsTable];
    [self hideSuggestions:NO];
    if ([self.txtSearch.text isEqualToString:@""]) {
        [[NSNotificationCenter defaultCenter] postNotificationName:@"ClearSearchPresentation" object:nil];
    }
}

- (void)textFieldDidChange:(UITextField *)textField {
    NSLog(@"textField.text>>> %@", textField.text);
    
    NSString *query = textField.text.lowercaseString ?: @"";
    if ([query isEqualToString:@""]) {
        [self filterAllAppDelegateArraysWithSearchText:@""];
        
        NSString *userInfo = @"";
        [[NSNotificationCenter defaultCenter] postNotificationName:@"SearchDidSelectObject"
                                                            object:nil
                                                          userInfo:@{@"selectedObject": userInfo}];
        [self.imgSearchClose setHighlighted:YES];
    } else {
        
        if (query.length < 3) {
            [self filterAllAppDelegateArraysWithSearchText:@""];
            [self clearAllSearchFilters];
            [self refreshSuggestionsTable];
            [self hideSuggestions:YES];
            [[NSNotificationCenter defaultCenter] postNotificationName:@"SearchBarFilteredItemsEmptyNotification" object:nil];
            return;
        } else  {
            [self filterAllAppDelegateArraysWithSearchText:query];
            self.arrFilteredItems = [[self allFileShortDispNamesFromAppDelegate] mutableCopy];
            NSLog(@"arrFilteredItems: %@", self.arrFilteredItems);
            [self updateSuggestionTableFrame];
            UIWindow *window = UIApplication.sharedApplication.keyWindow;
            BOOL shouldShow = (self.arrFilteredItems.count != 0);
            if (!shouldShow) {
                [[NSNotificationCenter defaultCenter] postNotificationName:@"SearchBarFilteredItemsEmptyNotification" object:nil];
            }
            if (shouldShow) {
                // Add overlay
                if (!self.overlayView) {
                    self.overlayView = [[UIControl alloc] initWithFrame:CGRectMake(0, 50, window.frame.size.width, window.frame.size.height)];
                    self.overlayView.backgroundColor = [UIColor colorWithWhite:0 alpha:0.01];
                    [self.overlayView addTarget:self action:@selector(hideSuggestions) forControlEvents:UIControlEventTouchUpInside];
                    
                    self.btnHideKeyboard = [[UIButton alloc] initWithFrame:window.bounds];
                    self.btnHideKeyboard.backgroundColor = [UIColor clearColor];
                    [self.btnHideKeyboard addTarget:self action:@selector(hideKeyboard) forControlEvents:UIControlEventTouchUpInside];
                }
                if (self.overlayView.superview != window) {
                    [self.overlayView removeFromSuperview];
                    [window addSubview:self.overlayView];
                }
                [window bringSubviewToFront:self.overlayView];
                
                // Add table view
                if (self.suggestionTableView.superview != window) {
                    [self.overlayView addSubview:self.btnHideKeyboard];
                    [self.suggestionTableView removeFromSuperview];
                    [self.overlayView addSubview:self.suggestionTableView];
                }
                self.suggestionTableView.hidden = NO;
                
            } else {
                [self hideSuggestions:NO];
            }
            [self refreshSuggestionsTable];
        }
    }
}

- (void)filterAllAppDelegateArraysWithSearchText:(NSString *)searchText {
    NSString *lowerSearch = [searchText lowercaseString];
    
    // Always search from the original arrays (the copies)
    NSArray* (^filterArray)(NSArray*) = ^NSArray* (NSArray *array) {
        NSMutableArray *filteredCollections = [NSMutableArray array];
        for (NSArray *collection in array) {
            NSMutableArray *arrFilteredItems = [NSMutableArray array];
            for (NSDictionary *item in collection) {
                NSString *shortName = [item[@"fileShortDispName"] lowercaseString];
                if ((!searchText.length) || [shortName containsString:lowerSearch]) {
                    [arrFilteredItems addObject:item];
                }
            }
            if (arrFilteredItems.count > 0) {
                [filteredCollections addObject:arrFilteredItems];
            }
        }
        return filteredCollections;
    };
    
    // Use the original copies, not the possibly filtered arrays
    [self updateAppDelegateSearchArraysWithFilter:^NSArray* (NSArray *array) {
        if (array == appDelegate.arrImage) {
            return filterArray(appDelegate.arrImageCopy ?: @[]);
        } else if (array == appDelegate.arrMovie) {
            return filterArray(appDelegate.arrMovieCopy ?: @[]);
        } else if (array == appDelegate.arrPresentation) {
            return filterArray(appDelegate.arrPresentationCopy ?: @[]);
        } else {
            return filterArray(array);
        }
    }];
}

- (NSArray<NSString *> *)allFileShortDispNamesFromAppDelegate {
    AppDelegate *appDelegate = (AppDelegate *)[[UIApplication sharedApplication] delegate];
    NSMutableArray<NSString *> *allNames = [NSMutableArray array];
    
    NSArray *allArrays = @[appDelegate.arrSearchImage ?: @[], appDelegate.arrSearchMovie ?: @[], appDelegate.arrSearchPresentation ?: @[]];
    for (NSArray *collectionArray in allArrays) {
        for (NSArray *collection in collectionArray) {
            for (NSDictionary *item in collection) {
                NSString *dispName = item[@"fileShortDispName"];
                if (dispName && [dispName isKindOfClass:[NSString class]]) {
                    NSString *cleanDispName = [[dispName componentsSeparatedByCharactersInSet:[NSCharacterSet newlineCharacterSet]] componentsJoinedByString:@" "];
                    [allNames addObject:cleanDispName];
                }
            }
        }
    }
    NSOrderedSet *orderedSet = [NSOrderedSet orderedSetWithArray:allNames];
    NSArray *uniqueNames = [orderedSet array];
    if (uniqueNames.count > 6) {
        return [uniqueNames subarrayWithRange:NSMakeRange(0, 6)];
    }
    return uniqueNames;
}

#pragma mark - Dynamic Frame Update

- (void)updateSuggestionTableFrame {
    UIWindow *window = UIApplication.sharedApplication.keyWindow;
    CGRect tfFrameInWindow = [self.txtSearch convertRect:self.txtSearch.bounds toView:window];
    CGFloat rowHeight = 34.0;
    CGFloat tableHeight = (self.arrFilteredItems.count * rowHeight) + 8;
    
    self.suggestionTableView.frame = CGRectMake(
                                                tfFrameInWindow.origin.x - 10,
                                                CGRectGetMaxY(tfFrameInWindow) - 50,
                                                self.imgSearchBase.frame.size.width + self.imgSearchClose.frame.size.width,
                                                tableHeight
                                                );
}

#pragma mark - UITableView Delegates

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.arrFilteredItems.count;
}

-(CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return  34.0;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    static NSString *cellId = @"SuggestionCell";
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:cellId];
    if (!cell) {
        cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:cellId];
        
        UIImageView *bgImageView = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"searchbar_base.png"]];
        bgImageView.contentMode = UIViewContentModeScaleToFill;
        cell.backgroundView = bgImageView;
        
        UIView *selectedBgView = [[UIView alloc] initWithFrame:cell.contentView.bounds];
        selectedBgView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
        selectedBgView.backgroundColor = [UIColor colorWithRed:(253.0/255.0) green:(204.0/255.0) blue:(86.0/255.0) alpha:1.0];
        cell.selectedBackgroundView = selectedBgView;
    }
    
    cell.textLabel.text = self.arrFilteredItems[indexPath.row];
    [cell.textLabel setFont:[UIFont systemFontOfSize:14.0]];
    CGRect labelFrame = cell.textLabel.frame;
    labelFrame.origin.y = (cell.contentView.frame.size.height - labelFrame.size.height) / 2.0;
    cell.textLabel.frame = labelFrame;
    cell.textLabel.backgroundColor = UIColor.clearColor;
    cell.textLabel.textColor = UIColor.blackColor;
    return cell;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.15 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [tableView deselectRowAtIndexPath:indexPath animated:YES];
    });
    
    self.txtSearch.text = self.arrFilteredItems[indexPath.row];
    [self hideSuggestions:YES];
    [self.txtSearch resignFirstResponder];
    
    NSString *userInfo = self.arrFilteredItems[indexPath.row];
    [[NSNotificationCenter defaultCenter] postNotificationName:@"SearchDidSelectObject"
                                                        object:nil
                                                      userInfo:@{@"selectedObject": userInfo}];
}

- (void)hideSuggestions:(BOOL)isClear {
    self.suggestionTableView.hidden = YES;
    if (self.overlayView && self.overlayView.superview) {
        [self.overlayView removeFromSuperview];
    }
    if (isClear) {
        if (![self.txtSearch.text isEqualToString:@""] ) {
            [self filterAllAppDelegateArraysWithSearchText:@""];
        }
    }
}

- (void)hideKeyboard {
    [self.txtSearch resignFirstResponder];
    if (self.overlayView && self.overlayView.superview) {
        [self.overlayView removeFromSuperview];
    }
}

- (void)refreshSuggestionsTable {
    [self.suggestionTableView reloadData];
}

- (void)updateAppDelegateSearchArraysWithFilter:(NSArray* (^)(NSArray*))filterArray {
    
    NSArray *filteredImages = filterArray(appDelegate.arrImageCopy);
    NSArray *filteredMovies = filterArray(appDelegate.arrMovieCopy);
    NSArray *filteredPresentations = filterArray(appDelegate.arrPresentationCopy);
    
    appDelegate.arrSearchImage = [[NSMutableArray alloc] init];
    for (id obj in filteredImages) {
        [appDelegate.arrSearchImage addObject:[obj copy]];
    }
    
    appDelegate.arrSearchMovie = [[NSMutableArray alloc] init];
    for (id obj in filteredMovies) {
        [appDelegate.arrSearchMovie addObject:[obj copy]];
    }
    
    appDelegate.arrSearchPresentation = [[NSMutableArray alloc] init];
    for (id obj in filteredPresentations) {
        [appDelegate.arrSearchPresentation addObject:[obj copy]];
    }
    
    BOOL hasResults = (self.arrFilteredItems.count > 0);
    NSDictionary *userInfo = @{ @"hasResults": @(hasResults),
                                @"arrFilteredItems": self.arrFilteredItems};
    [[NSNotificationCenter defaultCenter] postNotificationName:@"SearchArraysDidUpdateNotification"
                                                        object:nil
                                                      userInfo:userInfo];
}

- (void)clearAllSearchFilters {
    [self.arrFilteredItems removeAllObjects];
    [appDelegate.arrSearchImage removeAllObjects];
    [appDelegate.arrSearchMovie removeAllObjects];
    [appDelegate.arrSearchPresentation removeAllObjects];
}

@end
