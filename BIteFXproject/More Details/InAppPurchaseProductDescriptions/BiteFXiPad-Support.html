<html>
<head>
<Link href="dt1.css" rel="stylesheet" type="text/css"/>
</head>
<body class="RHS">
<H1>Support for BiteFX</H1>
<p>If you need support for BiteFX for iPad, you can contact us directly by:</p>
<ul>
<li>Phone at: ******-582-1189, or</li>
<li>Email to: <EMAIL>)</li>
</ul>
<p>We'll be delighted to help you.</p>

<h3>Possible Issue When Purchasing</h3>
<p>Late in the release cycle for version 3.1 we found that the iPad system required the user to accept new Apple Media Services Terms and Conditions to complete the purchase of the autorenewable subscription.<br>
Tapping OK to the dialog informing you of the new terms, should display a dialog with the new terms allowing you to accept them.<br>
This worked fine for most of our testers.<br>
However, on one of our test iPads, tapping OK displayed a "Cannot Connect" message and, on canceling that dialog, BiteFX hung - a spinning "doing something" circle was displayed and the app was non-responsive.<br>
We have been unabled to reproduce this behavior in our development environments.<br>
<b>If you encounter this behavior</b>, you need to force the BiteFX app to close.<br>
To force the app to close:<br>
<ul>
<li>Swipe up slowly from the bottom of the scren.<br>
You'll see the app reduce in size.</li>
<li>Take your finger off the screen, and you'll see small versions of all the apps that are running.</li>
<li>Swipe up on the BiteFX app<br>
This forces the app to close.</li>
</ul>
In our testing the Apple Media Services Terms and Conditions displayed immediately the app closed.<br>
Agreeing to the new terms allowed BiteFX to be restarted and the purchase to be completed without interruption.</p>

</body>
</html>
