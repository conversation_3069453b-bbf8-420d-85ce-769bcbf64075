a {text-decoration: none; color: #5a6c7f;}
a:link { color: #5a6c7f;}
a:hover { color: #5a6c7f;text-decoration: underline;}

body {
overflow: auto;
frame: box;
border: 2px;
indent: 5px;
/* background-color: #5a6c7f; */
background-color: #fff;
text-decoration: none;
font: 14px arial, verdana, helvetica, sans-serif;;
color: #000;
}

.image {
	float: left;
	padding: 0 10px 0 0;
}
.fourCol {
	float: left;
	width: 160px;
	padding: 0 10px 15px 0;
}
.fourCol .description {
	font-size: 12px;
}
.end {
	clear: both;
	font-size: .1em;
	line-height: .1em;
}


.blank {
overflow: auto;
background-color: #fff;
border: none;
}

.BestBite {
background-color: #fff;
}

.WrongBite {
background-color: #fff;
}

.copyright {
text-align: right;
font: 10px arial, verdana, helvetica, sans-serif;
overflow: hidden;
vertical-align: top;
}

.star {
color: #ff1f7f;
font: 20px times;
}

.RHS {
background-color: #fff;
font: 14px arial, verdana, helvetica, sans-serif;
padding-top: 10px
}

.BackButton {
text-align: center;
vertical-align: top;
font: 16px arial, verdana, helvetica, sans-serif;;
background-color: rgb(9,204,153);
color: #5a6c7f;
}

.Body, .copyright, .BackButton, .Contents, .SubItem, .SubSubItem, .topleft, .BoxTitle {
font-family: Arial, Verdana, Helvetica, sans-serif;
}

.Contents, .SubItem, .SubSubItem, .topleft {
font: bold arial, verdana, helvetica, sans-serif;
}	

.offside {
font: bold arial, verdana, helvetica, sans-serif;
color: rgb(200,173,242);
}

.BestBiteColHeads, .WrongBiteColHeads { 
font: 18px arial, verdana, helvetica, sans-serif;
text-decoration: underline;
color: black;
}

.BestBiteColHeads { 
color: #ffffff;
}

.WrongBiteColHeads { 
color: #ffffff;
}

.SubItem { 
font: 16px arial, verdana, helvetica, sans-serif;
text-indent: 15px;
}

.SubSubItem {
font: 14px arial, verdana, helvetica, sans-serif;
text-indent: 25px;
}

.Back {
font: 20px arial, verdana, helvetica, sans-serif;
margin-top: 20px;
}

.BulletPoint {
font: 14px arial, verdana, helvetica, sans-serif;
margin-top: 4px;
margin-bottom: 7px;
margin-left: -20px;
}

.SubBulletPoint {
font: 14px arial, verdana, helvetica, sans-serif;
margin-bottom: 7px;
margin-left: 20px;
}

.SubNumberPoint {
font: 16px arial, verdana, helvetica, sans-serif;
margin-bottom: 7px;
margin-left: 25px;
}

.BoxTitle {
font: bold 12px arial, verdana, helvetica, sans-serif;
text-indent: 10px;
color: #ffffff;
}

.BestBiteTableItem, .WrongBiteTableItem {
font: bold 15px arial, verdana, helvetica, sans-serif;
}

.BestBiteTableItem {
color: #000;
background-color: rgb(153,102,255);
}

.WrongBiteTableITem {
color: #000;
background-color: rgb(204,102,255);
}

H1 {
	font: bold 25px arial, verdana, helvetica, sans-serif;;
	color: #5a6c7f;
	text-align: left;
}

h2 {
	font: bold 18px arial, verdana, helvetica, sans-serif;;
	color: #5a6c7f;
	text-align: left;
	text-transform: uppercase;
}	

.sprhs {
text-align: right;
}

.topleft {
vertical-align: bottom;
font-weight: bold;
color: #5a6c7f;
text-align: center;
font-size: 32px;
}

OL.sublist {
list-style-type: lower-roman;
}
