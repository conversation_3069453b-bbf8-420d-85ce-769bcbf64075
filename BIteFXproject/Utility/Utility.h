//
//  Utility.h
//  BIteFXproject
//
//  Created by indianic on 6/12/17.
//
//

#import <Foundation/Foundation.h>

// Callback for ImagePicker selected image...

typedef void(^callBackFromImagePicker)(UIImage *selectedImage);

@interface Utility : NSObject <UIImagePickerControllerDelegate, UINavigationControllerDelegate>

// Callback for ImagePicker selected image...
@property (nonatomic,strong) callBackFromImagePicker callBackFromImagePicker;

// Get singlton object of this class...
+ (Utility *)sharedInstance;

// InApp Purchase Receipt Methods
+ (NSString *)getInAppPurchaseReceipt;

// UIImage Methods...
// Get image for new size...
+ (UIImage *)imageByScalingToSize:(CGSize)aCgSizeTarget forImage:(UIImage *)aImgSource;

// Compress image...
+ (NSData *)compressImageData:(UIImage *)aImg;

// UIImagePickerController Methods...
- (void)showImagePickerInViewController:(UIViewController *)aCurrentVC withSourceType:(int)aIntSourceType andCompletionBlock:(callBackFromImagePicker)completionBlock;

@end
