//
//  Utility.m
//  BIteFXproject
//
//  Created by indianic on 6/12/17.
//
//

#import "Utility.h"

@implementation Utility

- (instancetype)init {
    self = [super init];
    if (self) {
        
    }
    
    return self;
    
}

// Get singlton object of this class...
+ (Utility *)sharedInstance {
    
    static dispatch_once_t onceToken;
    static Utility *sharedInstance;
    dispatch_once(&onceToken, ^{
        
        sharedInstance = [[self alloc] init];
        
    });
    
    return sharedInstance;
    
}

#pragma mark --------------------------------
#pragma mark InApp Purchase Receipt Methods

+ (NSString *)getInAppPurchaseReceipt {
    
    NSString *aStrReceipt = @"";
    NSURL *aUrlReceipt = [NSBundle mainBundle].appStoreReceiptURL;
    if ([[NSFileManager defaultManager] fileExistsAtPath:aUrlReceipt.path])
    {
        NSData *aDataReceipt = [NSData dataWithContentsOfURL:aUrlReceipt];
        aStrReceipt = [aDataReceipt base64EncodedStringWithOptions:0];
    }
    
    return aStrReceipt;
}

#pragma mark --------------------------------
#pragma mark UIImage Methods

+(UIImage*)imageWithImage: (UIImage*) sourceImage scaledToWidth: (float) i_width
{
    float oldWidth = sourceImage.size.width;
    float scaleFactor = i_width / oldWidth;
    
    float newHeight = sourceImage.size.height * scaleFactor;
    float newWidth = oldWidth * scaleFactor;
    
    UIGraphicsBeginImageContext(CGSizeMake(newWidth, newHeight));
    [sourceImage drawInRect:CGRectMake(0, 0, newWidth, newHeight)];
    UIImage *newImage = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return newImage;
}
+ (UIImage *)imageByScalingToSize:(CGSize)aCgSizeTarget forImage:(UIImage *)aImgSource {

    float oldWidth = aImgSource.size.width;
    float scaleFactor = aCgSizeTarget.width / oldWidth;
    
    float newHeight = aImgSource.size.height * scaleFactor;
    float newWidth = oldWidth * scaleFactor;

    CGSize newSize = CGSizeMake(newWidth, newHeight);
    
    UIImage *aImgGenerated = nil;
    UIGraphicsBeginImageContextWithOptions(newSize, NO, [UIScreen mainScreen].scale);
    [aImgSource drawInRect:CGRectMake(0, 0, newWidth, newHeight)];
    aImgGenerated = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    
    return aImgGenerated;
}

// Compress image...
+ (NSData *)compressImageData:(UIImage *)aImg {
    
    NSLog(@"%lu",(unsigned long)UIImageJPEGRepresentation(aImg, 1.0).length);
    
    //Compress Image
    CGFloat compression = 1.0f;
    CGFloat maxCompression = 0.1f;
    int maxFileSize = 500*1024;
    
    NSData *imageData = UIImageJPEGRepresentation(aImg, compression);
    
    while (imageData.length > maxFileSize && compression > maxCompression)
    {
        compression -= 0.1;
        imageData = UIImageJPEGRepresentation(aImg, compression);
    }
    
    NSLog(@"%lu",(unsigned long)imageData.length);
    
    return imageData;
}

#pragma mark --------------------------------
#pragma mark UIImagePickerController Methods

- (void)showImagePickerInViewController:(UIViewController *)aCurrentVC withSourceType:(int)aIntSourceType andCompletionBlock:(callBackFromImagePicker)completionBlock {
    
    self.callBackFromImagePicker = completionBlock;
    UIImagePickerController *aImgPickerController = [[UIImagePickerController alloc] init];
    aImgPickerController.delegate = self;
    aImgPickerController.allowsEditing = NO;
    
    switch (aIntSourceType) {
        case SOURCE_TYPE_CAMERA:
            aImgPickerController.sourceType = UIImagePickerControllerSourceTypeCamera;
            break;
            
        case SOURCE_TYPE_PHOTO_LIBRARY:
            aImgPickerController.sourceType = UIImagePickerControllerSourceTypePhotoLibrary;
            break;
            
        default:
            break;
    }
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [aCurrentVC presentViewController:aImgPickerController animated:YES completion:^{
            NSLog(@"success present picker");
        }];
    });
    
}

#pragma mark --------------------------------
#pragma mark UIImagePickerController Delegate Methods

- (void)imagePickerController:(UIImagePickerController *)picker didFinishPickingMediaWithInfo:(NSDictionary *)info {
    
    UIImage *image = info[UIImagePickerControllerOriginalImage];
    
    self.callBackFromImagePicker(image);
    [picker dismissViewControllerAnimated:YES completion:nil];
    
}

- (void)imagePickerControllerDidCancel:(UIImagePickerController *)picker {
    
    self.callBackFromImagePicker(nil);
    [picker dismissViewControllerAnimated:YES completion:nil];
}

@end
