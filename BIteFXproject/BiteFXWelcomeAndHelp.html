<!-- saved from url=(0022)http://internet.e-mail -->
<html>
<head>
<Link href="dt.css" rel="stylesheet" type="text/css"/>
</head>
<body class="RHS">
<p align="center"><sup>(To return to this page press F1 at any time.)</sup></p>
<H1>BiteFX Help</H1>
<p><a href="#V1.1">What's new in BiteFX V1.1/V1.2</a></p>
<p>When used as designed, BiteFX can greatly enhance your ability to explain the importance of correct occlusion. This panel gives you brief guidelines on using BiteFX (pronounced "Bite, F, X" a bit like "Bite effects").</p>
<ul>
<li class="BulletPoint">
For detailed instructions on using BiteFX see the User Guide (press <image src="Menu.png"> and select "User Guide") </li>
<li class="BulletPoint">
For detailed descriptions of the animations see the Animation Guide also available using the menu button <image src="Menu.png"> and selecting "Animation Guide". </li>
</ul>
<H2>Using the Product</H2>
<p>If this is your first time using BiteFX, we suggest you take a couple of minutes to read through this panel trying everything it suggests - i.e. if it says "click this button" you should try clicking that button!</p>
<ul>
<li class="BulletPoint">
<b>*Returning to these instructions!*</b><br>
Some of the following instructions will cause this window to close or be switched to a different function. We've put those instructions at the end of the list and marked them with a "<span class="star">*</span>". To return to this "BiteFX Help" panel, press F1 or press the menu button <image src="Menu.png"> and select Help. (As you've probably figured out, you go to the menu button to access parts of BiteFX that are not immediately visible in the main interface.)</li>
<li class="BulletPoint"> 
<b>To Play an animation</b><br>
Drag the slider <image src="SliderForInfo.jpg"> control, or<br> 
Use Ctrl + right arrow, Ctrl + left arrow, or<br>
Use the Play button <image src="PlayButton1.png"> (but note that when using Play it is more difficult to time your comments to coincide with what is happening in the animation)
</li>
<li class="BulletPoint">
<b>To make an animation play continuously</b><br>
Click the loop button <image src="LoopButtonForInfo.jpg"> once so that it stays down,<br>
Then click the play button <image src="PlayButton1.png">.<br>
This can be a good way to show repetitive motions, like bruxing, or if you want to make general comments while your client watches the animation.</li>
<li class="BulletPoint"> 
<b>To stop playing an animation</b><br>
If you use the Play button, you can stop the animation by:<br>
<ul class="SubBulletPoint"><li>
Clicking on the same button (now a stop button <image src="StopButtonForInfo.jpg" valign="bottom">), or</li>
<li>Pressing P, or</li>
<li>Pressing the space bar.<br>
The space-bar can always be used to press again the last button you pressed. So if you press play to start the animation, you can press space to stop it.</li></ul>
</li>
<li class="BulletPoint">
<b>To see photographs</b><br>
Click the "Pictures on/off" <image src="PictureButtonForInfo.jpg"> button.<br>
Hover over thumbnails (small pictures) in the horizontal bar to see a vertical bar of similar or associated pictures.<br>
Click on a thumbnail to display that picture.<br>
The thumbnails in the vertical bars display in the order: most recently used at the bottom to least recently used at the top.<br>
Return to the animation by clicking the "Pictures on/off" button again.</li>
<li class="BulletPoint">
<b>To see Info about the current animation</b><br>
There is information provided on every animation - written using terminology that your patients should understand, though we recommend that you, or one of your staff members, do most of the explaining and don't just leave your patients to figure it out for themselves!<br>
Click the info <image src="Info.png"> button once - it will "toggle" down: <image src="InfoButtonDownForInfo.jpg">.<br>
Click it again to close the Info pane.<br>
</li>
<li class="BulletPoint">
<b>To select an animation</b><br>
The animations are grouped in sequences.<br>
Sequences let you determine the order in which you show the animations - BiteFX provides a number of pre-configured sequences and you can create your own sequences.<br>
You select an animation by first displaying a bar of sequences (click on the down arrow to the right of the Sequence title at the bottom left of the BiteFX window <image src="SequenceSelectionForInfo.jpg">) and then hovering over one of the pictures representing the sequence displays another vertical bar containing the animations in that sequence. Click on the animation you want to use.<br>
You can also click on the picture representing the sequence to be taken to the first animation in that sequence.<br>
<li>Use the next &nbsp;<image src="NextButtonNew.png"> or previous <image src="PreviousAnimationButtonForInfo.jpg"> buttons to take you to the next/previous animation in the selected sequence</li>
</li>
<li class="BulletPoint">
<b>To explore BiteFX animations</b><br>
<ol class="SubNumberPoint">
<li>Open the Info pane so you can see explanations of the animations.</li>
<li>Close this Help pane so that the animation is a reasonable size.</li>
<li>Select animations in turn. Play them to check out what they show and read the Info pane to find out what we thought could be communicated using that animation.</li>
</ol>
<li class="BulletPoint">
<b>To configure various BiteFX behaviors<span class="star">*</span></b><br>
BiteFX provides you with the ability to configure various parts of its functions, such as:
	<ul class="SubBulletPoint">
		<li>The speed at which the animations are played</li>
		<li>The size of the thumbnails used to select the animations</li>
		<li>Whether you use picture thumbnails or textual titles to select the sequences and animations</li>
		<li>Whether the Info text scrolls automatically, and at what speed</li>
		<li>The size of text displayed</li>
	</ul>
You set these behaviors using the Options pane. The options pane displays in the same area as this Help pane, so if you want to return here, remember just press F1.<br> 
Display the Options pane by clicking on the menu button <image src="Menu.png"> and selecting "Options".<br>
Close the Options pane by clicking on the small close button <image src="SmallXButtonForInfo.jpg"> in the "Options" title bar.</li>
<li class="BulletPoint">
<b>To edit sequences<span class="star">*</span></b><br>
Select the menu button <image src="Menu.png"> and select "Sequence Editor".<br>
See the User Guide for more information on using the Sequence Editor.<br>
Close the Sequence Editor pane by clicking on the small close button <image src="SmallXButtonForInfo.jpg"> in the "Sequence Editor" title bar.</li>
<li class="BulletPoint">
<b>To add your own photos<span class="star">*</span></b><br>
Select the menu button <image src="Menu.png"> and select "Picture Editor".<br>
See the User Guide for more information on using the Picture Editor, but basically it works by you navigating to where your pictures are stored and then using drag and drop to add your pictures to the BiteFX picture bar.<br>
Close the Picture Editor pane by clicking on the small close button <image src="SmallXButtonForInfo.jpg"> in the "Picture Editor" title bar.</li>

</ul>
<p>We trust you enjoy using BiteFX and find that it enhances your business and your clients' understanding of occlusal concepts.</p>
<p>REMEMBER! You can return to this pane at any time by pressing F1 and that there are more detailed instructions in the User Guide.</p>
<a name=1.1 id="V1.1"></a>
<h2>What's New in BiteFX V1.1/V1.2</h2>
<p>BiteFX V1.1 adds the following features:
<ul>
<li class="BulletPoint"><b>22 new animations</b> including animations on using splints, equilibration, detailed effects on molars, interference effects, and the disappearing crown space</li>
<li class="BulletPoint"><b>Select animations using</b> thumbnail <b>pictures</b> rather than textual descriptions</li>
<li class="BulletPoint">Add <b>your own photos</b></li>
<li class="BulletPoint">All photos are viewable regardless of which animation is being played</li>
<li class="BulletPoint"><b>Photos grouped by theme</b>. Grouping can be rearranged using the Picture Editor.</li>
<li class="BulletPoint">Vary the size of thumbnail pictures used to select the photos and animations</li>
<li class="BulletPoint">Textual <b>descriptions added to photos</b> making it easier to identify the photo you want to use</li>
<li class="BulletPoint"><b>Info</b> pane <b>text rewritten</b> to be meaningful to patients and to give frame-by-frame descriptions of the animations</li>
<li class="BulletPoint">Command line option to <b>play</b> a <b>sequence of animations automatically</b></li>
<li class="BulletPoint">Info pane <b>text can scroll automatically</b> - if you want to set a sequence of animations playing automatically</li>
<li class="BulletPoint">Instructions on <b>using BiteFX with PowerPoint</b> added to User Guide</li>
<li class="BulletPoint">Icons on "Forward one frame" and "Back one frame" buttons made more obvious.</li>
<li class="BulletPoint">Help displayed in a different pane to the Info pane (removing possible confusion between the Info and Help panes)</li>
<li class="BulletPoint"><b>List of credits</b> added to About box - acknowledging the many people who have helped with the creation and enhancement of BiteFX</li>
</ul>
</p>
<p>BiteFX V1.2 adds the following features:
<ul>
<li class="BulletPoint">Support for <b>Windows Vista</b></li>
<li class="BulletPoint">Solution to challenges with QuickTime releases after 7.1.6</li>
<li class="BulletPoint">A revised copy protection system - required for the previous two items. This also meant new serial numbers had to be issued.</li>
</ul>
</body>
</html>