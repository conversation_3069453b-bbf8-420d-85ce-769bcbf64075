//
//

#import "PlayerView.h"
#import <AVFoundation/AVFoundation.h>

@implementation PlayerView
@synthesize player,queuePlayer;
@synthesize arrVideoList;
//for creating view programiticly

- (instancetype)initWithFrame:(CGRect)frame 
{
	NSLog(@"initWithFrame");
    if ((self = [super initWithFrame:frame])) {
        // Initialization code
    }
    return self;
}

//from nib
-(instancetype)initWithCoder:(NSCoder *)aDecoder
{
	if ((self = [super initWithCoder:aDecoder]))
	{
		
	}
	NSLog(@"initWithCoder:%@",self);
	return self;	
}

+ (Class)layerClass
{
	
	return [AVPlayerLayer class];
	
}

- (AVPlayer*)player
{
	return ((AVPlayerLayer*)self.layer).player;
}

- (void)setPlayer:(AVPlayer*)playerView
{
	((AVPlayerLayer*)self.layer).player = playerView;
	
	((AVPlayerLayer*)self.layer).videoGravity = AVLayerVideoGravityResizeAspect;

}
-(void)setQueuePlayer:(AVQueuePlayer *)queuePlayer
{
    
}
/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/




-(NSInteger)getTimeOfVideo
{
    return 0;
}



@end
