//
//
//

#import <UIKit/UIKit.h>
#import <AVFoundation/AVFoundation.h>
@class AVPlayer;
@class AVQueuePlayer;

@interface PlayerView : UIView 
{
    AVPlayer *player;
    AVQueuePlayer *queuePlayer;    
    NSInteger currentIndex;
    NSInteger timeOfVideo;

}
@property (nonatomic, strong) AVPlayer* player;
@property (nonatomic, strong) AVQueuePlayer *queuePlayer;
@property (nonatomic, strong) NSMutableArray *arrVideoList;

@property (NS_NONATOMIC_IOSONLY, getter=getTimeOfVideo, readonly) NSInteger timeOfVideo;
@end
