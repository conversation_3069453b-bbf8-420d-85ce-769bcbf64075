/*
 <PERSON>, http://ericasadun.com
 iPhone Developer's Cookbook, 3.0 Edition
 BSD License, Use at your own risk
 */

#import "DownloadHelper.h"

#define DELEGATE_CALLBACK(X, Y) if (sharedInstance.delegate && [sharedInstance.delegate respondsToSelector:@selector(X)]) [sharedInstance.delegate performSelector:@selector(X) withObject:Y];
#define NUMBER(X) [NSNumber numberWithFloat:X]

static DownloadHelper *sharedInstance = nil;

@implementation DownloadHelper
@synthesize response;
@synthesize data;
@synthesize delegate;
@synthesize urlString;
@synthesize urlconnection;
@synthesize isDownloading;
@synthesize typeString;
@synthesize isDownloadFailed;


- (void) start {
    
	self.isDownloading = NO;
	
	NSURL *url = [NSURL URLWithString:self.urlString];
	if (!url) {
		NSString *reason = [NSString stringWithFormat:@"Could not create URL from string %@", self.urlString];
		DELEGATE_CALLBACK(dataDownloadFailed:, reason);
		return;
	}
	
	NSMutableURLRequest *theRequest = [NSMutableURLRequest requestWithURL:url cachePolicy:NSURLRequestReloadIgnoringLocalCacheData timeoutInterval:10000000];
    theRequest.timeoutInterval = 10000000;
    theRequest.HTTPMethod = @"POST";
    [theRequest setValue:@"application/x-www-form-urlencoded" forHTTPHeaderField:@"Content-Type"];
    
	if (!theRequest) {
        
		NSString *reason = [NSString stringWithFormat:@"Could not create URL request from string %@", self.urlString];
		DELEGATE_CALLBACK(dataDownloadFailed:, reason);
		return;
	}
    
	//if(self.urlconnection != nil) {
    if(self.urlconnection) {
        
        urlconnection= nil;
    }
    
    self.urlconnection = [[NSURLConnection alloc] initWithRequest:theRequest delegate:self];
    
	if (!self.urlconnection) {
        
		NSString *reason = [NSString stringWithFormat:@"URL connection failed for string %@", self.urlString];
		DELEGATE_CALLBACK(dataDownloadFailed:, reason);
		return;
	}
	
	self.isDownloading = YES;
	
	// Create the new data object
	self.data = [[NSMutableData alloc] init];
	self.response = nil;
	
	[self.urlconnection scheduleInRunLoop:[NSRunLoop currentRunLoop] forMode:NSRunLoopCommonModes];
}

- (void) cleanup {
    
    //NSLog(@"download data is clean");
	self.data = nil;
	self.response = nil;
    if (self.urlconnection != nil)
    {
    }
	self.urlconnection = nil;
	self.urlString = nil;
	self.isDownloading = NO;
    self.typeString = nil;
}

//- (void)connection:(NSURLConnection *)connection didReceiveResponse:(NSURLResponse *)aResponse {
- (void)connection:(NSURLConnection *)connection didReceiveResponse:(NSHTTPURLResponse *)aResponse {
    
	// store the response information
    NSInteger statusCode_ = aResponse.statusCode;
    
    if (statusCode_ == 200) {
        expectedLength = aResponse.expectedContentLength;
    }

	self.response = aResponse;
    //[self.data setLength:0];
	
	// Check for bad connection
	if (aResponse.expectedContentLength < 0) {
        
        //expectedLength = [aResponse expectedContentLength];
		NSString *reason = [NSString stringWithFormat:@"Invalid URL [%@]", self.urlString];
		DELEGATE_CALLBACK(dataDownloadFailed:, reason);
		[connection cancel];
		[self cleanup];
		return;
	}
	
	if (aResponse.suggestedFilename)
		DELEGATE_CALLBACK(didReceiveFilename:, [aResponse suggestedFilename]);
}

- (void)connection:(NSURLConnection *)connection didReceiveData:(NSData *)theData {
    
	// append the new data and update the delegate
    if (!self.data)
    {
        self.data = [NSMutableData data];
    }
    
    if(!appDelegate.isDownloadCancelled) {
        
        [self.data appendData:theData];
        
        if (self.response) {
            //expectedLength = [self.response expectedContentLength];
            float currentLength = self.data.length;
            //NSLog(@"expectedLength = %f and current %f %@",expectedLength,currentLength,self.data);
            float percent = currentLength / expectedLength;
            DELEGATE_CALLBACK(dataDownloadAtPercent:, @(percent));
        }
   
    } else {
        
        [connection cancel];
        self.isDownloading = NO;
    }
}

- (void)connectionDidFinishLoading:(NSURLConnection *)connection {
    
	// finished downloading the data, cleaning up
	self.response = nil;
	
	// Delegate is responsible for releasing data
    NSData *theData = nil;
    
    if (self.delegate) {
        
		theData = self.data;
		
	}
    //NSLog(@"Data expected length %.0f and totallength %d",expectedLength,[theData length]);
    //NSLog(@"%d", [theData length]);
    //NSLog(@"%f", expectedLength);
    
    if (expectedLength != theData.length) {
        
        [UIAlertController showAlertInViewController:AppDelegateobj.window.rootViewController withTitle:@"Error" message:@"There is some error with download data" cancelButtonTitle:@"OK" destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
        
        self.isDownloading = NO;
        [self cleanup];
        //DELEGATE_CALLBACK(reTryDownload:, @"Failed Connection");

        if ([delegate respondsToSelector:@selector(reTryDownload:)]) {
            [delegate reTryDownload:@"Failed Connection"];
        }
        
    } else {
        
        //DELEGATE_CALLBACK(didReceiveData:, theData);
        if ([delegate respondsToSelector:@selector(didReceiveData:)]) {
            [delegate didReceiveData:theData];
            //[self per]
        }
        [self.urlconnection unscheduleFromRunLoop:[NSRunLoop currentRunLoop] forMode:NSRunLoopCommonModes];
        //        [self cleanup];
        
    }
}


- (void)connection:(NSURLConnection *)connection didFailWithError:(NSError *)error {
    
	self.isDownloading = NO;
	NSLog(@"Error: Failed connection, %@", error.localizedDescription);
	//DELEGATE_CALLBACK(dataDownloadFailed:, @"Failed Connection");
    self.isDownloadFailed = 1;
    if ([delegate respondsToSelector:@selector(dataDownloadFailed:)]) {
        [delegate dataDownloadFailed:@"Failed Connection"];
        //[self per]
    }
	[self cleanup];
}

+ (DownloadHelper *) sharedInstance {
    
	if(!sharedInstance)  {
       sharedInstance = [[self alloc] init]; 
    }
    return sharedInstance;
}

+ (void) download:(NSString *) aURLString {
    
	if (sharedInstance.isDownloading) {
        
		NSLog(@"Error: Cannot start new download until current download finishes");
		DELEGATE_CALLBACK(dataDownloadFailed:, @"");
		return;
	}
	
	sharedInstance.urlString = aURLString;
	[sharedInstance start];
}

+ (void) cancel {
    
	if (sharedInstance.isDownloading) {
        [sharedInstance.urlconnection cancel];
    }
}

@end
