/*
 <PERSON>, http://ericasadun.com
 iPhone Developer's Cookbook, 3.0 Edition
 BSD License, Use at your own risk
 */

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>


@protocol DownloadHelperDelegate <NSObject>
@optional
- (void) didReceiveData: (NSData *) theData;
- (void) reTryDownload: (NSString *) reason;
- (void) didReceiveFilename: (NSString *) aName;
- (void) dataDownloadFailed: (NSString *) reason;
- (void) dataDownloadAtPercent: (NSNumber *) aPercent;
@end

@interface DownloadHelper : NSObject  <NSURLConnectionDataDelegate, NSURLConnectionDelegate> {
    
	NSURLResponse *response;
	NSMutableData *data;
	NSString *urlString;
    NSString *typeString;
	NSURLConnection *urlconnection;
    double expectedLength;
	id <DownloadHelperDelegate> delegate;
	BOOL isDownloading;
    long long bytesReceived;
    long long expectedBytes;
    int isDownloadFailed;
    
}
@property (strong) NSURLResponse *response;
@property (strong) NSURLConnection *urlconnection;
@property (strong) NSMutableData *data;
@property (strong) NSString *urlString;
@property (strong) NSString *typeString;
@property (strong) id delegate;
@property (assign) BOOL isDownloading;
@property (nonatomic, assign) int isDownloadFailed;

- (void) cleanup;
//+ (void) downloadCleanup;
+ (DownloadHelper *) sharedInstance;
+ (void) download:(NSString *) aURLString;
+ (void) cancel;
@end
