//
// Prefix header for all source files of the 'BIteFXproject' target in the 'BIteFXproject' project
//

#import <Availability.h>

#ifndef __IPHONE_4_0
#warning "This project uses features only available in iOS SDK 4.0 and later."
#endif

#ifdef __OBJC__
    #import <UIKit/UIKit.h>
    #import <Foundation/Foundation.h>

#import "AppDelegate.h"

#import <objc/runtime.h>
#import "UIDevice+IdentifierAddition.h"
#import "UIAlertController+QMAlertControl.h"

AppDelegate *appDelegate;
AppDelegate *AppDelegateobj;

#define IS_IOS7  ([[[UIDevice currentDevice] systemVersion] floatValue]>= 7.0)

#import "AppConstant.h"
#import "Utility.h"

#endif

//live server urls
//#define WebserviceURLuserTesting @"http://ipad.bitefx.com/index.php"
//#define DownloadURL @"http://ipad.bitefx.com/dl.php"
//#define DownloadFileURL @"http://ipad.bitefx.com/version7"

//Test server urls
#define WebserviceURLuserTesting @"http://test_ipad.bitefx.com/index.php"
#define DownloadURL @"http://test_ipad.bitefx.com/dl.php"
#define DownloadFileURL @"http://test_ipad.bitefx.com/version7"

// Version no is same as DownloadFileURL...
#define CURRENT_VERSION @"version7"
#define DEST_PATH	[NSHomeDirectory() stringByAppendingString:@"/Documents/BiteFXiPadFull/"]
#define Update_Path [NSHomeDirectory() stringByAppendingString:@"/Documents/Update0001/"]


#define NUMBERS @"0123456789"
#define ErrorTitle @"BiteFX"
#define NewVersionUpdateSuccessfully @"NewVersionUpdateSuccessfully"
#define ErrorMsg @"No internet connection found. Please try again!"
#define kReachabilityChangedNotification @"kNetworkReachabilityChangedNotification"

#define REMAIN_DOWNLOAD -150

int counter;

//#define UDIDForWS @"udid1234"
#define UDIDForWS [[UIDevice currentDevice] uniqueGlobalDeviceIdentifier]


