//
//  VideoDownloader.h
//  HPPiPadApp
//
//  Created by indianic on 05/09/11.
//  Copyright 2011 __MyCompanyName__. All rights reserved.
//

#import <Foundation/Foundation.h>

@protocol progressDelegate


-(void)progressBarmoved:(float)value;
@end


@protocol VideoDownloaderDelegate;
@protocol VideoDownloaderDelegate <NSObject>
- (void)videoDidLoadForTag:(int)tag;
- (void)downloadingDidLoadDataInKB:(NSInteger)KB forTag:(int)tag;
- (void)videoDownloadingFailedForTag:(int)tag;
-(void)responsedidreceive:(NSMutableData*)response_data forKey:(NSString*)reskey;
-(void)responsedidfail;

@end

@interface DownloadFile : NSObject
{
 
    NSFileHandle *file;
    int tag;
    __unsafe_unretained id<VideoDownloaderDelegate> delegate;
    NSURL *url;
    id<progressDelegate>progressBar;

    NSString *fileName;
    long long TatalSize;
    NSMutableData	*receiveddata;

}
@property (nonatomic)int tag;
@property (nonatomic,retain)NSURL *url;
@property (nonatomic, assign)id<VideoDownloaderDelegate> delegate;
@property(nonatomic,retain)id<progressDelegate>progressBar;



- (void)startDownload;
@end



