//
//  WebService.m
//  BIteFXproject
//
//  Created by indianic on 7/12/18.
//

#import "WebService.h"

@implementation WebService

- (void)initWithPostRequest:(NSMutableDictionary *)parameters controller:(UIViewController *)viewController callSilently:(BOOL)isCallSilently
    completion:(WebServiceCompletion)completionHandler {
    
    NSLog(@"------------ \n URL - %@ Body - %@ \n ------------\n",WebserviceURLuserTesting,parameters);
    
    NSString *aStrParameters = [[NSString alloc]initWithData:[NSJSONSerialization dataWithJSONObject:parameters options:kNilOptions error:nil] encoding:NSUTF8StringEncoding];
    
    NSString *aStrUrl = [NSString stringWithFormat:@"%@", WebserviceURLuserTesting];
    NSMutableURLRequest *aUrlRequest = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:aStrUrl]];
    
    // Encode parameters string...
    NSData *aData = [aStrParameters dataUsingEncoding:NSUTF8StringEncoding];
    
//    [aUrlRequest setTimeoutInterval:30];
    aUrlRequest.HTTPMethod = @"POST";
    [aUrlRequest setValue:@"application/json" forHTTPHeaderField:@"Content-Type"];
    aUrlRequest.HTTPBody = aData;
    
    //Async WS call...
    NSURLSession *aSession = [NSURLSession sharedSession];
    NSURLSessionDataTask *aDataTask = [aSession dataTaskWithRequest:aUrlRequest
                                            completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {
                                      
                                      if(!data || error) {
                                          NSLog(@"Connection Error : %@",error);
                                          dispatch_async(dispatch_get_main_queue(), ^{
                                              completionHandler(nil, error);
                                          });
                                          return ;
                                      }
                                      
                                      id object = [NSJSONSerialization JSONObjectWithData:data options:kNilOptions error:nil];
                                      if(!object){
                                          NSString *aSTR = [[NSString alloc]initWithData:data encoding:NSUTF8StringEncoding];
                                          dispatch_async(dispatch_get_main_queue(), ^{
                                              completionHandler(nil,[NSError errorWithDomain:aSTR code:300 userInfo:nil]);
                                          });
                                         return;
                                      }
                                      
                                      NSLog(@"Response ========== \n%@", object);
                                      NSDictionary *aDictResponse = [NSDictionary dictionaryWithDictionary:object];
                                                
                                      // Send response back to calling method...
                                      dispatch_async(dispatch_get_main_queue(), ^{
                                          completionHandler(aDictResponse ,nil);
                                      });
                                                
                                  }];
    
    [aDataTask resume];
}

@end
