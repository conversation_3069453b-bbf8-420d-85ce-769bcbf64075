//
//  CallWebService.h
//  She
//
//  Created by indianic on 5/19/10.
//  Copyright 2010 __MyCompanyName__. All rights reserved.
//

#import <Foundation/Foundation.h>
@protocol CallWebServiceDelegate;


@protocol progressBar

-(void)progressBarmoved:(float)value;
@end


@interface CallWebService : NSObject 
{
	@private 
		id <CallWebServiceDelegate> __weak delegate;

	NSString		*webServiceURL;
	NSMutableData	*receiveddata;
	NSString		*serviceKey;
    NSMutableURLRequest *request;
  //  double           totalLength;
    id<progressBar>progressDelegate;
    //add with filehandler
    NSFileHandle *file;
    NSString *fileName;
  //  int tag;
    long long TatalSize;

}

@property(nonatomic,weak)id <CallWebServiceDelegate> delegate;
@property(nonatomic,strong)NSString *webServiceURL;
@property(nonatomic,strong)NSString *serviceKey;
@property(nonatomic,strong)id<progressBar>progressDelegate;

- (instancetype) initWithURL:(NSString*)urlstring delegate:(id)WSdelegate args:(NSMutableDictionary*)parameters key:(NSString *)aStrKey NS_DESIGNATED_INITIALIZER;
-(instancetype)initWithURL:(NSString *)urlstring ;
@end


@protocol CallWebServiceDelegate <NSObject>

-(void)responsedidreceive:(NSMutableData*)response_data forKey:(NSString*)reskey;
-(void)responsedidfail;

@end
