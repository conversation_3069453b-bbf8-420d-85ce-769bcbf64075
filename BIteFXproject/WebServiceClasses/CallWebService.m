//
//  CallWebService.m
//  She
//
//  Created by indianic on 5/19/10.
//  Copyright 2010 __MyCompanyName__. All rights reserved.
//
#import "JSON.h"
#import "CallWebService.h"

@implementation CallWebService
@synthesize webServiceURL,delegate,serviceKey;
@synthesize progressDelegate;

- (instancetype) initWithURL:(NSString*)urlstring delegate:(id)WSdelegate args:(NSMutableDictionary*)parameters key:(NSString *)aStrKey {
	self = [super init];
    
	if (self != nil) {
        NSLog(@"------------ \n URL - %@ Body - %@ \n ------------\n",urlstring,parameters);
		NSString *tempURL=[urlstring copy];

//        webServiceURL=[tempURL stringByAddingPercentEscapesUsingEncoding:NSUTF8StringEncoding];
        webServiceURL=[NSString stringWithFormat:@"%s",tempURL.UTF8String];
		delegate=WSdelegate;
		receiveddata=[[NSMutableData alloc] init];
		NSURL *webURL=[NSURL URLWithString:webServiceURL];
        
        serviceKey = aStrKey;
		
		NSString *requestString = [NSString stringWithFormat:@"%@",[parameters JSONFragment],nil];
        
        //NSLog(@"%@",requestString);
//		requestString=[requestString stringByReplacingOccurrencesOfString:@"&" withString:@"%26"];
        const char *utfYourString = requestString.UTF8String;
        NSMutableData *requestData = [NSMutableData dataWithBytes:utfYourString length:strlen(utfYourString)];
		 request = [[NSMutableURLRequest alloc] initWithURL:webURL];
        request.HTTPMethod = @"POST";
       // [request setTimeoutInterval:60.0];
        [request setValue:@"application/json" forHTTPHeaderField:@"Content-type"];
		request.HTTPBody = requestData;
		[NSURLConnection  connectionWithRequest:request delegate:self];
        TatalSize=0;
	}
	return self;
}

- (void)connection:(NSURLConnection *)connection didReceiveResponse:(NSURLResponse *)response {
   
      /*  long long totalsize = [response expectedContentLength];
        NSLog(@"totalsize%lld %@",totalsize,[response description]);
      //  NSLog(@"totalLength = %F",filesize);
    NSLog(@"appdelegate%@",appDelegate);
        NSString *filename1;
        if(appDelegate.isUpdateDownloading)
        {
            filename1 = [[NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) objectAtIndex:0] stringByAppendingPathComponent:@"Update0001.zip"];
        }
        else
        {
            filename1 = [[NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) objectAtIndex:0] stringByAppendingPathComponent:@"BiteFXiPadFull.zip"];
        }
        NSError *error=nil;
        [[NSFileManager defaultManager]removeItemAtPath:filename1 error:&error];
        if(error)
            NSLog(@"didReceiveResponse = %@",[error localizedDescription]);
    
        [[NSFileManager defaultManager] createFileAtPath:filename1 contents:nil attributes:nil];
        file = [[NSFileHandle fileHandleForUpdatingAtPath:filename1] retain];
    
        if (file)
            [file seekToEndOfFile];
   */
}


- (void)connection:(NSURLConnection *)connection didReceiveData:(NSData *)data {
    
    [receiveddata appendData:data];
    /*if(!appDelegate.isDownloadCancelled)
    {
        if (file)
        [file seekToEndOfFile];
    
    [file writeData:data];
    TatalSize=TatalSize+[data length];
    NSFileManager *fileManager = [NSFileManager defaultManager];
    NSString *filename1 = [[NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) objectAtIndex:0] stringByAppendingPathComponent:fileName];
    NSError *error=nil;
	NSDictionary *fileAttributes = [fileManager attributesOfItemAtPath:filename1 error:&error];
    if(error){
        NSLog(@" error for Tag = %@ === %@ \n %@",fileName,[error localizedDescription],fileAttributes);
    }
    
    
    //NSInteger receivedLen = [data length];
    if(!appDelegate.isFullversionDownloading)
    {
        NSLog(@"append data");
        [receiveddata appendData:data];
    }
    
        float total = (float)TatalSize/(1000*1000);
        [progressDelegate progressBarmoved:total];
        NSLog(@"Received %f",total);
        
    }
    else
    {
        NSLog(@"connection cancel");
        [connection cancel];
        [[UIApplication sharedApplication]setNetworkActivityIndicatorVisible:FALSE];
    }*/
}

- (void)connectionDidFinishLoading:(NSURLConnection *)connection{

    //	if([delegate respondsToSelector:@selector(responsedidreceive:)]){
    //    [file closeFile];
    //    [file release];
    //    file = nil;
    //    if(!appDelegate.isFullversionDownloading||!appDelegate.isUpdateDownloading)
    //    {
    //		[delegate responsedidreceive:receiveddata forKey:serviceKey];
    //    }
    //
    //    else
    //    {
    //        [[NSUserDefaults standardUserDefaults]setInteger:1 forKey:@"Full"];
    //        [[NSUserDefaults standardUserDefaults]synchronize];
    //
    //        [[UIApplication sharedApplication]setNetworkActivityIndicatorVisible:FALSE];
    //        [delegate responsedidreceive:nil forKey:serviceKey];
    //    }
    
    
    [delegate responsedidreceive:receiveddata forKey:serviceKey];
}

- (void)connection:(NSURLConnection *)connection didFailWithError:(NSError *)error {

    //    NSLog(@"error = %@",error);
    //    [file closeFile];
    //    [file release];
    //    file = nil;
	if([delegate respondsToSelector:@selector(responsedidfail)]) {
		[delegate responsedidfail];
	}
}

- (instancetype)initWithURL:(NSString *)urlstring {
    
    NSMutableURLRequest *request1 = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:urlstring]];
    request1.timeoutInterval = 60.0;
    NSURLConnection *connection1 = [NSURLConnection connectionWithRequest:request1 delegate:self];
    NSLog(@"%@",connection1);
    return self;
}


@end
