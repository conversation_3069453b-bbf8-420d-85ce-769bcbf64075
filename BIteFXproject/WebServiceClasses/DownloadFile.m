//
//  VideoDownloader.m
//  HPPiPadApp
//
//  Created by indianic on 05/09/11.
//  Copyright 2011 __MyCompanyName__. All rights reserved.
//

#import "DownloadFile.h"


@implementation DownloadFile
@synthesize tag;
@synthesize url;
@synthesize delegate;
@synthesize progressBar;
- (void)startDownload
{
    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:url];
    request.timeoutInterval = 60.0;
    NSURLConnection *connection = [NSURLConnection connectionWithRequest:request delegate:self];
    NSLog(@"%@",connection);
//    NSArray *FileArr=[[url absoluteString] componentsSeparatedByString:@"/"];
//    fileName=[FileArr lastObject];
//    NSString *filePath = [[NSBundle mainBundle]pathForResource:@"DownloadStatus" ofType:@"plist"];
//    NSMutableDictionary* plistDict = [[NSMutableDictionary alloc] initWithContentsOfFile:filePath];
//    
//    [plistDict setObject:[NSNumber numberWithInt:0] forKey:fileName];
//    [plistDict writeToFile:filePath atomically: YES];

}
- (void)connection:(NSURLConnection *)connection didReceiveResponse:(NSURLResponse *)response
{
 /*   NSLog(@"total Length = %lld", [response expectedContentLength]);
    long long totalFileSize=[response expectedContentLength];
    NSString *filename = [[NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) objectAtIndex:0] stringByAppendingPathComponent:fileName]; 
    
    NSError *error=nil;
    [[NSFileManager defaultManager]removeItemAtPath:filename error:&error];
    if(error)
        NSLog(@"didReceiveResponse = %@",[error localizedDescription]);
    
                                                                                                                   
    
    [[NSFileManager defaultManager] createFileAtPath:filename contents:nil attributes:nil];
    file = [[NSFileHandle fileHandleForUpdatingAtPath:filename] retain];
    
    if (file)   
        [file seekToEndOfFile];*/
    
    long long totalsize = response.expectedContentLength;
    NSLog(@"totalsize%lld %@",totalsize,response.description);
    //  NSLog(@"totalLength = %F",filesize);
    
    NSString *filename1;
    if(appDelegate.isUpdateDownloading)
    {
        filename1 = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES)[0] stringByAppendingPathComponent:@"Update0001.zip"]; 
    }
    else 
    {
        filename1 = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES)[0] stringByAppendingPathComponent:@"BiteFXiPadFull"];
    }
    NSError *error=nil;
    [[NSFileManager defaultManager]removeItemAtPath:filename1 error:&error];
    if(error)
        NSLog(@"didReceiveResponse = %@",error.localizedDescription);
    
    [[NSFileManager defaultManager] createFileAtPath:filename1 contents:nil attributes:nil];
    file = [NSFileHandle fileHandleForUpdatingAtPath:filename1];
    
    if (file)   
        [file seekToEndOfFile];
    

}

- (void)connection:(NSURLConnection *)connection didReceiveData:(NSData *)data {
    
 /*   if (file) 
        [file seekToEndOfFile];
    
    [file writeData:data]; 

    NSFileManager *fileManager = [NSFileManager defaultManager];
    NSString *filename = [[NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) objectAtIndex:0] stringByAppendingPathComponent:fileName];  
    NSError *error=nil;
	NSDictionary *fileAttributes = [fileManager attributesOfItemAtPath:filename error:&error];
    if(error)
    {
//        NSLog(@"Data Length = %d",[data length]);
//        NSLog(@" error for Tag = %d === %@",self.tag,[error localizedDescription]);
    }
	if(fileAttributes != nil)
	{
		NSNumber *fileSize = [fileAttributes objectForKey:NSFileSize];
        [delegate downloadingDidLoadDataInKB:[fileSize intValue]/1000 forTag:self.tag];
		//NSLog(@"File size: %d kb", [fileSize intValue]/1000);
	}*/
    
    if(!appDelegate.isDownloadCancelled)
    {
        if (file) 
            [file seekToEndOfFile];
        
        [file writeData:data]; 
        TatalSize=TatalSize+data.length;
        NSFileManager *fileManager = [NSFileManager defaultManager];
        NSString *filename1 = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES)[0] stringByAppendingPathComponent:fileName];  
        NSError *error=nil;
        NSDictionary *fileAttributes = [fileManager attributesOfItemAtPath:filename1 error:&error];
        if(error){
            NSLog(@" error for Tag = %@ === %@ \n %@",fileName,error.localizedDescription,fileAttributes);
        }
        
        
        //NSInteger receivedLen = [data length];
        if(!appDelegate.isFullversionDownloading)
        {
            //NSLog(@"append data");
            [receiveddata appendData:data];
        }
        
        float total = (float)TatalSize/(1000*1000);
        [progressBar progressBarmoved:total];
        //NSLog(@"Received %f",total);
        
    }
    else
    {
        NSLog(@"connection cancel");
        [connection cancel];
        [[UIApplication sharedApplication]setNetworkActivityIndicatorVisible:FALSE];
    }

    
}
- (void)connectionDidFinishLoading:(NSURLConnection *)connection
{
    
   /* NSString *filePath = [[NSBundle mainBundle]pathForResource:@"DownloadStatus" ofType:@"plist"];
    NSMutableDictionary* plistDict = [[NSMutableDictionary alloc] initWithContentsOfFile:filePath];
    
    [plistDict setObject:[NSNumber numberWithInt:1] forKey:[NSString stringWithFormat:@"%d.mov",self.tag]];
    [plistDict writeToFile:filePath atomically: YES];
    
    [file closeFile]; 
    [delegate videoDidLoadForTag:self.tag];
    [file release];
    file = nil;*/
    
    [file closeFile]; 
    file = nil;
    if(!appDelegate.isFullversionDownloading||!appDelegate.isUpdateDownloading)
    {
		[delegate responsedidreceive:receiveddata forKey:nil];	
    }
    
    else
    {
        [[NSUserDefaults standardUserDefaults]setInteger:1 forKey:@"Full"];
        [[NSUserDefaults standardUserDefaults]synchronize];
        
        [[UIApplication sharedApplication]setNetworkActivityIndicatorVisible:FALSE];
        [delegate responsedidreceive:nil forKey:nil];
    }

}
- (void)connection:(NSURLConnection *)connection didFailWithError:(NSError *)error
{
    NSFileManager *fileManager = [NSFileManager defaultManager];
    NSString *filename = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES)[0] stringByAppendingPathComponent:fileName];  
    NSError *error1=nil;
    [fileManager removeItemAtPath:filename error:&error1];
    if(error1)
        NSLog(@"%@",error1.localizedDescription);
    [UIAlertController showAlertInViewController:AppDelegateobj.window.rootViewController withTitle:@"" message:error.localizedDescription cancelButtonTitle:@"OK" destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
    [delegate videoDownloadingFailedForTag:self.tag];
    
}

@end
