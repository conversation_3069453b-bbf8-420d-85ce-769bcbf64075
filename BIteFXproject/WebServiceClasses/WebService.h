//
//  WebService.h
//  BIteFXproject
//
//  Created by indianic on 7/12/18.
//

#import <Foundation/Foundation.h>

typedef void(^WebServiceCompletion)(id object, NSError* aError);

@interface WebService : NSObject

- (void)initWithPostRequest:(NSMutableDictionary *)parameters controller:(UIViewController *)viewController callSilently:(BOOL)isCallSilently
                 completion:(WebServiceCompletion)completionHandler;

@end
