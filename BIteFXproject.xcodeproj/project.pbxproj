// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		022F327D28E3BAF800630C8E /* SlideNoMuscle.mp4 in Resources */ = {isa = PBXBuildFile; fileRef = 022F327C28E3BAF700630C8E /* SlideNoMuscle.mp4 */; };
		022F328328E3BB6500630C8E /* BadGrindSideMusclesCU.mp4 in Resources */ = {isa = PBXBuildFile; fileRef = 022F328228E3BB6500630C8E /* BadGrindSideMusclesCU.mp4 */; };
		023CEC622A1BD922007E51D3 /* QuartzCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 814B88CB1498AB7500A32776 /* QuartzCore.framework */; };
		023EC4A42A3263C200F165A1 /* ErrorFormatter.m in Sources */ = {isa = PBXBuildFile; fileRef = 023EC4A32A3263C200F165A1 /* ErrorFormatter.m */; };
		02891E182928327B00ABC609 /* SpeedThumbDisabled.png in Resources */ = {isa = PBXBuildFile; fileRef = 02891E172928327B00ABC609 /* SpeedThumbDisabled.png */; };
		02F8F45C2AFB64E6002E2A5F /* PhotosUI.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 02F8F45B2AFB64E6002E2A5F /* PhotosUI.framework */; };
		2B251C76155BE2D7004CBB9E /* CFNetwork.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 2B251C75155BE2D7004CBB9E /* CFNetwork.framework */; };
		2B923AAB151B022200DCA7C7 /* libz.1.1.3.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = 2B923AAA151B022200DCA7C7 /* libz.1.1.3.dylib */; };
		2B923AB3151B03FD00DCA7C7 /* libz.1.2.5.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = 2B923AB2151B03FD00DCA7C7 /* libz.1.2.5.dylib */; };
		6CD6D23B8357F84473B66142 /* libPods-BIteFXproject.a in Frameworks */ = {isa = PBXBuildFile; fileRef = A64E6C1AC3A15EE58D874C1E /* libPods-BIteFXproject.a */; };
		810038DC14FCFD4600DF4A70 /* SystemConfiguration.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 810038DB14FCFD4600DF4A70 /* SystemConfiguration.framework */; };
		814B88C91498AB6100A32776 /* AVFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 814B88C81498AB6100A32776 /* AVFoundation.framework */; };
		814B88CE1498AB9200A32776 /* MobileCoreServices.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 814B88CD1498AB9200A32776 /* MobileCoreServices.framework */; };
		814B88D41498C21000A32776 /* Security.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 814B88D31498C21000A32776 /* Security.framework */; };
		814B88E31498DA4300A32776 /* CoreMedia.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 814B88E21498DA4300A32776 /* CoreMedia.framework */; };
		81737A181504D9AE0036086E /* AssetsLibrary.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 81737A171504D9AE0036086E /* AssetsLibrary.framework */; };
		81737A1B1504DC940036086E /* MediaPlayer.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 81737A1A1504DC940036086E /* MediaPlayer.framework */; };
		81A27D591497483F00C75021 /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 81A27D581497483F00C75021 /* UIKit.framework */; };
		81A27D5B1497483F00C75021 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 81A27D5A1497483F00C75021 /* Foundation.framework */; };
		81A27D5D1497483F00C75021 /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 81A27D5C1497483F00C75021 /* CoreGraphics.framework */; };
		81A27D631497483F00C75021 /* InfoPlist.strings in Resources */ = {isa = PBXBuildFile; fileRef = 81A27D611497483F00C75021 /* InfoPlist.strings */; };
		81AD590C1506404F00451EC2 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 81AD590B1506404F00451EC2 /* CoreVideo.framework */; };
		A91D28CF20AAE4E0000AFC20 /* BiteFxUpdate.MVI in Resources */ = {isa = PBXBuildFile; fileRef = A91D28CE20AAE4DF000AFC20 /* BiteFxUpdate.MVI */; };
		A920820D1619AC7600580279 /* StoreKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A920820C1619AC7600580279 /* StoreKit.framework */; };
		A922728A2DE99911008133EA /* searchBtn.png in Resources */ = {isa = PBXBuildFile; fileRef = A92272892DE99911008133EA /* searchBtn.png */; };
		A922728C2DE9992C008133EA /* closeBtn.png in Resources */ = {isa = PBXBuildFile; fileRef = A922728B2DE9992C008133EA /* closeBtn.png */; };
		A929E1CE2DE5E62400DB8085 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = A929E1CD2DE5E62400DB8085 /* PrivacyInfo.xcprivacy */; };
		A929E1D52DE5F49200DB8085 /* rightFlip.png in Resources */ = {isa = PBXBuildFile; fileRef = A929E1D42DE5F49200DB8085 /* rightFlip.png */; };
		A929E1D62DE5F49200DB8085 /* leftFlip.png in Resources */ = {isa = PBXBuildFile; fileRef = A929E1D32DE5F49200DB8085 /* leftFlip.png */; };
		A944A4272DEDC1100078FB4A /* fav.png in Resources */ = {isa = PBXBuildFile; fileRef = A944A4252DEDC1100078FB4A /* fav.png */; };
		A944A4282DEDC1100078FB4A /* unFav.png in Resources */ = {isa = PBXBuildFile; fileRef = A944A4262DEDC1100078FB4A /* unFav.png */; };
		A944A42A2DEDC51C0078FB4A /* clearSearch.png in Resources */ = {isa = PBXBuildFile; fileRef = A944A4292DEDC51C0078FB4A /* clearSearch.png */; };
		A94606512DEF1A0A001B7F91 /* SearchBar.m in Sources */ = {isa = PBXBuildFile; fileRef = A94606502DEF1A0A001B7F91 /* SearchBar.m */; };
		A95200F92690315F0026648A /* JAMSS-16KF.mp4 in Resources */ = {isa = PBXBuildFile; fileRef = A95200F22690315E0026648A /* JAMSS-16KF.mp4 */; };
		A95200FA2690315F0026648A /* JAMSS-32KF.mp4 in Resources */ = {isa = PBXBuildFile; fileRef = A95200F32690315E0026648A /* JAMSS-32KF.mp4 */; };
		A95200FC2690315F0026648A /* JAMSS.mp4 in Resources */ = {isa = PBXBuildFile; fileRef = A95200F52690315E0026648A /* JAMSS.mp4 */; };
		A95200FD2690315F0026648A /* JAMSS-119KF.mp4 in Resources */ = {isa = PBXBuildFile; fileRef = A95200F62690315E0026648A /* JAMSS-119KF.mp4 */; };
		A95200FE2690315F0026648A /* JAMSS-63KF.mp4 in Resources */ = {isa = PBXBuildFile; fileRef = A95200F72690315E0026648A /* JAMSS-63KF.mp4 */; };
		A95200FF2690315F0026648A /* JAMSS-8KF.mp4 in Resources */ = {isa = PBXBuildFile; fileRef = A95200F82690315F0026648A /* JAMSS-8KF.mp4 */; };
		A9537EBA26A1A1C500D48CB0 /* GoodAntRotateSideFront.mp4 in Resources */ = {isa = PBXBuildFile; fileRef = A9537EB426A1A1C400D48CB0 /* GoodAntRotateSideFront.mp4 */; };
		A9537EBB26A1A1C500D48CB0 /* BadGoodContactFront.mp4 in Resources */ = {isa = PBXBuildFile; fileRef = A9537EB526A1A1C500D48CB0 /* BadGoodContactFront.mp4 */; };
		A9537EBC26A1A1C500D48CB0 /* BadGoodAntMolarChip.mp4 in Resources */ = {isa = PBXBuildFile; fileRef = A9537EB626A1A1C500D48CB0 /* BadGoodAntMolarChip.mp4 */; };
		A9537EBE26A1A1C500D48CB0 /* GoodOpenCloseOCSide.mp4 in Resources */ = {isa = PBXBuildFile; fileRef = A9537EB826A1A1C500D48CB0 /* GoodOpenCloseOCSide.mp4 */; };
		A9537EBF26A1A1C500D48CB0 /* GoodContactAllQtr4Views.mp4 in Resources */ = {isa = PBXBuildFile; fileRef = A9537EB926A1A1C500D48CB0 /* GoodContactAllQtr4Views.mp4 */; };
		A95914722E0D0D18009E23DF /* leftCornerNew.png in Resources */ = {isa = PBXBuildFile; fileRef = A95914712E0D0D18009E23DF /* leftCornerNew.png */; };
		A95914752E0D5961009E23DF /* favMedium.png in Resources */ = {isa = PBXBuildFile; fileRef = A95914732E0D5961009E23DF /* favMedium.png */; };
		A95914762E0D5961009E23DF /* unFavMedium.png in Resources */ = {isa = PBXBuildFile; fileRef = A95914742E0D5961009E23DF /* unFavMedium.png */; };
		A95D0091274E5715004DA043 /* Features.MVI in Resources */ = {isa = PBXBuildFile; fileRef = A95D0090274E5715004DA043 /* Features.MVI */; };
		A969634D2DE8995300AE6CDD /* SearchBar.xib in Resources */ = {isa = PBXBuildFile; fileRef = A969634C2DE8995300AE6CDD /* SearchBar.xib */; };
		A96963552DE89D3100AE6CDD /* searchbar_base.png in Resources */ = {isa = PBXBuildFile; fileRef = A96963542DE89D3100AE6CDD /* searchbar_base.png */; };
		A970030320A1720D009E14BE /* CallWebService.m in Sources */ = {isa = PBXBuildFile; fileRef = A970030220A1720D009E14BE /* CallWebService.m */; };
		A976DDFB1897BEFD008147B6 /* CoreTelephony.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A976DDFA1897BEFD008147B6 /* CoreTelephony.framework */; };
		A98BA957274D2770008B4230 /* XMLParserFeature.m in Sources */ = {isa = PBXBuildFile; fileRef = A98BA956274D2770008B4230 /* XMLParserFeature.m */; };
		A98E9C5420A096F40027541A /* AFTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = A9C7D52920A086D400B54873 /* AFTableViewCell.m */; };
		A98E9C5520A096F40027541A /* AnimationPanelVC.m in Sources */ = {isa = PBXBuildFile; fileRef = A9C7D53B20A086D400B54873 /* AnimationPanelVC.m */; };
		A98E9C5620A096F40027541A /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = A9C7D53E20A086D400B54873 /* AppDelegate.m */; };
		A98E9C5820A096F40027541A /* PurchaseOptionCustomCell.m in Sources */ = {isa = PBXBuildFile; fileRef = A9C7D56320A086D500B54873 /* PurchaseOptionCustomCell.m */; };
		A98E9C5920A096F40027541A /* skipCell.m in Sources */ = {isa = PBXBuildFile; fileRef = A9C7D56620A086D500B54873 /* skipCell.m */; };
		A98E9C5A20A096F40027541A /* UITableViewCell+NIB.m in Sources */ = {isa = PBXBuildFile; fileRef = A9C7D56920A086D500B54873 /* UITableViewCell+NIB.m */; };
		A98E9C5B20A096F40027541A /* UpdateCell.m in Sources */ = {isa = PBXBuildFile; fileRef = A9C7D56B20A086D500B54873 /* UpdateCell.m */; };
		A98E9C5C20A096F40027541A /* CustomLabel.m in Sources */ = {isa = PBXBuildFile; fileRef = A9C7D56E20A086D500B54873 /* CustomLabel.m */; };
		A98E9C6220A096F40027541A /* Download.m in Sources */ = {isa = PBXBuildFile; fileRef = A9C7D58220A086D500B54873 /* Download.m */; };
		A98E9C6320A096F40027541A /* DownloadManager.m in Sources */ = {isa = PBXBuildFile; fileRef = A9C7D58420A086D500B54873 /* DownloadManager.m */; };
		A98E9C6420A096F40027541A /* AlwaysOpaqueImageView.m in Sources */ = {isa = PBXBuildFile; fileRef = A9C7D58920A086D500B54873 /* AlwaysOpaqueImageView.m */; };
		A98E9C6520A096F40027541A /* UIImageView+ForScrollView.m in Sources */ = {isa = PBXBuildFile; fileRef = A9C7D58B20A086D500B54873 /* UIImageView+ForScrollView.m */; };
		A98E9C6720A096F40027541A /* ICloud.m in Sources */ = {isa = PBXBuildFile; fileRef = A9C7D59220A086D500B54873 /* ICloud.m */; };
		A98E9C6820A096F40027541A /* iCloudDoc.m in Sources */ = {isa = PBXBuildFile; fileRef = A9C7D59420A086D500B54873 /* iCloudDoc.m */; };
		A98E9C6920A096F40027541A /* InAppPurchase.m in Sources */ = {isa = PBXBuildFile; fileRef = A9C7D64420A086D500B54873 /* InAppPurchase.m */; };
		A98E9C6B20A096F40027541A /* InfoWebView.m in Sources */ = {isa = PBXBuildFile; fileRef = A9C7D64C20A086D500B54873 /* InfoWebView.m */; };
		A98E9C6C20A096F40027541A /* NSObject+SBJSON.m in Sources */ = {isa = PBXBuildFile; fileRef = A9C7D65120A086D500B54873 /* NSObject+SBJSON.m */; };
		A98E9C6D20A096F40027541A /* NSString+SBJSON.m in Sources */ = {isa = PBXBuildFile; fileRef = A9C7D65320A086D500B54873 /* NSString+SBJSON.m */; };
		A98E9C6E20A096F40027541A /* SBJSON.m in Sources */ = {isa = PBXBuildFile; fileRef = A9C7D65520A086D500B54873 /* SBJSON.m */; };
		A98E9C6F20A096F40027541A /* SBJsonBase.m in Sources */ = {isa = PBXBuildFile; fileRef = A9C7D65720A086D500B54873 /* SBJsonBase.m */; };
		A98E9C7020A096F40027541A /* SBJsonParser.m in Sources */ = {isa = PBXBuildFile; fileRef = A9C7D65920A086D500B54873 /* SBJsonParser.m */; };
		A98E9C7120A096F40027541A /* SBJsonWriter.m in Sources */ = {isa = PBXBuildFile; fileRef = A9C7D65B20A086D500B54873 /* SBJsonWriter.m */; };
		A98E9C7220A096F40027541A /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = A9C7D65D20A086D500B54873 /* main.m */; };
		A98E9C7320A096F40027541A /* AnimationView.m in Sources */ = {isa = PBXBuildFile; fileRef = A9C7D66020A086D500B54873 /* AnimationView.m */; };
		A98E9C7420A096F40027541A /* BiteFXMainScreenVC.m in Sources */ = {isa = PBXBuildFile; fileRef = A9C7D66320A086D500B54873 /* BiteFXMainScreenVC.m */; };
		A98E9C7520A096F40027541A /* NetworkReachability.m in Sources */ = {isa = PBXBuildFile; fileRef = A9C7D6A120A086D500B54873 /* NetworkReachability.m */; };
		A98E9C7620A096F40027541A /* PlayerView.m in Sources */ = {isa = PBXBuildFile; fileRef = A9C7D6A520A086D500B54873 /* PlayerView.m */; };
		A98E9C7720A096F40027541A /* Reachability.m in Sources */ = {isa = PBXBuildFile; fileRef = A9C7D6A920A086D500B54873 /* Reachability.m */; };
		A98E9C7820A096F40027541A /* NSString+MD5Addition.m in Sources */ = {isa = PBXBuildFile; fileRef = A9C7D6AD20A086D500B54873 /* NSString+MD5Addition.m */; };
		A98E9C7920A096F40027541A /* UIDevice+IdentifierAddition.m in Sources */ = {isa = PBXBuildFile; fileRef = A9C7D6AF20A086D500B54873 /* UIDevice+IdentifierAddition.m */; };
		A98E9C7A20A096F40027541A /* Utility.m in Sources */ = {isa = PBXBuildFile; fileRef = A9C7D6B320A086D500B54873 /* Utility.m */; };
		A98E9C7C20A0970A0027541A /* DownloadFile.m in Sources */ = {isa = PBXBuildFile; fileRef = A9C7D6C920A086D500B54873 /* DownloadFile.m */; };
		A98E9C7D20A0970A0027541A /* XMLParser.m in Sources */ = {isa = PBXBuildFile; fileRef = A9C7D6CF20A086D500B54873 /* XMLParser.m */; };
		A98E9C7E20A0970A0027541A /* XMLParserUpdate.m in Sources */ = {isa = PBXBuildFile; fileRef = A9C7D6D120A086D500B54873 /* XMLParserUpdate.m */; };
		A99F2B932768D8B80086E9A6 /* FeatureSetDropDownView.m in Sources */ = {isa = PBXBuildFile; fileRef = A99F2B922768D8B80086E9A6 /* FeatureSetDropDownView.m */; };
		A99F2B962768D8D40086E9A6 /* FeatureSetDropDownView.xib in Resources */ = {isa = PBXBuildFile; fileRef = A99F2B952768D8D40086E9A6 /* FeatureSetDropDownView.xib */; };
		A9A1B7DA20F7585800FE39E3 /* WebService.m in Sources */ = {isa = PBXBuildFile; fileRef = A9A1B7D920F7585800FE39E3 /* WebService.m */; };
		A9A2056920A2C0C9007D5386 /* Database.m in Sources */ = {isa = PBXBuildFile; fileRef = A9A2056620A2C0C8007D5386 /* Database.m */; };
		A9A2056A20A2C0C9007D5386 /* BITEFXDatabase.sqlite in Resources */ = {isa = PBXBuildFile; fileRef = A9A2056720A2C0C8007D5386 /* BITEFXDatabase.sqlite */; };
		A9A2057020A2ED2C007D5386 /* UIAlertController+QMAlertControl.m in Sources */ = {isa = PBXBuildFile; fileRef = A9A2056F20A2ED2C007D5386 /* UIAlertController+QMAlertControl.m */; };
		A9A25A03247AE45700F9E361 /* BadGoodAntMolarChip.jpg in Resources */ = {isa = PBXBuildFile; fileRef = A9A259DC247AE45600F9E361 /* BadGoodAntMolarChip.jpg */; };
		A9A25A04247AE45700F9E361 /* BadGoodContactFront.jpg in Resources */ = {isa = PBXBuildFile; fileRef = A9A259DD247AE45600F9E361 /* BadGoodContactFront.jpg */; };
		A9A25A05247AE45700F9E361 /* BadGrindSideMusclesCU.jpg in Resources */ = {isa = PBXBuildFile; fileRef = A9A259DE247AE45600F9E361 /* BadGrindSideMusclesCU.jpg */; };
		A9A25A06247AE45700F9E361 /* GoodAntRotateSideFront.jpg in Resources */ = {isa = PBXBuildFile; fileRef = A9A259DF247AE45600F9E361 /* GoodAntRotateSideFront.jpg */; };
		A9A25A07247AE45700F9E361 /* GoodContactAllQtr4Views.jpg in Resources */ = {isa = PBXBuildFile; fileRef = A9A259E0247AE45600F9E361 /* GoodContactAllQtr4Views.jpg */; };
		A9A25A08247AE45700F9E361 /* GoodOpenCloseOCSide.jpg in Resources */ = {isa = PBXBuildFile; fileRef = A9A259E1247AE45600F9E361 /* GoodOpenCloseOCSide.jpg */; };
		A9A25A09247AE45700F9E361 /* SlideNoMuscle.jpg in Resources */ = {isa = PBXBuildFile; fileRef = A9A259E2247AE45600F9E361 /* SlideNoMuscle.jpg */; };
		A9A25A0D247AE45700F9E361 /* BadGoodAntMolarChip.html in Resources */ = {isa = PBXBuildFile; fileRef = A9A259E8247AE45700F9E361 /* BadGoodAntMolarChip.html */; };
		A9A25A0F247AE45700F9E361 /* BadGoodContactFront.html in Resources */ = {isa = PBXBuildFile; fileRef = A9A259EA247AE45700F9E361 /* BadGoodContactFront.html */; };
		A9A25A11247AE45700F9E361 /* BadGrindSideMusclesCU.html in Resources */ = {isa = PBXBuildFile; fileRef = A9A259EC247AE45700F9E361 /* BadGrindSideMusclesCU.html */; };
		A9A25A13247AE45700F9E361 /* GoodAntRotateSideFront.html in Resources */ = {isa = PBXBuildFile; fileRef = A9A259EE247AE45700F9E361 /* GoodAntRotateSideFront.html */; };
		A9A25A15247AE45700F9E361 /* GoodContactAllQtr4Views.html in Resources */ = {isa = PBXBuildFile; fileRef = A9A259F0247AE45700F9E361 /* GoodContactAllQtr4Views.html */; };
		A9A25A17247AE45700F9E361 /* GoodOpenCloseOCSide.html in Resources */ = {isa = PBXBuildFile; fileRef = A9A259F2247AE45700F9E361 /* GoodOpenCloseOCSide.html */; };
		A9A25A19247AE45700F9E361 /* SlideNoMuscle.html in Resources */ = {isa = PBXBuildFile; fileRef = A9A259F4247AE45700F9E361 /* SlideNoMuscle.html */; };
		A9A25A1B247AE45700F9E361 /* Animation Panel 512x384.png in Resources */ = {isa = PBXBuildFile; fileRef = A9A259F7247AE45700F9E361 /* Animation Panel 512x384.png */; };
		A9A25A1D247AE45700F9E361 /* dt.css in Resources */ = {isa = PBXBuildFile; fileRef = A9A259F9247AE45700F9E361 /* dt.css */; };
		A9A25A1E247AE45700F9E361 /* ExamplePresentationTemplate.html in Resources */ = {isa = PBXBuildFile; fileRef = A9A259FA247AE45700F9E361 /* ExamplePresentationTemplate.html */; };
		A9A25A1F247AE45700F9E361 /* Menu-Button.png in Resources */ = {isa = PBXBuildFile; fileRef = A9A259FB247AE45700F9E361 /* Menu-Button.png */; };
		A9A25A20247AE45700F9E361 /* NextButton.png in Resources */ = {isa = PBXBuildFile; fileRef = A9A259FC247AE45700F9E361 /* NextButton.png */; };
		A9A25A21247AE45700F9E361 /* PlayButton.png in Resources */ = {isa = PBXBuildFile; fileRef = A9A259FD247AE45700F9E361 /* PlayButton.png */; };
		A9A25A22247AE45700F9E361 /* Slider-Control.png in Resources */ = {isa = PBXBuildFile; fileRef = A9A259FE247AE45700F9E361 /* Slider-Control.png */; };
		A9A25A23247AE45700F9E361 /* CanineGd.jpg in Resources */ = {isa = PBXBuildFile; fileRef = A9A25A00247AE45700F9E361 /* CanineGd.jpg */; };
		A9A25A24247AE45700F9E361 /* MIPvsCR2.jpg in Resources */ = {isa = PBXBuildFile; fileRef = A9A25A01247AE45700F9E361 /* MIPvsCR2.jpg */; };
		A9A25A25247AE45700F9E361 /* WearFacets.jpg in Resources */ = {isa = PBXBuildFile; fileRef = A9A25A02247AE45700F9E361 /* WearFacets.jpg */; };
		A9B8963E20A1B622009DC519 /* Launch Screen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = A9B8963D20A1B622009DC519 /* Launch Screen.storyboard */; };
		A9BABE8E249C901D006933FB /* Slider-Control-109x60.png in Resources */ = {isa = PBXBuildFile; fileRef = A9BABE89249C901C006933FB /* Slider-Control-109x60.png */; };
		A9BABE8F249C901D006933FB /* Next-Button-84x60.png in Resources */ = {isa = PBXBuildFile; fileRef = A9BABE8A249C901C006933FB /* Next-Button-84x60.png */; };
		A9BABE90249C901D006933FB /* Menu-Button_110x59.png in Resources */ = {isa = PBXBuildFile; fileRef = A9BABE8B249C901C006933FB /* Menu-Button_110x59.png */; };
		A9BABE91249C901D006933FB /* Play-Button-84x58.png in Resources */ = {isa = PBXBuildFile; fileRef = A9BABE8C249C901C006933FB /* Play-Button-84x58.png */; };
		A9BABE92249C901D006933FB /* Presentation-Info-Button.png in Resources */ = {isa = PBXBuildFile; fileRef = A9BABE8D249C901C006933FB /* Presentation-Info-Button.png */; };
		A9C7D6E220A086D500B54873 /* AnimationPanelVC.xib in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D53C20A086D400B54873 /* AnimationPanelVC.xib */; };
		A9C7D6F720A086D500B54873 /* BiteFXWelcomeAndHelp.html in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D55820A086D500B54873 /* BiteFXWelcomeAndHelp.html */; };
		A9C7D6FB20A086D500B54873 /* PurchaseOptionCustomCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D56420A086D500B54873 /* PurchaseOptionCustomCell.xib */; };
		A9C7D6FD20A086D500B54873 /* skipCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D56720A086D500B54873 /* skipCell.xib */; };
		A9C7D70020A086D500B54873 /* UpdateCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D56C20A086D500B54873 /* UpdateCell.xib */; };
		A9C7D70520A086D500B54873 /* Devider_image.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D57620A086D500B54873 /* Devider_image.png */; };
		A9C7D71220A086D500B54873 /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D59620A086D500B54873 /* Images.xcassets */; };
		A9C7D71320A086D500B54873 /* 02.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D59820A086D500B54873 /* 02.png */; };
		A9C7D71420A086D500B54873 /* 03.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D59920A086D500B54873 /* 03.png */; };
		A9C7D71520A086D500B54873 /* buttoniPad.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D59A20A086D500B54873 /* buttoniPad.png */; };
		A9C7D71620A086D500B54873 /* nextFrame.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D59B20A086D500B54873 /* nextFrame.png */; };
		A9C7D71720A086D500B54873 /* PlayIMAGE.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D59C20A086D500B54873 /* PlayIMAGE.png */; };
		A9C7D71820A086D500B54873 /* playimg.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D59D20A086D500B54873 /* playimg.png */; };
		A9C7D71920A086D500B54873 /* previousFrame.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D59E20A086D500B54873 /* previousFrame.png */; };
		A9C7D71A20A086D500B54873 /* roundImg.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D59F20A086D500B54873 /* roundImg.png */; };
		A9C7D71B20A086D500B54873 /* RoundiPad.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5A020A086D500B54873 /* RoundiPad.png */; };
		A9C7D71C20A086D500B54873 /* btnClose.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5A220A086D500B54873 /* btnClose.png */; };
		A9C7D71D20A086D500B54873 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5A320A086D500B54873 /* <EMAIL> */; };
		A9C7D71E20A086D500B54873 /* btnCopy.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5A420A086D500B54873 /* btnCopy.png */; };
		A9C7D71F20A086D500B54873 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5A520A086D500B54873 /* <EMAIL> */; };
		A9C7D72020A086D500B54873 /* btnDeleteAlbum.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5A620A086D500B54873 /* btnDeleteAlbum.png */; };
		A9C7D72120A086D500B54873 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5A720A086D500B54873 /* <EMAIL> */; };
		A9C7D72220A086D500B54873 /* btnDeleteImage.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5A820A086D500B54873 /* btnDeleteImage.png */; };
		A9C7D72320A086D500B54873 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5A920A086D500B54873 /* <EMAIL> */; };
		A9C7D72420A086D500B54873 /* btnInfo.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5AA20A086D500B54873 /* btnInfo.png */; };
		A9C7D72520A086D500B54873 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5AB20A086D500B54873 /* <EMAIL> */; };
		A9C7D72620A086D500B54873 /* btnInfoSelected.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5AC20A086D500B54873 /* btnInfoSelected.png */; };
		A9C7D72720A086D500B54873 /* btnInfoSelected1.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5AD20A086D500B54873 /* btnInfoSelected1.png */; };
		A9C7D72820A086D500B54873 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5AE20A086D500B54873 /* <EMAIL> */; };
		A9C7D72920A086D500B54873 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5AF20A086D500B54873 /* <EMAIL> */; };
		A9C7D72A20A086D500B54873 /* BiteFX_Help.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5B120A086D500B54873 /* BiteFX_Help.png */; };
		A9C7D72B20A086D500B54873 /* BiteFXLogoSmall.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5B220A086D500B54873 /* BiteFXLogoSmall.png */; };
		A9C7D72C20A086D500B54873 /* BiteFXMainHelpPlayArea.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5B320A086D500B54873 /* BiteFXMainHelpPlayArea.png */; };
		A9C7D72D20A086D500B54873 /* BiteFXMainHelpPlayArea2.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5B420A086D500B54873 /* BiteFXMainHelpPlayArea2.png */; };
		A9C7D72E20A086D500B54873 /* BiteFXMainHelpPlayArea3.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5B520A086D500B54873 /* BiteFXMainHelpPlayArea3.png */; };
		A9C7D72F20A086D500B54873 /* BiteFXMainHelpSelection1.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5B620A086D500B54873 /* BiteFXMainHelpSelection1.png */; };
		A9C7D73020A086D500B54873 /* FrameCounter.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5B720A086D500B54873 /* FrameCounter.png */; };
		A9C7D73120A086D500B54873 /* AnimationButton.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5B920A086D500B54873 /* AnimationButton.png */; };
		A9C7D73220A086D500B54873 /* Help.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5BA20A086D500B54873 /* Help.png */; };
		A9C7D73320A086D500B54873 /* Info.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5BB20A086D500B54873 /* Info.png */; };
		A9C7D73420A086D500B54873 /* LockButton.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5BC20A086D500B54873 /* LockButton.png */; };
		A9C7D73520A086D500B54873 /* locked.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5BD20A086D500B54873 /* locked.png */; };
		A9C7D73620A086D500B54873 /* LoopButtonImg.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5BE20A086D500B54873 /* LoopButtonImg.png */; };
		A9C7D73720A086D500B54873 /* Menu.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5BF20A086D500B54873 /* Menu.png */; };
		A9C7D73820A086D500B54873 /* NextButtonImg.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5C020A086D500B54873 /* NextButtonImg.png */; };
		A9C7D73920A086D500B54873 /* PlayButtonImg.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5C120A086D500B54873 /* PlayButtonImg.png */; };
		A9C7D73A20A086D500B54873 /* PreviousButton.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5C220A086D500B54873 /* PreviousButton.png */; };
		A9C7D73B20A086D500B54873 /* SliderKnob.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5C320A086D500B54873 /* SliderKnob.png */; };
		A9C7D73C20A086D500B54873 /* StopButton.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5C420A086D500B54873 /* StopButton.png */; };
		A9C7D73D20A086D500B54873 /* unlocked.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5C520A086D500B54873 /* unlocked.png */; };
		A9C7D73E20A086D500B54873 /* AnimationPressed.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5C720A086D500B54873 /* AnimationPressed.png */; };
		A9C7D73F20A086D500B54873 /* HelpPressed.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5C820A086D500B54873 /* HelpPressed.png */; };
		A9C7D74020A086D500B54873 /* InfoPressed.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5C920A086D500B54873 /* InfoPressed.png */; };
		A9C7D74120A086D500B54873 /* LockButtonPressed.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5CA20A086D500B54873 /* LockButtonPressed.png */; };
		A9C7D74220A086D500B54873 /* LoopButtonPressedImg.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5CB20A086D500B54873 /* LoopButtonPressedImg.png */; };
		A9C7D74320A086D500B54873 /* MenuPressed.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5CC20A086D500B54873 /* MenuPressed.png */; };
		A9C7D74420A086D500B54873 /* NextButtonPressedImg.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5CD20A086D500B54873 /* NextButtonPressedImg.png */; };
		A9C7D74520A086D500B54873 /* PlayButtonPressed.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5CE20A086D500B54873 /* PlayButtonPressed.png */; };
		A9C7D74620A086D500B54873 /* PreviousPressed.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5CF20A086D500B54873 /* PreviousPressed.png */; };
		A9C7D74720A086D500B54873 /* SliderKnobPressed.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5D020A086D500B54873 /* SliderKnobPressed.png */; };
		A9C7D74820A086D500B54873 /* StopButtonPressed.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5D120A086D500B54873 /* StopButtonPressed.png */; };
		A9C7D74920A086D500B54873 /* unlockedPresses.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5D220A086D500B54873 /* unlockedPresses.png */; };
		A9C7D74A20A086D500B54873 /* resume-download.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5D320A086D500B54873 /* resume-download.png */; };
		A9C7D74D20A086D500B54873 /* SliderArea.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5D620A086D500B54873 /* SliderArea.png */; };
		A9C7D74E20A086D500B54873 /* SliderContainer.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5D720A086D500B54873 /* SliderContainer.png */; };
		A9C7D74F20A086D500B54873 /* SliderFilled.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5D820A086D500B54873 /* SliderFilled.png */; };
		A9C7D75020A086D500B54873 /* SliderSpeed.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5D920A086D500B54873 /* SliderSpeed.png */; };
		A9C7D75120A086D500B54873 /* SliderThumb.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5DA20A086D500B54873 /* SliderThumb.png */; };
		A9C7D75220A086D500B54873 /* SpeedIndicator.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5DB20A086D500B54873 /* SpeedIndicator.png */; };
		A9C7D75320A086D500B54873 /* SpeedSlider.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5DC20A086D500B54873 /* SpeedSlider.png */; };
		A9C7D75420A086D500B54873 /* SpeedSliderKnob.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5DD20A086D500B54873 /* SpeedSliderKnob.png */; };
		A9C7D75520A086D500B54873 /* toolbar.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5DE20A086D500B54873 /* toolbar.png */; };
		A9C7D75620A086D500B54873 /* toolbar1.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5DF20A086D500B54873 /* toolbar1.png */; };
		A9C7D75720A086D500B54873 /* gray.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5E020A086D500B54873 /* gray.png */; };
		A9C7D75820A086D500B54873 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5E120A086D500B54873 /* <EMAIL> */; };
		A9C7D75920A086D500B54873 /* Main_screen_with_animation_displayed.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5E320A086D500B54873 /* Main_screen_with_animation_displayed.png */; };
		A9C7D75A20A086D500B54873 /* Main_screen_with_picture_displayed.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5E420A086D500B54873 /* Main_screen_with_picture_displayed.png */; };
		A9C7D75B20A086D500B54873 /* Selection_panel_with animations_tab_selected.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5E520A086D500B54873 /* Selection_panel_with animations_tab_selected.png */; };
		A9C7D75C20A086D500B54873 /* Selection_panel_with_pictures_tab_selected.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5E620A086D500B54873 /* Selection_panel_with_pictures_tab_selected.png */; };
		A9C7D75D20A086D500B54873 /* Selection_panel_with_presentation_templates_tab_selected.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5E720A086D500B54873 /* Selection_panel_with_presentation_templates_tab_selected.png */; };
		A9C7D75E20A086D500B54873 /* LoopButton.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5E820A086D500B54873 /* LoopButton.png */; };
		A9C7D75F20A086D500B54873 /* LoopButtonNew.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5E920A086D500B54873 /* LoopButtonNew.png */; };
		A9C7D76020A086D500B54873 /* LoopButtonPressed.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5EA20A086D500B54873 /* LoopButtonPressed.png */; };
		A9C7D76120A086D500B54873 /* LoopButtonPressedNew.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5EB20A086D500B54873 /* LoopButtonPressedNew.png */; };
		A9C7D76220A086D500B54873 /* About.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5ED20A086D500B54873 /* About.png */; };
		A9C7D76320A086D500B54873 /* badge-big.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5EE20A086D500B54873 /* badge-big.png */; };
		A9C7D76420A086D500B54873 /* badge.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5EF20A086D500B54873 /* badge.png */; };
		A9C7D76520A086D500B54873 /* btnDownloadNrml.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5F020A086D500B54873 /* btnDownloadNrml.png */; };
		A9C7D76620A086D500B54873 /* btnDownloadslt.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5F120A086D500B54873 /* btnDownloadslt.png */; };
		A9C7D76720A086D500B54873 /* checkForUpdates-normal.PNG in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5F220A086D500B54873 /* checkForUpdates-normal.PNG */; };
		A9C7D76820A086D500B54873 /* checkForUpdates-pressed.PNG in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5F320A086D500B54873 /* checkForUpdates-pressed.PNG */; };
		A9C7D76920A086D500B54873 /* checkForUpdates.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5F420A086D500B54873 /* checkForUpdates.png */; };
		A9C7D76A20A086D500B54873 /* checkForUpdatesPressed.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5F520A086D500B54873 /* checkForUpdatesPressed.png */; };
		A9C7D76B20A086D500B54873 /* checkUpdates-bg.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5F620A086D500B54873 /* checkUpdates-bg.png */; };
		A9C7D76C20A086D500B54873 /* close-download.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5F720A086D500B54873 /* close-download.png */; };
		A9C7D76D20A086D500B54873 /* Deactivate-hover.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5F820A086D500B54873 /* Deactivate-hover.png */; };
		A9C7D76E20A086D500B54873 /* Deactivate-normal.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5F920A086D500B54873 /* Deactivate-normal.png */; };
		A9C7D76F20A086D500B54873 /* Deactivate-normalNew.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5FA20A086D500B54873 /* Deactivate-normalNew.png */; };
		A9C7D77020A086D500B54873 /* Deactivate-pressed.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5FB20A086D500B54873 /* Deactivate-pressed.png */; };
		A9C7D77120A086D500B54873 /* Deactivate-pressedNew.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5FC20A086D500B54873 /* Deactivate-pressedNew.png */; };
		A9C7D77220A086D500B54873 /* Default_landscape.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5FD20A086D500B54873 /* Default_landscape.png */; };
		A9C7D77320A086D500B54873 /* Default_portrait.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5FE20A086D500B54873 /* Default_portrait.png */; };
		A9C7D77420A086D500B54873 /* details.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D5FF20A086D500B54873 /* details.png */; };
		A9C7D77520A086D500B54873 /* DialogDownload.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D60020A086D500B54873 /* DialogDownload.png */; };
		A9C7D77620A086D500B54873 /* DialogNewDownload.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D60120A086D500B54873 /* DialogNewDownload.png */; };
		A9C7D77720A086D500B54873 /* download-bg.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D60220A086D500B54873 /* download-bg.png */; };
		A9C7D77820A086D500B54873 /* download.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D60320A086D500B54873 /* download.png */; };
		A9C7D77920A086D500B54873 /* downloadPRessed.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D60420A086D500B54873 /* downloadPRessed.png */; };
		A9C7D77A20A086D500B54873 /* downloadprogressFilled.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D60520A086D500B54873 /* downloadprogressFilled.png */; };
		A9C7D77B20A086D500B54873 /* downloadProgressNormal.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D60620A086D500B54873 /* downloadProgressNormal.png */; };
		A9C7D77C20A086D500B54873 /* downloadUpdates-normal.PNG in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D60720A086D500B54873 /* downloadUpdates-normal.PNG */; };
		A9C7D77D20A086D500B54873 /* downloadUpdates-pressed.PNG in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D60820A086D500B54873 /* downloadUpdates-pressed.PNG */; };
		A9C7D77E20A086D500B54873 /* dwnloadDialog.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D60920A086D500B54873 /* dwnloadDialog.png */; };
		A9C7D77F20A086D500B54873 /* Line.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D60A20A086D500B54873 /* Line.png */; };
		A9C7D78020A086D500B54873 /* Line1.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D60B20A086D500B54873 /* Line1.png */; };
		A9C7D78120A086D500B54873 /* Line2.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D60C20A086D500B54873 /* Line2.png */; };
		A9C7D78220A086D500B54873 /* login-normal.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D60D20A086D500B54873 /* login-normal.png */; };
		A9C7D78320A086D500B54873 /* login-pressed.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D60E20A086D500B54873 /* login-pressed.png */; };
		A9C7D78420A086D500B54873 /* menuPopup.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D60F20A086D500B54873 /* menuPopup.png */; };
		A9C7D78520A086D500B54873 /* more.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D61020A086D500B54873 /* more.png */; };
		A9C7D78620A086D500B54873 /* pauseNormal.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D61120A086D500B54873 /* pauseNormal.png */; };
		A9C7D78720A086D500B54873 /* pausePressed.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D61220A086D500B54873 /* pausePressed.png */; };
		A9C7D78820A086D500B54873 /* products.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D61320A086D500B54873 /* products.png */; };
		A9C7D78920A086D500B54873 /* products1.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D61420A086D500B54873 /* products1.png */; };
		A9C7D78A20A086D500B54873 /* progressbarFilled.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D61520A086D500B54873 /* progressbarFilled.png */; };
		A9C7D78B20A086D500B54873 /* progressbarNormal.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D61620A086D500B54873 /* progressbarNormal.png */; };
		A9C7D78C20A086D500B54873 /* PurchasePrice-normal.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D61720A086D500B54873 /* PurchasePrice-normal.png */; };
		A9C7D78D20A086D500B54873 /* PurchasePrice-pressed.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D61820A086D500B54873 /* PurchasePrice-pressed.png */; };
		A9C7D78E20A086D500B54873 /* Register-normal.PNG in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D61920A086D500B54873 /* Register-normal.PNG */; };
		A9C7D78F20A086D500B54873 /* Register-pressed.PNG in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D61A20A086D500B54873 /* Register-pressed.PNG */; };
		A9C7D79020A086D500B54873 /* register.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D61B20A086D500B54873 /* register.png */; };
		A9C7D79120A086D500B54873 /* register1.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D61C20A086D500B54873 /* register1.png */; };
		A9C7D79220A086D500B54873 /* restore.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D61D20A086D500B54873 /* restore.png */; };
		A9C7D79320A086D500B54873 /* speed.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D61E20A086D500B54873 /* speed.png */; };
		A9C7D79420A086D500B54873 /* speedKnob-Normal.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D61F20A086D500B54873 /* speedKnob-Normal.png */; };
		A9C7D79520A086D500B54873 /* speedKnob-Pressed.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D62020A086D500B54873 /* speedKnob-Pressed.png */; };
		A9C7D79620A086D500B54873 /* speedSliderNew.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D62120A086D500B54873 /* speedSliderNew.png */; };
		A9C7D79720A086D500B54873 /* speedslidernewimg.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D62220A086D500B54873 /* speedslidernewimg.png */; };
		A9C7D79820A086D500B54873 /* SpeedThumbNormal.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D62320A086D500B54873 /* SpeedThumbNormal.png */; };
		A9C7D79920A086D500B54873 /* SpeedThumbSelected.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D62420A086D500B54873 /* SpeedThumbSelected.png */; };
		A9C7D79A20A086D500B54873 /* updates-bg.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D62520A086D500B54873 /* updates-bg.png */; };
		A9C7D79B20A086D500B54873 /* updates.PNG in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D62620A086D500B54873 /* updates.PNG */; };
		A9C7D79C20A086D500B54873 /* updatesNew.PNG in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D62720A086D500B54873 /* updatesNew.PNG */; };
		A9C7D79D20A086D500B54873 /* X.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D62820A086D500B54873 /* X.png */; };
		A9C7D79F20A086D500B54873 /* NextButtonNew.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D62A20A086D500B54873 /* NextButtonNew.png */; };
		A9C7D7A120A086D500B54873 /* PlayButtonNew.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D62C20A086D500B54873 /* PlayButtonNew.png */; };
		A9C7D7A220A086D500B54873 /* Red.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D62D20A086D500B54873 /* Red.png */; };
		A9C7D7A320A086D500B54873 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D62E20A086D500B54873 /* <EMAIL> */; };
		A9C7D7A420A086D500B54873 /* Active_Animation.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D63020A086D500B54873 /* Active_Animation.png */; };
		A9C7D7A520A086D500B54873 /* Active_Pictures.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D63120A086D500B54873 /* Active_Pictures.png */; };
		A9C7D7A620A086D500B54873 /* Active_Presentations.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D63220A086D500B54873 /* Active_Presentations.png */; };
		A9C7D7A720A086D500B54873 /* btnHide.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D63320A086D500B54873 /* btnHide.png */; };
		A9C7D7A820A086D500B54873 /* btnUnHide.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D63420A086D500B54873 /* btnUnHide.png */; };
		A9C7D7A920A086D500B54873 /* Default_Animation.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D63520A086D500B54873 /* Default_Animation.png */; };
		A9C7D7AA20A086D500B54873 /* Default_Pictures.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D63620A086D500B54873 /* Default_Pictures.png */; };
		A9C7D7AB20A086D500B54873 /* Default_Presentations.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D63720A086D500B54873 /* Default_Presentations.png */; };
		A9C7D7B320A086D500B54873 /* Yellow.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D64020A086D500B54873 /* Yellow.png */; };
		A9C7D7B420A086D500B54873 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D64120A086D500B54873 /* <EMAIL> */; };
		A9C7D7B920A086D500B54873 /* InfoWebView.xib in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D64D20A086D500B54873 /* InfoWebView.xib */; };
		A9C7D7C020A086D500B54873 /* leftCorner.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D65C20A086D500B54873 /* leftCorner.png */; };
		A9C7D7C320A086D500B54873 /* AnimationView.xib in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D66120A086D500B54873 /* AnimationView.xib */; };
		A9C7D7C520A086D500B54873 /* BiteFXMainScreenVC.xib in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D66420A086D500B54873 /* BiteFXMainScreenVC.xib */; };
		A9C7D80320A086D500B54873 /* AddMoreImage.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D6B520A086D500B54873 /* AddMoreImage.png */; };
		A9C7D80D20A086D500B54873 /* hiddenpic.png in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D6BF20A086D500B54873 /* hiddenpic.png */; };
		A9C7D81520A086D500B54873 /* BiteFXiPadBasicInventory.MVI in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D6CB20A086D500B54873 /* BiteFXiPadBasicInventory.MVI */; };
		A9C7D81620A086D500B54873 /* BiteFXiPadFullInventory.mvi in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D6CC20A086D500B54873 /* BiteFXiPadFullInventory.mvi */; };
		A9C7D81720A086D500B54873 /* BiteFXiPadFullInventoryOld.mvi in Resources */ = {isa = PBXBuildFile; fileRef = A9C7D6CD20A086D500B54873 /* BiteFXiPadFullInventoryOld.mvi */; };
		A9D7F4A3242B285F007EBAE2 /* IAPManager.m in Sources */ = {isa = PBXBuildFile; fileRef = A9D7F4A2242B285F007EBAE2 /* IAPManager.m */; };
		A9DD7F2227DF0D9100C5B864 /* BiteFXiPad-Subscription.html in Resources */ = {isa = PBXBuildFile; fileRef = A9DD7F1E27DF0D9100C5B864 /* BiteFXiPad-Subscription.html */; };
		A9DD7F2327DF0D9100C5B864 /* BiteFXiPad-Privacy.html in Resources */ = {isa = PBXBuildFile; fileRef = A9DD7F1F27DF0D9100C5B864 /* BiteFXiPad-Privacy.html */; };
		A9DD7F2427DF0D9100C5B864 /* BiteFXiPad-Support.html in Resources */ = {isa = PBXBuildFile; fileRef = A9DD7F2027DF0D9100C5B864 /* BiteFXiPad-Support.html */; };
		A9DD7F2527DF0D9100C5B864 /* BiteFXiPad-Terms-and-Conditions.html in Resources */ = {isa = PBXBuildFile; fileRef = A9DD7F2127DF0D9100C5B864 /* BiteFXiPad-Terms-and-Conditions.html */; };
		A9DD7F2727DF0D9F00C5B864 /* dt1.css in Resources */ = {isa = PBXBuildFile; fileRef = A9DD7F2627DF0D9F00C5B864 /* dt1.css */; };
		A9DD7F3727DF0DF700C5B864 /* Animation Panel.png in Resources */ = {isa = PBXBuildFile; fileRef = A9DD7F2827DF0DF400C5B864 /* Animation Panel.png */; };
		A9DD7F3827DF0DF700C5B864 /* AnimationSelectionTab.png in Resources */ = {isa = PBXBuildFile; fileRef = A9DD7F2927DF0DF400C5B864 /* AnimationSelectionTab.png */; };
		A9DD7F3927DF0DF700C5B864 /* BiteFX-on-iPad-3-1-Issues-and-Treatments-Callout.png in Resources */ = {isa = PBXBuildFile; fileRef = A9DD7F2A27DF0DF400C5B864 /* BiteFX-on-iPad-3-1-Issues-and-Treatments-Callout.png */; };
		A9DD7F3A27DF0DF700C5B864 /* Hal-Stewart-on-Patient-Education-Detail-2.jpg in Resources */ = {isa = PBXBuildFile; fileRef = A9DD7F2B27DF0DF500C5B864 /* Hal-Stewart-on-Patient-Education-Detail-2.jpg */; };
		A9DD7F3B27DF0DF700C5B864 /* BiteFX-on-iPad-Presentation-Sets.PNG in Resources */ = {isa = PBXBuildFile; fileRef = A9DD7F2C27DF0DF500C5B864 /* BiteFX-on-iPad-Presentation-Sets.PNG */; };
		A9DD7F3C27DF0DF700C5B864 /* BiteFX-on-iPad-3-1-Dawson-Starter-Callout.png in Resources */ = {isa = PBXBuildFile; fileRef = A9DD7F2D27DF0DF500C5B864 /* BiteFX-on-iPad-3-1-Dawson-Starter-Callout.png */; };
		A9DD7F3D27DF0DF700C5B864 /* BiteFX-on-iPad-2-5-18-Picture-Panel.png in Resources */ = {isa = PBXBuildFile; fileRef = A9DD7F2E27DF0DF500C5B864 /* BiteFX-on-iPad-2-5-18-Picture-Panel.png */; };
		A9DD7F3E27DF0DF700C5B864 /* Dr TJ Bolt 2020 Detail 148x200.png in Resources */ = {isa = PBXBuildFile; fileRef = A9DD7F2F27DF0DF500C5B864 /* Dr TJ Bolt 2020 Detail 148x200.png */; };
		A9DD7F3F27DF0DF700C5B864 /* Rick-Rogers-Detail-140x177.png in Resources */ = {isa = PBXBuildFile; fileRef = A9DD7F3027DF0DF500C5B864 /* Rick-Rogers-Detail-140x177.png */; };
		A9DD7F4027DF0DF700C5B864 /* BiteFX-on-iPad-3-1-Detailed-Presentations-Callout.png in Resources */ = {isa = PBXBuildFile; fileRef = A9DD7F3127DF0DF600C5B864 /* BiteFX-on-iPad-3-1-Detailed-Presentations-Callout.png */; };
		A9DD7F4127DF0DF700C5B864 /* BiteFX-on-iPad-2-5-18-Hygienist-Presentation-Templates.png in Resources */ = {isa = PBXBuildFile; fileRef = A9DD7F3227DF0DF600C5B864 /* BiteFX-on-iPad-2-5-18-Hygienist-Presentation-Templates.png */; };
		A9DD7F4227DF0DF700C5B864 /* BiteFX-on-iPad-2-5-18-Presentation-Templates.png in Resources */ = {isa = PBXBuildFile; fileRef = A9DD7F3327DF0DF600C5B864 /* BiteFX-on-iPad-2-5-18-Presentation-Templates.png */; };
		A9DD7F4327DF0DF700C5B864 /* Chris_Toomey_191x256.png in Resources */ = {isa = PBXBuildFile; fileRef = A9DD7F3427DF0DF600C5B864 /* Chris_Toomey_191x256.png */; };
		A9DD7F4427DF0DF700C5B864 /* BiteFX-on-iPad-3-1-Staff-Training-Callout.png in Resources */ = {isa = PBXBuildFile; fileRef = A9DD7F3527DF0DF600C5B864 /* BiteFX-on-iPad-3-1-Staff-Training-Callout.png */; };
		A9DD7F4527DF0DF700C5B864 /* BiteFX-on-iPad-3-1-Hygienist-Callout.png in Resources */ = {isa = PBXBuildFile; fileRef = A9DD7F3627DF0DF600C5B864 /* BiteFX-on-iPad-3-1-Hygienist-Callout.png */; };
		A9E3E7A02E0ADA47000E4956 /* crossSelected.png in Resources */ = {isa = PBXBuildFile; fileRef = A9E3E79F2E0ADA47000E4956 /* crossSelected.png */; };
		A9E7B5B314D901C300C3D32C /* libsqlite3.0.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = A9E7B5B214D901C300C3D32C /* libsqlite3.0.dylib */; };
		A9E7F29E20B3061D001BBC06 /* ExternalAccessory.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A9E7F29D20B3061D001BBC06 /* ExternalAccessory.framework */; };
		A9F3FF9F24DD977A00CC61D0 /* Active Tab Presentations v4.png in Resources */ = {isa = PBXBuildFile; fileRef = A9F3FF9E24DD977A00CC61D0 /* Active Tab Presentations v4.png */; };
		A9F6A18520A1798000FE3D57 /* DownloadHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = A9F6A18420A1798000FE3D57 /* DownloadHelper.m */; };
		A9F7773A2E05653900FFB765 /* unFavSmall.png in Resources */ = {isa = PBXBuildFile; fileRef = A9F777392E05653900FFB765 /* unFavSmall.png */; };
		A9F7773B2E05653900FFB765 /* favSmall.png in Resources */ = {isa = PBXBuildFile; fileRef = A9F777382E05653900FFB765 /* favSmall.png */; };
		A9F7C98F2760DE7200BD7A30 /* FeatureSetVC.m in Sources */ = {isa = PBXBuildFile; fileRef = A9F7C98D2760DE7200BD7A30 /* FeatureSetVC.m */; };
		A9F7C9902760DE7200BD7A30 /* FeatureSetVC.xib in Resources */ = {isa = PBXBuildFile; fileRef = A9F7C98E2760DE7200BD7A30 /* FeatureSetVC.xib */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		022F327C28E3BAF700630C8E /* SlideNoMuscle.mp4 */ = {isa = PBXFileReference; lastKnownFileType = file; name = SlideNoMuscle.mp4; path = H265/SlideNoMuscle.mp4; sourceTree = "<group>"; };
		022F327E28E3BB2200630C8E /* BadGoodAntMolarChip.mp4 */ = {isa = PBXFileReference; lastKnownFileType = file; name = BadGoodAntMolarChip.mp4; path = H265/BadGoodAntMolarChip.mp4; sourceTree = "<group>"; };
		022F328028E3BB3F00630C8E /* BadGoodContactFront.mp4 */ = {isa = PBXFileReference; lastKnownFileType = file; name = BadGoodContactFront.mp4; path = H265/BadGoodContactFront.mp4; sourceTree = "<group>"; };
		022F328228E3BB6500630C8E /* BadGrindSideMusclesCU.mp4 */ = {isa = PBXFileReference; lastKnownFileType = file; name = BadGrindSideMusclesCU.mp4; path = H265/BadGrindSideMusclesCU.mp4; sourceTree = "<group>"; };
		022F328428E3BB7E00630C8E /* GoodAntRotateSideFront.mp4 */ = {isa = PBXFileReference; lastKnownFileType = file; name = GoodAntRotateSideFront.mp4; path = H265/GoodAntRotateSideFront.mp4; sourceTree = "<group>"; };
		022F328628E3BBAC00630C8E /* GoodContactAllQtr4Views.mp4 */ = {isa = PBXFileReference; lastKnownFileType = file; name = GoodContactAllQtr4Views.mp4; path = H265/GoodContactAllQtr4Views.mp4; sourceTree = "<group>"; };
		022F328828E3BBC500630C8E /* GoodOpenCloseOCSide.mp4 */ = {isa = PBXFileReference; lastKnownFileType = file; name = GoodOpenCloseOCSide.mp4; path = H265/GoodOpenCloseOCSide.mp4; sourceTree = "<group>"; };
		023EC4A32A3263C200F165A1 /* ErrorFormatter.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ErrorFormatter.m; sourceTree = SOURCE_ROOT; };
		023EC4A52A32640300F165A1 /* ErrorFormatter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ErrorFormatter.h; sourceTree = "<group>"; };
		02891E172928327B00ABC609 /* SpeedThumbDisabled.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = SpeedThumbDisabled.png; sourceTree = "<group>"; };
		02F8F45B2AFB64E6002E2A5F /* PhotosUI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = PhotosUI.framework; path = System/Library/Frameworks/PhotosUI.framework; sourceTree = SDKROOT; };
		1A26A5FD725A603889CBA235 /* Pods-BIteFXproject.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-BIteFXproject.release.xcconfig"; path = "Pods/Target Support Files/Pods-BIteFXproject/Pods-BIteFXproject.release.xcconfig"; sourceTree = "<group>"; };
		2B251C75155BE2D7004CBB9E /* CFNetwork.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CFNetwork.framework; path = System/Library/Frameworks/CFNetwork.framework; sourceTree = SDKROOT; };
		2B923AAA151B022200DCA7C7 /* libz.1.1.3.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libz.1.1.3.dylib; path = usr/lib/libz.1.1.3.dylib; sourceTree = SDKROOT; };
		2B923AB2151B03FD00DCA7C7 /* libz.1.2.5.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libz.1.2.5.dylib; path = usr/lib/libz.1.2.5.dylib; sourceTree = SDKROOT; };
		810038DB14FCFD4600DF4A70 /* SystemConfiguration.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SystemConfiguration.framework; path = System/Library/Frameworks/SystemConfiguration.framework; sourceTree = SDKROOT; };
		814B88C81498AB6100A32776 /* AVFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVFoundation.framework; path = System/Library/Frameworks/AVFoundation.framework; sourceTree = SDKROOT; };
		814B88CB1498AB7500A32776 /* QuartzCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = QuartzCore.framework; path = System/Library/Frameworks/QuartzCore.framework; sourceTree = SDKROOT; };
		814B88CD1498AB9200A32776 /* MobileCoreServices.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MobileCoreServices.framework; path = System/Library/Frameworks/MobileCoreServices.framework; sourceTree = SDKROOT; };
		814B88D31498C21000A32776 /* Security.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Security.framework; path = System/Library/Frameworks/Security.framework; sourceTree = SDKROOT; };
		814B88E21498DA4300A32776 /* CoreMedia.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreMedia.framework; path = System/Library/Frameworks/CoreMedia.framework; sourceTree = SDKROOT; };
		81737A171504D9AE0036086E /* AssetsLibrary.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AssetsLibrary.framework; path = System/Library/Frameworks/AssetsLibrary.framework; sourceTree = SDKROOT; };
		81737A1A1504DC940036086E /* MediaPlayer.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MediaPlayer.framework; path = System/Library/Frameworks/MediaPlayer.framework; sourceTree = SDKROOT; };
		81A27D541497483F00C75021 /* BiteFX.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = BiteFX.app; sourceTree = BUILT_PRODUCTS_DIR; };
		81A27D581497483F00C75021 /* UIKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UIKit.framework; path = System/Library/Frameworks/UIKit.framework; sourceTree = SDKROOT; };
		81A27D5A1497483F00C75021 /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = System/Library/Frameworks/Foundation.framework; sourceTree = SDKROOT; };
		81A27D5C1497483F00C75021 /* CoreGraphics.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreGraphics.framework; path = System/Library/Frameworks/CoreGraphics.framework; sourceTree = SDKROOT; };
		81A27D601497483F00C75021 /* BIteFXproject-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "BIteFXproject-Info.plist"; sourceTree = "<group>"; };
		81A27D621497483F00C75021 /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		81A27D641497483F00C75021 /* main.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		81A27D661497483F00C75021 /* BIteFXproject-Prefix.pch */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "BIteFXproject-Prefix.pch"; sourceTree = "<group>"; };
		81AD590B1506404F00451EC2 /* CoreVideo.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreVideo.framework; path = System/Library/Frameworks/CoreVideo.framework; sourceTree = SDKROOT; };
		A64E6C1AC3A15EE58D874C1E /* libPods-BIteFXproject.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-BIteFXproject.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		A91D28CE20AAE4DF000AFC20 /* BiteFxUpdate.MVI */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xml; path = BiteFxUpdate.MVI; sourceTree = "<group>"; };
		A920820C1619AC7600580279 /* StoreKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = StoreKit.framework; path = System/Library/Frameworks/StoreKit.framework; sourceTree = SDKROOT; };
		A92272892DE99911008133EA /* searchBtn.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = searchBtn.png; sourceTree = "<group>"; };
		A922728B2DE9992C008133EA /* closeBtn.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = closeBtn.png; sourceTree = "<group>"; };
		A929E1CD2DE5E62400DB8085 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; lastKnownFileType = text.xml; path = PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		A929E1D32DE5F49200DB8085 /* leftFlip.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = leftFlip.png; sourceTree = "<group>"; };
		A929E1D42DE5F49200DB8085 /* rightFlip.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = rightFlip.png; sourceTree = "<group>"; };
		A944A4252DEDC1100078FB4A /* fav.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = fav.png; sourceTree = "<group>"; };
		A944A4262DEDC1100078FB4A /* unFav.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = unFav.png; sourceTree = "<group>"; };
		A944A4292DEDC51C0078FB4A /* clearSearch.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = clearSearch.png; sourceTree = "<group>"; };
		A946064F2DEF1A0A001B7F91 /* SearchBar.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SearchBar.h; sourceTree = "<group>"; };
		A94606502DEF1A0A001B7F91 /* SearchBar.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SearchBar.m; sourceTree = "<group>"; };
		A95200F22690315E0026648A /* JAMSS-16KF.mp4 */ = {isa = PBXFileReference; lastKnownFileType = file; path = "JAMSS-16KF.mp4"; sourceTree = "<group>"; };
		A95200F32690315E0026648A /* JAMSS-32KF.mp4 */ = {isa = PBXFileReference; lastKnownFileType = file; path = "JAMSS-32KF.mp4"; sourceTree = "<group>"; };
		A95200F52690315E0026648A /* JAMSS.mp4 */ = {isa = PBXFileReference; lastKnownFileType = file; path = JAMSS.mp4; sourceTree = "<group>"; };
		A95200F62690315E0026648A /* JAMSS-119KF.mp4 */ = {isa = PBXFileReference; lastKnownFileType = file; path = "JAMSS-119KF.mp4"; sourceTree = "<group>"; };
		A95200F72690315E0026648A /* JAMSS-63KF.mp4 */ = {isa = PBXFileReference; lastKnownFileType = file; path = "JAMSS-63KF.mp4"; sourceTree = "<group>"; };
		A95200F82690315F0026648A /* JAMSS-8KF.mp4 */ = {isa = PBXFileReference; lastKnownFileType = file; path = "JAMSS-8KF.mp4"; sourceTree = "<group>"; };
		A9537EB426A1A1C400D48CB0 /* GoodAntRotateSideFront.mp4 */ = {isa = PBXFileReference; lastKnownFileType = file; path = GoodAntRotateSideFront.mp4; sourceTree = "<group>"; };
		A9537EB526A1A1C500D48CB0 /* BadGoodContactFront.mp4 */ = {isa = PBXFileReference; lastKnownFileType = file; path = BadGoodContactFront.mp4; sourceTree = "<group>"; };
		A9537EB626A1A1C500D48CB0 /* BadGoodAntMolarChip.mp4 */ = {isa = PBXFileReference; lastKnownFileType = file; path = BadGoodAntMolarChip.mp4; sourceTree = "<group>"; };
		A9537EB726A1A1C500D48CB0 /* BadGrindSideMusclesCU.mp4 */ = {isa = PBXFileReference; lastKnownFileType = file; path = BadGrindSideMusclesCU.mp4; sourceTree = "<group>"; };
		A9537EB826A1A1C500D48CB0 /* GoodOpenCloseOCSide.mp4 */ = {isa = PBXFileReference; lastKnownFileType = file; path = GoodOpenCloseOCSide.mp4; sourceTree = "<group>"; };
		A9537EB926A1A1C500D48CB0 /* GoodContactAllQtr4Views.mp4 */ = {isa = PBXFileReference; lastKnownFileType = file; path = GoodContactAllQtr4Views.mp4; sourceTree = "<group>"; };
		A9537EC126A1A1F900D48CB0 /* SlideNoMuscle.mp4 */ = {isa = PBXFileReference; lastKnownFileType = file; path = SlideNoMuscle.mp4; sourceTree = "<group>"; };
		A95914712E0D0D18009E23DF /* leftCornerNew.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = leftCornerNew.png; sourceTree = "<group>"; };
		A95914732E0D5961009E23DF /* favMedium.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = favMedium.png; sourceTree = "<group>"; };
		A95914742E0D5961009E23DF /* unFavMedium.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = unFavMedium.png; sourceTree = "<group>"; };
		A95D0090274E5715004DA043 /* Features.MVI */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xml; path = Features.MVI; sourceTree = "<group>"; };
		A969634C2DE8995300AE6CDD /* SearchBar.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = SearchBar.xib; sourceTree = "<group>"; };
		A96963542DE89D3100AE6CDD /* searchbar_base.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = searchbar_base.png; sourceTree = "<group>"; };
		A970030120A1720D009E14BE /* CallWebService.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CallWebService.h; sourceTree = "<group>"; };
		A970030220A1720D009E14BE /* CallWebService.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CallWebService.m; sourceTree = "<group>"; };
		A976DDFA1897BEFD008147B6 /* CoreTelephony.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreTelephony.framework; path = System/Library/Frameworks/CoreTelephony.framework; sourceTree = SDKROOT; };
		A98BA955274D2770008B4230 /* XMLParserFeature.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XMLParserFeature.h; sourceTree = "<group>"; };
		A98BA956274D2770008B4230 /* XMLParserFeature.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XMLParserFeature.m; sourceTree = "<group>"; };
		A9968CA220A960DA0064ED61 /* VideoPlay.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = VideoPlay.h; sourceTree = "<group>"; };
		A99F2B912768D8B80086E9A6 /* FeatureSetDropDownView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FeatureSetDropDownView.h; sourceTree = "<group>"; };
		A99F2B922768D8B80086E9A6 /* FeatureSetDropDownView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FeatureSetDropDownView.m; sourceTree = "<group>"; };
		A99F2B952768D8D40086E9A6 /* FeatureSetDropDownView.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = FeatureSetDropDownView.xib; sourceTree = "<group>"; };
		A9A1B7D820F7585800FE39E3 /* WebService.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = WebService.h; sourceTree = "<group>"; };
		A9A1B7D920F7585800FE39E3 /* WebService.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = WebService.m; sourceTree = "<group>"; };
		A9A2056620A2C0C8007D5386 /* Database.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = Database.m; sourceTree = "<group>"; };
		A9A2056720A2C0C8007D5386 /* BITEFXDatabase.sqlite */ = {isa = PBXFileReference; lastKnownFileType = file; path = BITEFXDatabase.sqlite; sourceTree = "<group>"; };
		A9A2056820A2C0C9007D5386 /* Database.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Database.h; sourceTree = "<group>"; };
		A9A2056E20A2ED2C007D5386 /* UIAlertController+QMAlertControl.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIAlertController+QMAlertControl.h"; sourceTree = "<group>"; };
		A9A2056F20A2ED2C007D5386 /* UIAlertController+QMAlertControl.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIAlertController+QMAlertControl.m"; sourceTree = "<group>"; };
		A9A259DC247AE45600F9E361 /* BadGoodAntMolarChip.jpg */ = {isa = PBXFileReference; lastKnownFileType = image.jpeg; path = BadGoodAntMolarChip.jpg; sourceTree = "<group>"; };
		A9A259DD247AE45600F9E361 /* BadGoodContactFront.jpg */ = {isa = PBXFileReference; lastKnownFileType = image.jpeg; path = BadGoodContactFront.jpg; sourceTree = "<group>"; };
		A9A259DE247AE45600F9E361 /* BadGrindSideMusclesCU.jpg */ = {isa = PBXFileReference; lastKnownFileType = image.jpeg; path = BadGrindSideMusclesCU.jpg; sourceTree = "<group>"; };
		A9A259DF247AE45600F9E361 /* GoodAntRotateSideFront.jpg */ = {isa = PBXFileReference; lastKnownFileType = image.jpeg; path = GoodAntRotateSideFront.jpg; sourceTree = "<group>"; };
		A9A259E0247AE45600F9E361 /* GoodContactAllQtr4Views.jpg */ = {isa = PBXFileReference; lastKnownFileType = image.jpeg; path = GoodContactAllQtr4Views.jpg; sourceTree = "<group>"; };
		A9A259E1247AE45600F9E361 /* GoodOpenCloseOCSide.jpg */ = {isa = PBXFileReference; lastKnownFileType = image.jpeg; path = GoodOpenCloseOCSide.jpg; sourceTree = "<group>"; };
		A9A259E2247AE45600F9E361 /* SlideNoMuscle.jpg */ = {isa = PBXFileReference; lastKnownFileType = image.jpeg; path = SlideNoMuscle.jpg; sourceTree = "<group>"; };
		A9A259E8247AE45700F9E361 /* BadGoodAntMolarChip.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; path = BadGoodAntMolarChip.html; sourceTree = "<group>"; };
		A9A259EA247AE45700F9E361 /* BadGoodContactFront.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; path = BadGoodContactFront.html; sourceTree = "<group>"; };
		A9A259EC247AE45700F9E361 /* BadGrindSideMusclesCU.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; path = BadGrindSideMusclesCU.html; sourceTree = "<group>"; };
		A9A259EE247AE45700F9E361 /* GoodAntRotateSideFront.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; path = GoodAntRotateSideFront.html; sourceTree = "<group>"; };
		A9A259F0247AE45700F9E361 /* GoodContactAllQtr4Views.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; path = GoodContactAllQtr4Views.html; sourceTree = "<group>"; };
		A9A259F2247AE45700F9E361 /* GoodOpenCloseOCSide.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; path = GoodOpenCloseOCSide.html; sourceTree = "<group>"; };
		A9A259F4247AE45700F9E361 /* SlideNoMuscle.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; path = SlideNoMuscle.html; sourceTree = "<group>"; };
		A9A259F7247AE45700F9E361 /* Animation Panel 512x384.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "Animation Panel 512x384.png"; sourceTree = "<group>"; };
		A9A259F9247AE45700F9E361 /* dt.css */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.css; path = dt.css; sourceTree = "<group>"; };
		A9A259FA247AE45700F9E361 /* ExamplePresentationTemplate.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; path = ExamplePresentationTemplate.html; sourceTree = "<group>"; };
		A9A259FB247AE45700F9E361 /* Menu-Button.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "Menu-Button.png"; sourceTree = "<group>"; };
		A9A259FC247AE45700F9E361 /* NextButton.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = NextButton.png; sourceTree = "<group>"; };
		A9A259FD247AE45700F9E361 /* PlayButton.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = PlayButton.png; sourceTree = "<group>"; };
		A9A259FE247AE45700F9E361 /* Slider-Control.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "Slider-Control.png"; sourceTree = "<group>"; };
		A9A25A00247AE45700F9E361 /* CanineGd.jpg */ = {isa = PBXFileReference; lastKnownFileType = image.jpeg; path = CanineGd.jpg; sourceTree = "<group>"; };
		A9A25A01247AE45700F9E361 /* MIPvsCR2.jpg */ = {isa = PBXFileReference; lastKnownFileType = image.jpeg; path = MIPvsCR2.jpg; sourceTree = "<group>"; };
		A9A25A02247AE45700F9E361 /* WearFacets.jpg */ = {isa = PBXFileReference; lastKnownFileType = image.jpeg; path = WearFacets.jpg; sourceTree = "<group>"; };
		A9B8963D20A1B622009DC519 /* Launch Screen.storyboard */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; path = "Launch Screen.storyboard"; sourceTree = "<group>"; };
		A9BABE89249C901C006933FB /* Slider-Control-109x60.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "Slider-Control-109x60.png"; sourceTree = "<group>"; };
		A9BABE8A249C901C006933FB /* Next-Button-84x60.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "Next-Button-84x60.png"; sourceTree = "<group>"; };
		A9BABE8B249C901C006933FB /* Menu-Button_110x59.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "Menu-Button_110x59.png"; sourceTree = "<group>"; };
		A9BABE8C249C901C006933FB /* Play-Button-84x58.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "Play-Button-84x58.png"; sourceTree = "<group>"; };
		A9BABE8D249C901C006933FB /* Presentation-Info-Button.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "Presentation-Info-Button.png"; sourceTree = "<group>"; };
		A9C7D52820A086D400B54873 /* AFTableViewCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AFTableViewCell.h; sourceTree = "<group>"; };
		A9C7D52920A086D400B54873 /* AFTableViewCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AFTableViewCell.m; sourceTree = "<group>"; };
		A9C7D53A20A086D400B54873 /* AnimationPanelVC.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AnimationPanelVC.h; sourceTree = "<group>"; };
		A9C7D53B20A086D400B54873 /* AnimationPanelVC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AnimationPanelVC.m; sourceTree = "<group>"; };
		A9C7D53C20A086D400B54873 /* AnimationPanelVC.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = AnimationPanelVC.xib; sourceTree = "<group>"; };
		A9C7D53D20A086D400B54873 /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		A9C7D53E20A086D400B54873 /* AppDelegate.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AppDelegate.m; sourceTree = "<group>"; };
		A9C7D55720A086D500B54873 /* BIteFXproject.entitlements */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.entitlements; path = BIteFXproject.entitlements; sourceTree = "<group>"; };
		A9C7D55820A086D500B54873 /* BiteFXWelcomeAndHelp.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; path = BiteFXWelcomeAndHelp.html; sourceTree = "<group>"; };
		A9C7D55A20A086D500B54873 /* AppConstant.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AppConstant.h; sourceTree = "<group>"; };
		A9C7D56220A086D500B54873 /* PurchaseOptionCustomCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PurchaseOptionCustomCell.h; sourceTree = "<group>"; };
		A9C7D56320A086D500B54873 /* PurchaseOptionCustomCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PurchaseOptionCustomCell.m; sourceTree = "<group>"; };
		A9C7D56420A086D500B54873 /* PurchaseOptionCustomCell.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = PurchaseOptionCustomCell.xib; sourceTree = "<group>"; };
		A9C7D56520A086D500B54873 /* skipCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = skipCell.h; sourceTree = "<group>"; };
		A9C7D56620A086D500B54873 /* skipCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = skipCell.m; sourceTree = "<group>"; };
		A9C7D56720A086D500B54873 /* skipCell.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = skipCell.xib; sourceTree = "<group>"; };
		A9C7D56820A086D500B54873 /* UITableViewCell+NIB.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UITableViewCell+NIB.h"; sourceTree = "<group>"; };
		A9C7D56920A086D500B54873 /* UITableViewCell+NIB.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UITableViewCell+NIB.m"; sourceTree = "<group>"; };
		A9C7D56A20A086D500B54873 /* UpdateCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpdateCell.h; sourceTree = "<group>"; };
		A9C7D56B20A086D500B54873 /* UpdateCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpdateCell.m; sourceTree = "<group>"; };
		A9C7D56C20A086D500B54873 /* UpdateCell.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = UpdateCell.xib; sourceTree = "<group>"; };
		A9C7D56D20A086D500B54873 /* CustomLabel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CustomLabel.h; sourceTree = "<group>"; };
		A9C7D56E20A086D500B54873 /* CustomLabel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CustomLabel.m; sourceTree = "<group>"; };
		A9C7D57620A086D500B54873 /* Devider_image.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = Devider_image.png; sourceTree = "<group>"; };
		A9C7D58120A086D500B54873 /* Download.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Download.h; sourceTree = "<group>"; };
		A9C7D58220A086D500B54873 /* Download.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = Download.m; sourceTree = "<group>"; };
		A9C7D58320A086D500B54873 /* DownloadManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DownloadManager.h; sourceTree = "<group>"; };
		A9C7D58420A086D500B54873 /* DownloadManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DownloadManager.m; sourceTree = "<group>"; };
		A9C7D58820A086D500B54873 /* AlwaysOpaqueImageView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AlwaysOpaqueImageView.h; sourceTree = "<group>"; };
		A9C7D58920A086D500B54873 /* AlwaysOpaqueImageView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AlwaysOpaqueImageView.m; sourceTree = "<group>"; };
		A9C7D58A20A086D500B54873 /* UIImageView+ForScrollView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIImageView+ForScrollView.h"; sourceTree = "<group>"; };
		A9C7D58B20A086D500B54873 /* UIImageView+ForScrollView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIImageView+ForScrollView.m"; sourceTree = "<group>"; };
		A9C7D59020A086D500B54873 /* Constant.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Constant.h; sourceTree = "<group>"; };
		A9C7D59120A086D500B54873 /* ICloud.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ICloud.h; sourceTree = "<group>"; };
		A9C7D59220A086D500B54873 /* ICloud.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ICloud.m; sourceTree = "<group>"; };
		A9C7D59320A086D500B54873 /* iCloudDoc.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = iCloudDoc.h; sourceTree = "<group>"; };
		A9C7D59420A086D500B54873 /* iCloudDoc.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = iCloudDoc.m; sourceTree = "<group>"; };
		A9C7D59620A086D500B54873 /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Images.xcassets; sourceTree = "<group>"; };
		A9C7D59820A086D500B54873 /* 02.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 02.png; sourceTree = "<group>"; };
		A9C7D59920A086D500B54873 /* 03.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 03.png; sourceTree = "<group>"; };
		A9C7D59A20A086D500B54873 /* buttoniPad.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = buttoniPad.png; sourceTree = "<group>"; };
		A9C7D59B20A086D500B54873 /* nextFrame.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = nextFrame.png; sourceTree = "<group>"; };
		A9C7D59C20A086D500B54873 /* PlayIMAGE.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = PlayIMAGE.png; sourceTree = "<group>"; };
		A9C7D59D20A086D500B54873 /* playimg.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = playimg.png; sourceTree = "<group>"; };
		A9C7D59E20A086D500B54873 /* previousFrame.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = previousFrame.png; sourceTree = "<group>"; };
		A9C7D59F20A086D500B54873 /* roundImg.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = roundImg.png; sourceTree = "<group>"; };
		A9C7D5A020A086D500B54873 /* RoundiPad.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = RoundiPad.png; sourceTree = "<group>"; };
		A9C7D5A220A086D500B54873 /* btnClose.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = btnClose.png; sourceTree = "<group>"; };
		A9C7D5A320A086D500B54873 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		A9C7D5A420A086D500B54873 /* btnCopy.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = btnCopy.png; sourceTree = "<group>"; };
		A9C7D5A520A086D500B54873 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		A9C7D5A620A086D500B54873 /* btnDeleteAlbum.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = btnDeleteAlbum.png; sourceTree = "<group>"; };
		A9C7D5A720A086D500B54873 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		A9C7D5A820A086D500B54873 /* btnDeleteImage.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = btnDeleteImage.png; sourceTree = "<group>"; };
		A9C7D5A920A086D500B54873 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		A9C7D5AA20A086D500B54873 /* btnInfo.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = btnInfo.png; sourceTree = "<group>"; };
		A9C7D5AB20A086D500B54873 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		A9C7D5AC20A086D500B54873 /* btnInfoSelected.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = btnInfoSelected.png; sourceTree = "<group>"; };
		A9C7D5AD20A086D500B54873 /* btnInfoSelected1.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = btnInfoSelected1.png; sourceTree = "<group>"; };
		A9C7D5AE20A086D500B54873 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		A9C7D5AF20A086D500B54873 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		A9C7D5B120A086D500B54873 /* BiteFX_Help.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = BiteFX_Help.png; sourceTree = "<group>"; };
		A9C7D5B220A086D500B54873 /* BiteFXLogoSmall.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = BiteFXLogoSmall.png; sourceTree = "<group>"; };
		A9C7D5B320A086D500B54873 /* BiteFXMainHelpPlayArea.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = BiteFXMainHelpPlayArea.png; sourceTree = "<group>"; };
		A9C7D5B420A086D500B54873 /* BiteFXMainHelpPlayArea2.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = BiteFXMainHelpPlayArea2.png; sourceTree = "<group>"; };
		A9C7D5B520A086D500B54873 /* BiteFXMainHelpPlayArea3.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = BiteFXMainHelpPlayArea3.png; sourceTree = "<group>"; };
		A9C7D5B620A086D500B54873 /* BiteFXMainHelpSelection1.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = BiteFXMainHelpSelection1.png; sourceTree = "<group>"; };
		A9C7D5B720A086D500B54873 /* FrameCounter.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = FrameCounter.png; sourceTree = "<group>"; };
		A9C7D5B920A086D500B54873 /* AnimationButton.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = AnimationButton.png; sourceTree = "<group>"; };
		A9C7D5BA20A086D500B54873 /* Help.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = Help.png; sourceTree = "<group>"; };
		A9C7D5BB20A086D500B54873 /* Info.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = Info.png; sourceTree = "<group>"; };
		A9C7D5BC20A086D500B54873 /* LockButton.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = LockButton.png; sourceTree = "<group>"; };
		A9C7D5BD20A086D500B54873 /* locked.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = locked.png; sourceTree = "<group>"; };
		A9C7D5BE20A086D500B54873 /* LoopButtonImg.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = LoopButtonImg.png; sourceTree = "<group>"; };
		A9C7D5BF20A086D500B54873 /* Menu.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = Menu.png; sourceTree = "<group>"; };
		A9C7D5C020A086D500B54873 /* NextButtonImg.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = NextButtonImg.png; sourceTree = "<group>"; };
		A9C7D5C120A086D500B54873 /* PlayButtonImg.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = PlayButtonImg.png; sourceTree = "<group>"; };
		A9C7D5C220A086D500B54873 /* PreviousButton.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = PreviousButton.png; sourceTree = "<group>"; };
		A9C7D5C320A086D500B54873 /* SliderKnob.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = SliderKnob.png; sourceTree = "<group>"; };
		A9C7D5C420A086D500B54873 /* StopButton.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = StopButton.png; sourceTree = "<group>"; };
		A9C7D5C520A086D500B54873 /* unlocked.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = unlocked.png; sourceTree = "<group>"; };
		A9C7D5C720A086D500B54873 /* AnimationPressed.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = AnimationPressed.png; sourceTree = "<group>"; };
		A9C7D5C820A086D500B54873 /* HelpPressed.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = HelpPressed.png; sourceTree = "<group>"; };
		A9C7D5C920A086D500B54873 /* InfoPressed.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = InfoPressed.png; sourceTree = "<group>"; };
		A9C7D5CA20A086D500B54873 /* LockButtonPressed.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = LockButtonPressed.png; sourceTree = "<group>"; };
		A9C7D5CB20A086D500B54873 /* LoopButtonPressedImg.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = LoopButtonPressedImg.png; sourceTree = "<group>"; };
		A9C7D5CC20A086D500B54873 /* MenuPressed.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = MenuPressed.png; sourceTree = "<group>"; };
		A9C7D5CD20A086D500B54873 /* NextButtonPressedImg.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = NextButtonPressedImg.png; sourceTree = "<group>"; };
		A9C7D5CE20A086D500B54873 /* PlayButtonPressed.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = PlayButtonPressed.png; sourceTree = "<group>"; };
		A9C7D5CF20A086D500B54873 /* PreviousPressed.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = PreviousPressed.png; sourceTree = "<group>"; };
		A9C7D5D020A086D500B54873 /* SliderKnobPressed.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = SliderKnobPressed.png; sourceTree = "<group>"; };
		A9C7D5D120A086D500B54873 /* StopButtonPressed.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = StopButtonPressed.png; sourceTree = "<group>"; };
		A9C7D5D220A086D500B54873 /* unlockedPresses.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = unlockedPresses.png; sourceTree = "<group>"; };
		A9C7D5D320A086D500B54873 /* resume-download.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "resume-download.png"; sourceTree = "<group>"; };
		A9C7D5D420A086D500B54873 /* resume-download1.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "resume-download1.png"; sourceTree = "<group>"; };
		A9C7D5D520A086D500B54873 /* resume-download2.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "resume-download2.png"; sourceTree = "<group>"; };
		A9C7D5D620A086D500B54873 /* SliderArea.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = SliderArea.png; sourceTree = "<group>"; };
		A9C7D5D720A086D500B54873 /* SliderContainer.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = SliderContainer.png; sourceTree = "<group>"; };
		A9C7D5D820A086D500B54873 /* SliderFilled.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = SliderFilled.png; sourceTree = "<group>"; };
		A9C7D5D920A086D500B54873 /* SliderSpeed.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = SliderSpeed.png; sourceTree = "<group>"; };
		A9C7D5DA20A086D500B54873 /* SliderThumb.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = SliderThumb.png; sourceTree = "<group>"; };
		A9C7D5DB20A086D500B54873 /* SpeedIndicator.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = SpeedIndicator.png; sourceTree = "<group>"; };
		A9C7D5DC20A086D500B54873 /* SpeedSlider.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = SpeedSlider.png; sourceTree = "<group>"; };
		A9C7D5DD20A086D500B54873 /* SpeedSliderKnob.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = SpeedSliderKnob.png; sourceTree = "<group>"; };
		A9C7D5DE20A086D500B54873 /* toolbar.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = toolbar.png; sourceTree = "<group>"; };
		A9C7D5DF20A086D500B54873 /* toolbar1.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = toolbar1.png; sourceTree = "<group>"; };
		A9C7D5E020A086D500B54873 /* gray.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = gray.png; sourceTree = "<group>"; };
		A9C7D5E120A086D500B54873 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		A9C7D5E320A086D500B54873 /* Main_screen_with_animation_displayed.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = Main_screen_with_animation_displayed.png; sourceTree = "<group>"; };
		A9C7D5E420A086D500B54873 /* Main_screen_with_picture_displayed.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = Main_screen_with_picture_displayed.png; sourceTree = "<group>"; };
		A9C7D5E520A086D500B54873 /* Selection_panel_with animations_tab_selected.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "Selection_panel_with animations_tab_selected.png"; sourceTree = "<group>"; };
		A9C7D5E620A086D500B54873 /* Selection_panel_with_pictures_tab_selected.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = Selection_panel_with_pictures_tab_selected.png; sourceTree = "<group>"; };
		A9C7D5E720A086D500B54873 /* Selection_panel_with_presentation_templates_tab_selected.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = Selection_panel_with_presentation_templates_tab_selected.png; sourceTree = "<group>"; };
		A9C7D5E820A086D500B54873 /* LoopButton.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = LoopButton.png; sourceTree = "<group>"; };
		A9C7D5E920A086D500B54873 /* LoopButtonNew.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = LoopButtonNew.png; sourceTree = "<group>"; };
		A9C7D5EA20A086D500B54873 /* LoopButtonPressed.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = LoopButtonPressed.png; sourceTree = "<group>"; };
		A9C7D5EB20A086D500B54873 /* LoopButtonPressedNew.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = LoopButtonPressedNew.png; sourceTree = "<group>"; };
		A9C7D5ED20A086D500B54873 /* About.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = About.png; sourceTree = "<group>"; };
		A9C7D5EE20A086D500B54873 /* badge-big.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "badge-big.png"; sourceTree = "<group>"; };
		A9C7D5EF20A086D500B54873 /* badge.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = badge.png; sourceTree = "<group>"; };
		A9C7D5F020A086D500B54873 /* btnDownloadNrml.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = btnDownloadNrml.png; sourceTree = "<group>"; };
		A9C7D5F120A086D500B54873 /* btnDownloadslt.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = btnDownloadslt.png; sourceTree = "<group>"; };
		A9C7D5F220A086D500B54873 /* checkForUpdates-normal.PNG */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "checkForUpdates-normal.PNG"; sourceTree = "<group>"; };
		A9C7D5F320A086D500B54873 /* checkForUpdates-pressed.PNG */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "checkForUpdates-pressed.PNG"; sourceTree = "<group>"; };
		A9C7D5F420A086D500B54873 /* checkForUpdates.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = checkForUpdates.png; sourceTree = "<group>"; };
		A9C7D5F520A086D500B54873 /* checkForUpdatesPressed.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = checkForUpdatesPressed.png; sourceTree = "<group>"; };
		A9C7D5F620A086D500B54873 /* checkUpdates-bg.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "checkUpdates-bg.png"; sourceTree = "<group>"; };
		A9C7D5F720A086D500B54873 /* close-download.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "close-download.png"; sourceTree = "<group>"; };
		A9C7D5F820A086D500B54873 /* Deactivate-hover.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "Deactivate-hover.png"; sourceTree = "<group>"; };
		A9C7D5F920A086D500B54873 /* Deactivate-normal.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "Deactivate-normal.png"; sourceTree = "<group>"; };
		A9C7D5FA20A086D500B54873 /* Deactivate-normalNew.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "Deactivate-normalNew.png"; sourceTree = "<group>"; };
		A9C7D5FB20A086D500B54873 /* Deactivate-pressed.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "Deactivate-pressed.png"; sourceTree = "<group>"; };
		A9C7D5FC20A086D500B54873 /* Deactivate-pressedNew.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "Deactivate-pressedNew.png"; sourceTree = "<group>"; };
		A9C7D5FD20A086D500B54873 /* Default_landscape.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = Default_landscape.png; sourceTree = "<group>"; };
		A9C7D5FE20A086D500B54873 /* Default_portrait.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = Default_portrait.png; sourceTree = "<group>"; };
		A9C7D5FF20A086D500B54873 /* details.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = details.png; sourceTree = "<group>"; };
		A9C7D60020A086D500B54873 /* DialogDownload.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = DialogDownload.png; sourceTree = "<group>"; };
		A9C7D60120A086D500B54873 /* DialogNewDownload.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = DialogNewDownload.png; sourceTree = "<group>"; };
		A9C7D60220A086D500B54873 /* download-bg.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "download-bg.png"; sourceTree = "<group>"; };
		A9C7D60320A086D500B54873 /* download.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = download.png; sourceTree = "<group>"; };
		A9C7D60420A086D500B54873 /* downloadPRessed.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = downloadPRessed.png; sourceTree = "<group>"; };
		A9C7D60520A086D500B54873 /* downloadprogressFilled.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = downloadprogressFilled.png; sourceTree = "<group>"; };
		A9C7D60620A086D500B54873 /* downloadProgressNormal.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = downloadProgressNormal.png; sourceTree = "<group>"; };
		A9C7D60720A086D500B54873 /* downloadUpdates-normal.PNG */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "downloadUpdates-normal.PNG"; sourceTree = "<group>"; };
		A9C7D60820A086D500B54873 /* downloadUpdates-pressed.PNG */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "downloadUpdates-pressed.PNG"; sourceTree = "<group>"; };
		A9C7D60920A086D500B54873 /* dwnloadDialog.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = dwnloadDialog.png; sourceTree = "<group>"; };
		A9C7D60A20A086D500B54873 /* Line.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = Line.png; sourceTree = "<group>"; };
		A9C7D60B20A086D500B54873 /* Line1.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = Line1.png; sourceTree = "<group>"; };
		A9C7D60C20A086D500B54873 /* Line2.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = Line2.png; sourceTree = "<group>"; };
		A9C7D60D20A086D500B54873 /* login-normal.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "login-normal.png"; sourceTree = "<group>"; };
		A9C7D60E20A086D500B54873 /* login-pressed.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "login-pressed.png"; sourceTree = "<group>"; };
		A9C7D60F20A086D500B54873 /* menuPopup.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = menuPopup.png; sourceTree = "<group>"; };
		A9C7D61020A086D500B54873 /* more.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = more.png; sourceTree = "<group>"; };
		A9C7D61120A086D500B54873 /* pauseNormal.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = pauseNormal.png; sourceTree = "<group>"; };
		A9C7D61220A086D500B54873 /* pausePressed.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = pausePressed.png; sourceTree = "<group>"; };
		A9C7D61320A086D500B54873 /* products.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = products.png; sourceTree = "<group>"; };
		A9C7D61420A086D500B54873 /* products1.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = products1.png; sourceTree = "<group>"; };
		A9C7D61520A086D500B54873 /* progressbarFilled.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = progressbarFilled.png; sourceTree = "<group>"; };
		A9C7D61620A086D500B54873 /* progressbarNormal.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = progressbarNormal.png; sourceTree = "<group>"; };
		A9C7D61720A086D500B54873 /* PurchasePrice-normal.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "PurchasePrice-normal.png"; sourceTree = "<group>"; };
		A9C7D61820A086D500B54873 /* PurchasePrice-pressed.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "PurchasePrice-pressed.png"; sourceTree = "<group>"; };
		A9C7D61920A086D500B54873 /* Register-normal.PNG */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "Register-normal.PNG"; sourceTree = "<group>"; };
		A9C7D61A20A086D500B54873 /* Register-pressed.PNG */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "Register-pressed.PNG"; sourceTree = "<group>"; };
		A9C7D61B20A086D500B54873 /* register.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = register.png; sourceTree = "<group>"; };
		A9C7D61C20A086D500B54873 /* register1.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = register1.png; sourceTree = "<group>"; };
		A9C7D61D20A086D500B54873 /* restore.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = restore.png; sourceTree = "<group>"; };
		A9C7D61E20A086D500B54873 /* speed.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = speed.png; sourceTree = "<group>"; };
		A9C7D61F20A086D500B54873 /* speedKnob-Normal.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "speedKnob-Normal.png"; sourceTree = "<group>"; };
		A9C7D62020A086D500B54873 /* speedKnob-Pressed.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "speedKnob-Pressed.png"; sourceTree = "<group>"; };
		A9C7D62120A086D500B54873 /* speedSliderNew.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = speedSliderNew.png; sourceTree = "<group>"; };
		A9C7D62220A086D500B54873 /* speedslidernewimg.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = speedslidernewimg.png; sourceTree = "<group>"; };
		A9C7D62320A086D500B54873 /* SpeedThumbNormal.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = SpeedThumbNormal.png; sourceTree = "<group>"; };
		A9C7D62420A086D500B54873 /* SpeedThumbSelected.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = SpeedThumbSelected.png; sourceTree = "<group>"; };
		A9C7D62520A086D500B54873 /* updates-bg.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "updates-bg.png"; sourceTree = "<group>"; };
		A9C7D62620A086D500B54873 /* updates.PNG */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = updates.PNG; sourceTree = "<group>"; };
		A9C7D62720A086D500B54873 /* updatesNew.PNG */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = updatesNew.PNG; sourceTree = "<group>"; };
		A9C7D62820A086D500B54873 /* X.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = X.png; sourceTree = "<group>"; };
		A9C7D62A20A086D500B54873 /* NextButtonNew.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = NextButtonNew.png; sourceTree = "<group>"; };
		A9C7D62C20A086D500B54873 /* PlayButtonNew.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = PlayButtonNew.png; sourceTree = "<group>"; };
		A9C7D62D20A086D500B54873 /* Red.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = Red.png; sourceTree = "<group>"; };
		A9C7D62E20A086D500B54873 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		A9C7D63020A086D500B54873 /* Active_Animation.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = Active_Animation.png; sourceTree = "<group>"; };
		A9C7D63120A086D500B54873 /* Active_Pictures.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = Active_Pictures.png; sourceTree = "<group>"; };
		A9C7D63220A086D500B54873 /* Active_Presentations.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = Active_Presentations.png; sourceTree = "<group>"; };
		A9C7D63320A086D500B54873 /* btnHide.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = btnHide.png; sourceTree = "<group>"; };
		A9C7D63420A086D500B54873 /* btnUnHide.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = btnUnHide.png; sourceTree = "<group>"; };
		A9C7D63520A086D500B54873 /* Default_Animation.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = Default_Animation.png; sourceTree = "<group>"; };
		A9C7D63620A086D500B54873 /* Default_Pictures.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = Default_Pictures.png; sourceTree = "<group>"; };
		A9C7D63720A086D500B54873 /* Default_Presentations.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = Default_Presentations.png; sourceTree = "<group>"; };
		A9C7D64020A086D500B54873 /* Yellow.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = Yellow.png; sourceTree = "<group>"; };
		A9C7D64120A086D500B54873 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		A9C7D64320A086D500B54873 /* InAppPurchase.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = InAppPurchase.h; sourceTree = "<group>"; };
		A9C7D64420A086D500B54873 /* InAppPurchase.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = InAppPurchase.m; sourceTree = "<group>"; };
		A9C7D64B20A086D500B54873 /* InfoWebView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = InfoWebView.h; sourceTree = "<group>"; };
		A9C7D64C20A086D500B54873 /* InfoWebView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = InfoWebView.m; sourceTree = "<group>"; };
		A9C7D64D20A086D500B54873 /* InfoWebView.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = InfoWebView.xib; sourceTree = "<group>"; };
		A9C7D64F20A086D500B54873 /* JSON.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JSON.h; sourceTree = "<group>"; };
		A9C7D65020A086D500B54873 /* NSObject+SBJSON.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSObject+SBJSON.h"; sourceTree = "<group>"; };
		A9C7D65120A086D500B54873 /* NSObject+SBJSON.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSObject+SBJSON.m"; sourceTree = "<group>"; };
		A9C7D65220A086D500B54873 /* NSString+SBJSON.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSString+SBJSON.h"; sourceTree = "<group>"; };
		A9C7D65320A086D500B54873 /* NSString+SBJSON.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSString+SBJSON.m"; sourceTree = "<group>"; };
		A9C7D65420A086D500B54873 /* SBJSON.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SBJSON.h; sourceTree = "<group>"; };
		A9C7D65520A086D500B54873 /* SBJSON.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SBJSON.m; sourceTree = "<group>"; };
		A9C7D65620A086D500B54873 /* SBJsonBase.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SBJsonBase.h; sourceTree = "<group>"; };
		A9C7D65720A086D500B54873 /* SBJsonBase.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SBJsonBase.m; sourceTree = "<group>"; };
		A9C7D65820A086D500B54873 /* SBJsonParser.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SBJsonParser.h; sourceTree = "<group>"; };
		A9C7D65920A086D500B54873 /* SBJsonParser.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SBJsonParser.m; sourceTree = "<group>"; };
		A9C7D65A20A086D500B54873 /* SBJsonWriter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SBJsonWriter.h; sourceTree = "<group>"; };
		A9C7D65B20A086D500B54873 /* SBJsonWriter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SBJsonWriter.m; sourceTree = "<group>"; };
		A9C7D65C20A086D500B54873 /* leftCorner.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = leftCorner.png; sourceTree = "<group>"; };
		A9C7D65D20A086D500B54873 /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		A9C7D65F20A086D500B54873 /* AnimationView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AnimationView.h; sourceTree = "<group>"; };
		A9C7D66020A086D500B54873 /* AnimationView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AnimationView.m; sourceTree = "<group>"; };
		A9C7D66120A086D500B54873 /* AnimationView.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = AnimationView.xib; sourceTree = "<group>"; };
		A9C7D66220A086D500B54873 /* BiteFXMainScreenVC.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BiteFXMainScreenVC.h; sourceTree = "<group>"; };
		A9C7D66320A086D500B54873 /* BiteFXMainScreenVC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BiteFXMainScreenVC.m; sourceTree = "<group>"; usesTabs = 0; };
		A9C7D66420A086D500B54873 /* BiteFXMainScreenVC.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = BiteFXMainScreenVC.xib; sourceTree = "<group>"; };
		A9C7D6A020A086D500B54873 /* NetworkReachability.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = NetworkReachability.h; sourceTree = "<group>"; };
		A9C7D6A120A086D500B54873 /* NetworkReachability.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = NetworkReachability.m; sourceTree = "<group>"; };
		A9C7D6A420A086D500B54873 /* PlayerView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PlayerView.h; sourceTree = "<group>"; };
		A9C7D6A520A086D500B54873 /* PlayerView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PlayerView.m; sourceTree = "<group>"; };
		A9C7D6A820A086D500B54873 /* Reachability.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Reachability.h; sourceTree = "<group>"; };
		A9C7D6A920A086D500B54873 /* Reachability.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = Reachability.m; sourceTree = "<group>"; };
		A9C7D6AC20A086D500B54873 /* NSString+MD5Addition.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSString+MD5Addition.h"; sourceTree = "<group>"; };
		A9C7D6AD20A086D500B54873 /* NSString+MD5Addition.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSString+MD5Addition.m"; sourceTree = "<group>"; };
		A9C7D6AE20A086D500B54873 /* UIDevice+IdentifierAddition.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIDevice+IdentifierAddition.h"; sourceTree = "<group>"; };
		A9C7D6AF20A086D500B54873 /* UIDevice+IdentifierAddition.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIDevice+IdentifierAddition.m"; sourceTree = "<group>"; };
		A9C7D6B220A086D500B54873 /* Utility.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Utility.h; sourceTree = "<group>"; };
		A9C7D6B320A086D500B54873 /* Utility.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = Utility.m; sourceTree = "<group>"; };
		A9C7D6B520A086D500B54873 /* AddMoreImage.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = AddMoreImage.png; sourceTree = "<group>"; };
		A9C7D6BF20A086D500B54873 /* hiddenpic.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = hiddenpic.png; sourceTree = "<group>"; };
		A9C7D6C820A086D500B54873 /* DownloadFile.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DownloadFile.h; sourceTree = "<group>"; };
		A9C7D6C920A086D500B54873 /* DownloadFile.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DownloadFile.m; sourceTree = "<group>"; };
		A9C7D6CB20A086D500B54873 /* BiteFXiPadBasicInventory.MVI */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xml; path = BiteFXiPadBasicInventory.MVI; sourceTree = "<group>"; };
		A9C7D6CC20A086D500B54873 /* BiteFXiPadFullInventory.mvi */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xml; path = BiteFXiPadFullInventory.mvi; sourceTree = "<group>"; };
		A9C7D6CD20A086D500B54873 /* BiteFXiPadFullInventoryOld.mvi */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xml; path = BiteFXiPadFullInventoryOld.mvi; sourceTree = "<group>"; };
		A9C7D6CE20A086D500B54873 /* XMLParser.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XMLParser.h; sourceTree = "<group>"; };
		A9C7D6CF20A086D500B54873 /* XMLParser.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = XMLParser.m; sourceTree = "<group>"; };
		A9C7D6D020A086D500B54873 /* XMLParserUpdate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XMLParserUpdate.h; sourceTree = "<group>"; };
		A9C7D6D120A086D500B54873 /* XMLParserUpdate.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = XMLParserUpdate.m; sourceTree = "<group>"; };
		A9D7F4A1242B285F007EBAE2 /* IAPManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = IAPManager.h; sourceTree = "<group>"; };
		A9D7F4A2242B285F007EBAE2 /* IAPManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = IAPManager.m; sourceTree = "<group>"; };
		A9DD7F1E27DF0D9100C5B864 /* BiteFXiPad-Subscription.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; path = "BiteFXiPad-Subscription.html"; sourceTree = "<group>"; };
		A9DD7F1F27DF0D9100C5B864 /* BiteFXiPad-Privacy.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; path = "BiteFXiPad-Privacy.html"; sourceTree = "<group>"; };
		A9DD7F2027DF0D9100C5B864 /* BiteFXiPad-Support.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; path = "BiteFXiPad-Support.html"; sourceTree = "<group>"; };
		A9DD7F2127DF0D9100C5B864 /* BiteFXiPad-Terms-and-Conditions.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; path = "BiteFXiPad-Terms-and-Conditions.html"; sourceTree = "<group>"; };
		A9DD7F2627DF0D9F00C5B864 /* dt1.css */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.css; path = dt1.css; sourceTree = "<group>"; };
		A9DD7F2827DF0DF400C5B864 /* Animation Panel.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "Animation Panel.png"; sourceTree = "<group>"; };
		A9DD7F2927DF0DF400C5B864 /* AnimationSelectionTab.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = AnimationSelectionTab.png; sourceTree = "<group>"; };
		A9DD7F2A27DF0DF400C5B864 /* BiteFX-on-iPad-3-1-Issues-and-Treatments-Callout.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "BiteFX-on-iPad-3-1-Issues-and-Treatments-Callout.png"; sourceTree = "<group>"; };
		A9DD7F2B27DF0DF500C5B864 /* Hal-Stewart-on-Patient-Education-Detail-2.jpg */ = {isa = PBXFileReference; lastKnownFileType = image.jpeg; path = "Hal-Stewart-on-Patient-Education-Detail-2.jpg"; sourceTree = "<group>"; };
		A9DD7F2C27DF0DF500C5B864 /* BiteFX-on-iPad-Presentation-Sets.PNG */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "BiteFX-on-iPad-Presentation-Sets.PNG"; sourceTree = "<group>"; };
		A9DD7F2D27DF0DF500C5B864 /* BiteFX-on-iPad-3-1-Dawson-Starter-Callout.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "BiteFX-on-iPad-3-1-Dawson-Starter-Callout.png"; sourceTree = "<group>"; };
		A9DD7F2E27DF0DF500C5B864 /* BiteFX-on-iPad-2-5-18-Picture-Panel.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "BiteFX-on-iPad-2-5-18-Picture-Panel.png"; sourceTree = "<group>"; };
		A9DD7F2F27DF0DF500C5B864 /* Dr TJ Bolt 2020 Detail 148x200.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "Dr TJ Bolt 2020 Detail 148x200.png"; sourceTree = "<group>"; };
		A9DD7F3027DF0DF500C5B864 /* Rick-Rogers-Detail-140x177.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "Rick-Rogers-Detail-140x177.png"; sourceTree = "<group>"; };
		A9DD7F3127DF0DF600C5B864 /* BiteFX-on-iPad-3-1-Detailed-Presentations-Callout.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "BiteFX-on-iPad-3-1-Detailed-Presentations-Callout.png"; sourceTree = "<group>"; };
		A9DD7F3227DF0DF600C5B864 /* BiteFX-on-iPad-2-5-18-Hygienist-Presentation-Templates.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "BiteFX-on-iPad-2-5-18-Hygienist-Presentation-Templates.png"; sourceTree = "<group>"; };
		A9DD7F3327DF0DF600C5B864 /* BiteFX-on-iPad-2-5-18-Presentation-Templates.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "BiteFX-on-iPad-2-5-18-Presentation-Templates.png"; sourceTree = "<group>"; };
		A9DD7F3427DF0DF600C5B864 /* Chris_Toomey_191x256.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = Chris_Toomey_191x256.png; sourceTree = "<group>"; };
		A9DD7F3527DF0DF600C5B864 /* BiteFX-on-iPad-3-1-Staff-Training-Callout.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "BiteFX-on-iPad-3-1-Staff-Training-Callout.png"; sourceTree = "<group>"; };
		A9DD7F3627DF0DF600C5B864 /* BiteFX-on-iPad-3-1-Hygienist-Callout.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "BiteFX-on-iPad-3-1-Hygienist-Callout.png"; sourceTree = "<group>"; };
		A9E3E79F2E0ADA47000E4956 /* crossSelected.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = crossSelected.png; sourceTree = "<group>"; };
		A9E7B5B214D901C300C3D32C /* libsqlite3.0.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libsqlite3.0.dylib; path = usr/lib/libsqlite3.0.dylib; sourceTree = SDKROOT; };
		A9E7F29D20B3061D001BBC06 /* ExternalAccessory.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = ExternalAccessory.framework; path = System/Library/Frameworks/ExternalAccessory.framework; sourceTree = SDKROOT; };
		A9F3FF9E24DD977A00CC61D0 /* Active Tab Presentations v4.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "Active Tab Presentations v4.png"; sourceTree = "<group>"; };
		A9F6A18320A1798000FE3D57 /* DownloadHelper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DownloadHelper.h; sourceTree = "<group>"; };
		A9F6A18420A1798000FE3D57 /* DownloadHelper.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DownloadHelper.m; sourceTree = "<group>"; };
		A9F777382E05653900FFB765 /* favSmall.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = favSmall.png; sourceTree = "<group>"; };
		A9F777392E05653900FFB765 /* unFavSmall.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = unFavSmall.png; sourceTree = "<group>"; };
		A9F7C98C2760DE7200BD7A30 /* FeatureSetVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FeatureSetVC.h; sourceTree = "<group>"; };
		A9F7C98D2760DE7200BD7A30 /* FeatureSetVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FeatureSetVC.m; sourceTree = "<group>"; };
		A9F7C98E2760DE7200BD7A30 /* FeatureSetVC.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = FeatureSetVC.xib; sourceTree = "<group>"; };
		C7729F0D0AD1A3D0CD520BF2 /* Pods-BIteFXproject.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-BIteFXproject.debug.xcconfig"; path = "Pods/Target Support Files/Pods-BIteFXproject/Pods-BIteFXproject.debug.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		81A27D511497483F00C75021 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				023CEC622A1BD922007E51D3 /* QuartzCore.framework in Frameworks */,
				A976DDFB1897BEFD008147B6 /* CoreTelephony.framework in Frameworks */,
				A920820D1619AC7600580279 /* StoreKit.framework in Frameworks */,
				2B251C76155BE2D7004CBB9E /* CFNetwork.framework in Frameworks */,
				2B923AB3151B03FD00DCA7C7 /* libz.1.2.5.dylib in Frameworks */,
				02F8F45C2AFB64E6002E2A5F /* PhotosUI.framework in Frameworks */,
				2B923AAB151B022200DCA7C7 /* libz.1.1.3.dylib in Frameworks */,
				81AD590C1506404F00451EC2 /* CoreVideo.framework in Frameworks */,
				81737A1B1504DC940036086E /* MediaPlayer.framework in Frameworks */,
				81737A181504D9AE0036086E /* AssetsLibrary.framework in Frameworks */,
				810038DC14FCFD4600DF4A70 /* SystemConfiguration.framework in Frameworks */,
				A9E7F29E20B3061D001BBC06 /* ExternalAccessory.framework in Frameworks */,
				A9E7B5B314D901C300C3D32C /* libsqlite3.0.dylib in Frameworks */,
				814B88E31498DA4300A32776 /* CoreMedia.framework in Frameworks */,
				814B88D41498C21000A32776 /* Security.framework in Frameworks */,
				814B88CE1498AB9200A32776 /* MobileCoreServices.framework in Frameworks */,
				814B88C91498AB6100A32776 /* AVFoundation.framework in Frameworks */,
				81A27D591497483F00C75021 /* UIKit.framework in Frameworks */,
				81A27D5B1497483F00C75021 /* Foundation.framework in Frameworks */,
				81A27D5D1497483F00C75021 /* CoreGraphics.framework in Frameworks */,
				6CD6D23B8357F84473B66142 /* libPods-BIteFXproject.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		19071B7A8826D65F67EB088D /* Pods */ = {
			isa = PBXGroup;
			children = (
				C7729F0D0AD1A3D0CD520BF2 /* Pods-BIteFXproject.debug.xcconfig */,
				1A26A5FD725A603889CBA235 /* Pods-BIteFXproject.release.xcconfig */,
			);
			name = Pods;
			sourceTree = "<group>";
		};
		81A27D491497483E00C75021 = {
			isa = PBXGroup;
			children = (
				A9C7D52720A086D400B54873 /* BIteFXproject */,
				81A27D5F1497483F00C75021 /* Supporting Files */,
				81A27D571497483F00C75021 /* Frameworks */,
				81A27D551497483F00C75021 /* Products */,
				19071B7A8826D65F67EB088D /* Pods */,
			);
			sourceTree = "<group>";
		};
		81A27D551497483F00C75021 /* Products */ = {
			isa = PBXGroup;
			children = (
				81A27D541497483F00C75021 /* BiteFX.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		81A27D571497483F00C75021 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				02F8F45B2AFB64E6002E2A5F /* PhotosUI.framework */,
				A9E7F29D20B3061D001BBC06 /* ExternalAccessory.framework */,
				A976DDFA1897BEFD008147B6 /* CoreTelephony.framework */,
				A920820C1619AC7600580279 /* StoreKit.framework */,
				A9E7B5B214D901C300C3D32C /* libsqlite3.0.dylib */,
				2B923AAA151B022200DCA7C7 /* libz.1.1.3.dylib */,
				2B923AB2151B03FD00DCA7C7 /* libz.1.2.5.dylib */,
				2B251C75155BE2D7004CBB9E /* CFNetwork.framework */,
				814B88D31498C21000A32776 /* Security.framework */,
				814B88CB1498AB7500A32776 /* QuartzCore.framework */,
				814B88CD1498AB9200A32776 /* MobileCoreServices.framework */,
				814B88C81498AB6100A32776 /* AVFoundation.framework */,
				814B88E21498DA4300A32776 /* CoreMedia.framework */,
				81AD590B1506404F00451EC2 /* CoreVideo.framework */,
				81737A1A1504DC940036086E /* MediaPlayer.framework */,
				81737A171504D9AE0036086E /* AssetsLibrary.framework */,
				810038DB14FCFD4600DF4A70 /* SystemConfiguration.framework */,
				81A27D581497483F00C75021 /* UIKit.framework */,
				81A27D5A1497483F00C75021 /* Foundation.framework */,
				81A27D5C1497483F00C75021 /* CoreGraphics.framework */,
				A64E6C1AC3A15EE58D874C1E /* libPods-BIteFXproject.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		81A27D5F1497483F00C75021 /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				A929E1CD2DE5E62400DB8085 /* PrivacyInfo.xcprivacy */,
				81A27D601497483F00C75021 /* BIteFXproject-Info.plist */,
				81A27D611497483F00C75021 /* InfoPlist.strings */,
				81A27D641497483F00C75021 /* main.m */,
				81A27D661497483F00C75021 /* BIteFXproject-Prefix.pch */,
			);
			name = "Supporting Files";
			path = BIteFXproject;
			sourceTree = "<group>";
		};
		A96963452DE8988F00AE6CDD /* Search */ = {
			isa = PBXGroup;
			children = (
				A946064F2DEF1A0A001B7F91 /* SearchBar.h */,
				A94606502DEF1A0A001B7F91 /* SearchBar.m */,
				A969634C2DE8995300AE6CDD /* SearchBar.xib */,
			);
			path = Search;
			sourceTree = "<group>";
		};
		A99F59D8246C07DA00F58C42 /* images */ = {
			isa = PBXGroup;
			children = (
				A9DD7F2827DF0DF400C5B864 /* Animation Panel.png */,
				A9DD7F2927DF0DF400C5B864 /* AnimationSelectionTab.png */,
				A9DD7F3227DF0DF600C5B864 /* BiteFX-on-iPad-2-5-18-Hygienist-Presentation-Templates.png */,
				A9DD7F2E27DF0DF500C5B864 /* BiteFX-on-iPad-2-5-18-Picture-Panel.png */,
				A9DD7F3327DF0DF600C5B864 /* BiteFX-on-iPad-2-5-18-Presentation-Templates.png */,
				A9DD7F2D27DF0DF500C5B864 /* BiteFX-on-iPad-3-1-Dawson-Starter-Callout.png */,
				A9DD7F3127DF0DF600C5B864 /* BiteFX-on-iPad-3-1-Detailed-Presentations-Callout.png */,
				A9DD7F3627DF0DF600C5B864 /* BiteFX-on-iPad-3-1-Hygienist-Callout.png */,
				A9DD7F2A27DF0DF400C5B864 /* BiteFX-on-iPad-3-1-Issues-and-Treatments-Callout.png */,
				A9DD7F3527DF0DF600C5B864 /* BiteFX-on-iPad-3-1-Staff-Training-Callout.png */,
				A9DD7F2C27DF0DF500C5B864 /* BiteFX-on-iPad-Presentation-Sets.PNG */,
				A9DD7F3427DF0DF600C5B864 /* Chris_Toomey_191x256.png */,
				A9DD7F2F27DF0DF500C5B864 /* Dr TJ Bolt 2020 Detail 148x200.png */,
				A9DD7F2B27DF0DF500C5B864 /* Hal-Stewart-on-Patient-Education-Detail-2.jpg */,
				A9DD7F3027DF0DF500C5B864 /* Rick-Rogers-Detail-140x177.png */,
			);
			path = images;
			sourceTree = "<group>";
		};
		A9A2056D20A2ED2C007D5386 /* AlertClasses */ = {
			isa = PBXGroup;
			children = (
				023EC4A32A3263C200F165A1 /* ErrorFormatter.m */,
				023EC4A52A32640300F165A1 /* ErrorFormatter.h */,
				A9A2056E20A2ED2C007D5386 /* UIAlertController+QMAlertControl.h */,
				A9A2056F20A2ED2C007D5386 /* UIAlertController+QMAlertControl.m */,
			);
			path = AlertClasses;
			sourceTree = "<group>";
		};
		A9A259B9247AE3B600F9E361 /* Basic3.0 */ = {
			isa = PBXGroup;
			children = (
				A9A259E7247AE45700F9E361 /* Movies */,
				A9A259FF247AE45700F9E361 /* Pictures */,
				A9A259F6247AE45700F9E361 /* Presentations */,
				A9A259DA247AE45600F9E361 /* Thumbnails */,
			);
			path = Basic3.0;
			sourceTree = "<group>";
		};
		A9A259DA247AE45600F9E361 /* Thumbnails */ = {
			isa = PBXGroup;
			children = (
				A9A259DB247AE45600F9E361 /* Movies */,
				A9A259E3247AE45600F9E361 /* Pictures */,
			);
			path = Thumbnails;
			sourceTree = "<group>";
		};
		A9A259DB247AE45600F9E361 /* Movies */ = {
			isa = PBXGroup;
			children = (
				A9A259DC247AE45600F9E361 /* BadGoodAntMolarChip.jpg */,
				A9A259DD247AE45600F9E361 /* BadGoodContactFront.jpg */,
				A9A259DE247AE45600F9E361 /* BadGrindSideMusclesCU.jpg */,
				A9A259DF247AE45600F9E361 /* GoodAntRotateSideFront.jpg */,
				A9A259E0247AE45600F9E361 /* GoodContactAllQtr4Views.jpg */,
				A9A259E1247AE45600F9E361 /* GoodOpenCloseOCSide.jpg */,
				A9A259E2247AE45600F9E361 /* SlideNoMuscle.jpg */,
			);
			path = Movies;
			sourceTree = "<group>";
		};
		A9A259E3247AE45600F9E361 /* Pictures */ = {
			isa = PBXGroup;
			children = (
			);
			path = Pictures;
			sourceTree = "<group>";
		};
		A9A259E7247AE45700F9E361 /* Movies */ = {
			isa = PBXGroup;
			children = (
				A9CC3AB1264EAAB2004D5E8E /* H265 */,
				A9A259E8247AE45700F9E361 /* BadGoodAntMolarChip.html */,
				022F327E28E3BB2200630C8E /* BadGoodAntMolarChip.mp4 */,
				A9A259EA247AE45700F9E361 /* BadGoodContactFront.html */,
				022F328028E3BB3F00630C8E /* BadGoodContactFront.mp4 */,
				A9A259EC247AE45700F9E361 /* BadGrindSideMusclesCU.html */,
				022F328228E3BB6500630C8E /* BadGrindSideMusclesCU.mp4 */,
				A9A259EE247AE45700F9E361 /* GoodAntRotateSideFront.html */,
				022F328428E3BB7E00630C8E /* GoodAntRotateSideFront.mp4 */,
				A9A259F0247AE45700F9E361 /* GoodContactAllQtr4Views.html */,
				022F328628E3BBAC00630C8E /* GoodContactAllQtr4Views.mp4 */,
				A9A259F2247AE45700F9E361 /* GoodOpenCloseOCSide.html */,
				022F328828E3BBC500630C8E /* GoodOpenCloseOCSide.mp4 */,
				A9A259F4247AE45700F9E361 /* SlideNoMuscle.html */,
				022F327C28E3BAF700630C8E /* SlideNoMuscle.mp4 */,
			);
			path = Movies;
			sourceTree = "<group>";
		};
		A9A259F6247AE45700F9E361 /* Presentations */ = {
			isa = PBXGroup;
			children = (
				A9BABE8B249C901C006933FB /* Menu-Button_110x59.png */,
				A9F3FF9E24DD977A00CC61D0 /* Active Tab Presentations v4.png */,
				A9BABE8A249C901C006933FB /* Next-Button-84x60.png */,
				A9BABE8C249C901C006933FB /* Play-Button-84x58.png */,
				A9BABE8D249C901C006933FB /* Presentation-Info-Button.png */,
				A9BABE89249C901C006933FB /* Slider-Control-109x60.png */,
				A9A259F7247AE45700F9E361 /* Animation Panel 512x384.png */,
				A9A259F9247AE45700F9E361 /* dt.css */,
				A9A259FA247AE45700F9E361 /* ExamplePresentationTemplate.html */,
				A9A259FB247AE45700F9E361 /* Menu-Button.png */,
				A9A259FC247AE45700F9E361 /* NextButton.png */,
				A9A259FD247AE45700F9E361 /* PlayButton.png */,
				A9A259FE247AE45700F9E361 /* Slider-Control.png */,
			);
			path = Presentations;
			sourceTree = "<group>";
		};
		A9A259FF247AE45700F9E361 /* Pictures */ = {
			isa = PBXGroup;
			children = (
				A9A25A00247AE45700F9E361 /* CanineGd.jpg */,
				A9A25A01247AE45700F9E361 /* MIPvsCR2.jpg */,
				A9A25A02247AE45700F9E361 /* WearFacets.jpg */,
			);
			path = Pictures;
			sourceTree = "<group>";
		};
		A9B8963A20A1B4EF009DC519 /* Storyboards */ = {
			isa = PBXGroup;
			children = (
				A9B8963D20A1B622009DC519 /* Launch Screen.storyboard */,
			);
			path = Storyboards;
			sourceTree = "<group>";
		};
		A9C7D52720A086D400B54873 /* BIteFXproject */ = {
			isa = PBXGroup;
			children = (
				A96963452DE8988F00AE6CDD /* Search */,
				A9F7C98B2760DDD500BD7A30 /* FeatureSets */,
				A9A259B9247AE3B600F9E361 /* Basic3.0 */,
				A9A2056D20A2ED2C007D5386 /* AlertClasses */,
				A9C7D52820A086D400B54873 /* AFTableViewCell.h */,
				A9C7D52920A086D400B54873 /* AFTableViewCell.m */,
				A9C7D53A20A086D400B54873 /* AnimationPanelVC.h */,
				A9C7D53B20A086D400B54873 /* AnimationPanelVC.m */,
				A9968CA220A960DA0064ED61 /* VideoPlay.h */,
				A9C7D53C20A086D400B54873 /* AnimationPanelVC.xib */,
				A9C7D53D20A086D400B54873 /* AppDelegate.h */,
				A9C7D53E20A086D400B54873 /* AppDelegate.m */,
				A9C7D55720A086D500B54873 /* BIteFXproject.entitlements */,
				A9C7D55820A086D500B54873 /* BiteFXWelcomeAndHelp.html */,
				A9C7D55920A086D500B54873 /* Constant */,
				A9C7D56120A086D500B54873 /* CustomCells */,
				A9C7D56D20A086D500B54873 /* CustomLabel.h */,
				A9C7D56E20A086D500B54873 /* CustomLabel.m */,
				A9C7D57220A086D500B54873 /* Database */,
				A9C7D57620A086D500B54873 /* Devider_image.png */,
				A9C7D57B20A086D500B54873 /* DownloadingClasses */,
				A9C7D58020A086D500B54873 /* DownloadManager */,
				A9C7D58720A086D500B54873 /* ForScrollViewIndicator */,
				A9C7D58F20A086D500B54873 /* iCloud */,
				A9C7D59620A086D500B54873 /* Images.xcassets */,
				A9C7D59720A086D500B54873 /* ImagesDummy */,
				A9C7D5A120A086D500B54873 /* ImagesNew */,
				A9C7D64220A086D500B54873 /* In-App Classes */,
				A9C7D64A20A086D500B54873 /* InfoWebView */,
				A9C7D64E20A086D500B54873 /* JSON */,
				A9C7D65C20A086D500B54873 /* leftCorner.png */,
				A9C7D65D20A086D500B54873 /* main.m */,
				A9C7D65E20A086D500B54873 /* MainScreenVC */,
				A9C7D66520A086D500B54873 /* More Details */,
				A9C7D69F20A086D500B54873 /* NetworkReachability */,
				A9C7D6A320A086D500B54873 /* PlayerView */,
				A9C7D6A720A086D500B54873 /* Reachability */,
				A9C7D6AB20A086D500B54873 /* UIDevice+Addition */,
				A9C7D6B120A086D500B54873 /* Utility */,
				A9C7D6B420A086D500B54873 /* Videos */,
				A9C7D6C520A086D500B54873 /* WebServiceClasses */,
				A9C7D6CA20A086D500B54873 /* XML Parser */,
				A9B8963A20A1B4EF009DC519 /* Storyboards */,
			);
			path = BIteFXproject;
			sourceTree = "<group>";
		};
		A9C7D55920A086D500B54873 /* Constant */ = {
			isa = PBXGroup;
			children = (
				A9C7D55A20A086D500B54873 /* AppConstant.h */,
			);
			path = Constant;
			sourceTree = "<group>";
		};
		A9C7D56120A086D500B54873 /* CustomCells */ = {
			isa = PBXGroup;
			children = (
				A9C7D56220A086D500B54873 /* PurchaseOptionCustomCell.h */,
				A9C7D56320A086D500B54873 /* PurchaseOptionCustomCell.m */,
				A9C7D56420A086D500B54873 /* PurchaseOptionCustomCell.xib */,
				A9C7D56520A086D500B54873 /* skipCell.h */,
				A9C7D56620A086D500B54873 /* skipCell.m */,
				A9C7D56720A086D500B54873 /* skipCell.xib */,
				A9C7D56820A086D500B54873 /* UITableViewCell+NIB.h */,
				A9C7D56920A086D500B54873 /* UITableViewCell+NIB.m */,
				A9C7D56A20A086D500B54873 /* UpdateCell.h */,
				A9C7D56B20A086D500B54873 /* UpdateCell.m */,
				A9C7D56C20A086D500B54873 /* UpdateCell.xib */,
			);
			path = CustomCells;
			sourceTree = "<group>";
		};
		A9C7D57220A086D500B54873 /* Database */ = {
			isa = PBXGroup;
			children = (
				A9A2056720A2C0C8007D5386 /* BITEFXDatabase.sqlite */,
				A9A2056820A2C0C9007D5386 /* Database.h */,
				A9A2056620A2C0C8007D5386 /* Database.m */,
			);
			path = Database;
			sourceTree = "<group>";
		};
		A9C7D57B20A086D500B54873 /* DownloadingClasses */ = {
			isa = PBXGroup;
			children = (
				A9F6A18320A1798000FE3D57 /* DownloadHelper.h */,
				A9F6A18420A1798000FE3D57 /* DownloadHelper.m */,
			);
			path = DownloadingClasses;
			sourceTree = "<group>";
		};
		A9C7D58020A086D500B54873 /* DownloadManager */ = {
			isa = PBXGroup;
			children = (
				A9C7D58120A086D500B54873 /* Download.h */,
				A9C7D58220A086D500B54873 /* Download.m */,
				A9C7D58320A086D500B54873 /* DownloadManager.h */,
				A9C7D58420A086D500B54873 /* DownloadManager.m */,
			);
			path = DownloadManager;
			sourceTree = "<group>";
		};
		A9C7D58720A086D500B54873 /* ForScrollViewIndicator */ = {
			isa = PBXGroup;
			children = (
				A9C7D58820A086D500B54873 /* AlwaysOpaqueImageView.h */,
				A9C7D58920A086D500B54873 /* AlwaysOpaqueImageView.m */,
				A9C7D58A20A086D500B54873 /* UIImageView+ForScrollView.h */,
				A9C7D58B20A086D500B54873 /* UIImageView+ForScrollView.m */,
			);
			path = ForScrollViewIndicator;
			sourceTree = "<group>";
		};
		A9C7D58F20A086D500B54873 /* iCloud */ = {
			isa = PBXGroup;
			children = (
				A9C7D59020A086D500B54873 /* Constant.h */,
				A9C7D59120A086D500B54873 /* ICloud.h */,
				A9C7D59220A086D500B54873 /* ICloud.m */,
				A9C7D59320A086D500B54873 /* iCloudDoc.h */,
				A9C7D59420A086D500B54873 /* iCloudDoc.m */,
			);
			path = iCloud;
			sourceTree = "<group>";
		};
		A9C7D59720A086D500B54873 /* ImagesDummy */ = {
			isa = PBXGroup;
			children = (
				A9C7D59820A086D500B54873 /* 02.png */,
				A9C7D59920A086D500B54873 /* 03.png */,
				A9C7D59A20A086D500B54873 /* buttoniPad.png */,
				A9C7D59B20A086D500B54873 /* nextFrame.png */,
				A9C7D59C20A086D500B54873 /* PlayIMAGE.png */,
				A9C7D59D20A086D500B54873 /* playimg.png */,
				A9C7D59E20A086D500B54873 /* previousFrame.png */,
				A9C7D59F20A086D500B54873 /* roundImg.png */,
				A9C7D5A020A086D500B54873 /* RoundiPad.png */,
			);
			path = ImagesDummy;
			sourceTree = "<group>";
		};
		A9C7D5A120A086D500B54873 /* ImagesNew */ = {
			isa = PBXGroup;
			children = (
				A944A4292DEDC51C0078FB4A /* clearSearch.png */,
				A95914732E0D5961009E23DF /* favMedium.png */,
				A95914742E0D5961009E23DF /* unFavMedium.png */,
				A9F777382E05653900FFB765 /* favSmall.png */,
				A9F777392E05653900FFB765 /* unFavSmall.png */,
				A944A4252DEDC1100078FB4A /* fav.png */,
				A944A4262DEDC1100078FB4A /* unFav.png */,
				A92272892DE99911008133EA /* searchBtn.png */,
				A96963542DE89D3100AE6CDD /* searchbar_base.png */,
				A929E1D32DE5F49200DB8085 /* leftFlip.png */,
				A929E1D42DE5F49200DB8085 /* rightFlip.png */,
				A9C7D5A220A086D500B54873 /* btnClose.png */,
				A9C7D5A320A086D500B54873 /* <EMAIL> */,
				A9C7D5A420A086D500B54873 /* btnCopy.png */,
				A9C7D5A520A086D500B54873 /* <EMAIL> */,
				A9C7D5A620A086D500B54873 /* btnDeleteAlbum.png */,
				A9C7D5A720A086D500B54873 /* <EMAIL> */,
				A9C7D5A820A086D500B54873 /* btnDeleteImage.png */,
				A9C7D5A920A086D500B54873 /* <EMAIL> */,
				A9C7D5AA20A086D500B54873 /* btnInfo.png */,
				A9C7D5AB20A086D500B54873 /* <EMAIL> */,
				A9C7D5AC20A086D500B54873 /* btnInfoSelected.png */,
				A9C7D5AD20A086D500B54873 /* btnInfoSelected1.png */,
				A9C7D5AE20A086D500B54873 /* <EMAIL> */,
				A9C7D5AF20A086D500B54873 /* <EMAIL> */,
				A9C7D5B020A086D500B54873 /* Extracted PNGs 2 */,
				A9C7D5E020A086D500B54873 /* gray.png */,
				A9C7D5E120A086D500B54873 /* <EMAIL> */,
				A9C7D5E220A086D500B54873 /* HelpPanel */,
				A9C7D5E820A086D500B54873 /* LoopButton.png */,
				A9C7D5E920A086D500B54873 /* LoopButtonNew.png */,
				A9C7D5EA20A086D500B54873 /* LoopButtonPressed.png */,
				A9C7D5EB20A086D500B54873 /* LoopButtonPressedNew.png */,
				A9C7D5EC20A086D500B54873 /* NewImages */,
				A9C7D62A20A086D500B54873 /* NextButtonNew.png */,
				A9C7D62C20A086D500B54873 /* PlayButtonNew.png */,
				A9C7D62D20A086D500B54873 /* Red.png */,
				A9C7D62E20A086D500B54873 /* <EMAIL> */,
				A9C7D62F20A086D500B54873 /* SegmentImages */,
				A9C7D64020A086D500B54873 /* Yellow.png */,
				A9C7D64120A086D500B54873 /* <EMAIL> */,
				A922728B2DE9992C008133EA /* closeBtn.png */,
				A9E3E79F2E0ADA47000E4956 /* crossSelected.png */,
				A95914712E0D0D18009E23DF /* leftCornerNew.png */,
			);
			path = ImagesNew;
			sourceTree = "<group>";
		};
		A9C7D5B020A086D500B54873 /* Extracted PNGs 2 */ = {
			isa = PBXGroup;
			children = (
				A9C7D5B120A086D500B54873 /* BiteFX_Help.png */,
				A9C7D5B220A086D500B54873 /* BiteFXLogoSmall.png */,
				A9C7D5B320A086D500B54873 /* BiteFXMainHelpPlayArea.png */,
				A9C7D5B420A086D500B54873 /* BiteFXMainHelpPlayArea2.png */,
				A9C7D5B520A086D500B54873 /* BiteFXMainHelpPlayArea3.png */,
				A9C7D5B620A086D500B54873 /* BiteFXMainHelpSelection1.png */,
				A9C7D5B720A086D500B54873 /* FrameCounter.png */,
				A9C7D5B820A086D500B54873 /* Normal state buttons */,
				A9C7D5C620A086D500B54873 /* Pressed state buttons */,
				A9C7D5D320A086D500B54873 /* resume-download.png */,
				A9C7D5D420A086D500B54873 /* resume-download1.png */,
				A9C7D5D520A086D500B54873 /* resume-download2.png */,
				A9C7D5D620A086D500B54873 /* SliderArea.png */,
				A9C7D5D720A086D500B54873 /* SliderContainer.png */,
				A9C7D5D820A086D500B54873 /* SliderFilled.png */,
				A9C7D5D920A086D500B54873 /* SliderSpeed.png */,
				A9C7D5DA20A086D500B54873 /* SliderThumb.png */,
				A9C7D5DB20A086D500B54873 /* SpeedIndicator.png */,
				A9C7D5DC20A086D500B54873 /* SpeedSlider.png */,
				A9C7D5DD20A086D500B54873 /* SpeedSliderKnob.png */,
				A9C7D5DE20A086D500B54873 /* toolbar.png */,
				A9C7D5DF20A086D500B54873 /* toolbar1.png */,
			);
			path = "Extracted PNGs 2";
			sourceTree = "<group>";
		};
		A9C7D5B820A086D500B54873 /* Normal state buttons */ = {
			isa = PBXGroup;
			children = (
				A9C7D5B920A086D500B54873 /* AnimationButton.png */,
				A9C7D5BA20A086D500B54873 /* Help.png */,
				A9C7D5BB20A086D500B54873 /* Info.png */,
				A9C7D5BC20A086D500B54873 /* LockButton.png */,
				A9C7D5BD20A086D500B54873 /* locked.png */,
				A9C7D5BE20A086D500B54873 /* LoopButtonImg.png */,
				A9C7D5BF20A086D500B54873 /* Menu.png */,
				A9C7D5C020A086D500B54873 /* NextButtonImg.png */,
				A9C7D5C120A086D500B54873 /* PlayButtonImg.png */,
				A9C7D5C220A086D500B54873 /* PreviousButton.png */,
				A9C7D5C320A086D500B54873 /* SliderKnob.png */,
				A9C7D5C420A086D500B54873 /* StopButton.png */,
				A9C7D5C520A086D500B54873 /* unlocked.png */,
			);
			path = "Normal state buttons";
			sourceTree = "<group>";
		};
		A9C7D5C620A086D500B54873 /* Pressed state buttons */ = {
			isa = PBXGroup;
			children = (
				A9C7D5C720A086D500B54873 /* AnimationPressed.png */,
				A9C7D5C820A086D500B54873 /* HelpPressed.png */,
				A9C7D5C920A086D500B54873 /* InfoPressed.png */,
				A9C7D5CA20A086D500B54873 /* LockButtonPressed.png */,
				A9C7D5CB20A086D500B54873 /* LoopButtonPressedImg.png */,
				A9C7D5CC20A086D500B54873 /* MenuPressed.png */,
				A9C7D5CD20A086D500B54873 /* NextButtonPressedImg.png */,
				A9C7D5CE20A086D500B54873 /* PlayButtonPressed.png */,
				A9C7D5CF20A086D500B54873 /* PreviousPressed.png */,
				A9C7D5D020A086D500B54873 /* SliderKnobPressed.png */,
				A9C7D5D120A086D500B54873 /* StopButtonPressed.png */,
				A9C7D5D220A086D500B54873 /* unlockedPresses.png */,
			);
			path = "Pressed state buttons";
			sourceTree = "<group>";
		};
		A9C7D5E220A086D500B54873 /* HelpPanel */ = {
			isa = PBXGroup;
			children = (
				A9C7D5E320A086D500B54873 /* Main_screen_with_animation_displayed.png */,
				A9C7D5E420A086D500B54873 /* Main_screen_with_picture_displayed.png */,
				A9C7D5E520A086D500B54873 /* Selection_panel_with animations_tab_selected.png */,
				A9C7D5E620A086D500B54873 /* Selection_panel_with_pictures_tab_selected.png */,
				A9C7D5E720A086D500B54873 /* Selection_panel_with_presentation_templates_tab_selected.png */,
			);
			path = HelpPanel;
			sourceTree = "<group>";
		};
		A9C7D5EC20A086D500B54873 /* NewImages */ = {
			isa = PBXGroup;
			children = (
				A9C7D5ED20A086D500B54873 /* About.png */,
				A9C7D5EE20A086D500B54873 /* badge-big.png */,
				A9C7D5EF20A086D500B54873 /* badge.png */,
				A9C7D5F020A086D500B54873 /* btnDownloadNrml.png */,
				A9C7D5F120A086D500B54873 /* btnDownloadslt.png */,
				A9C7D5F220A086D500B54873 /* checkForUpdates-normal.PNG */,
				A9C7D5F320A086D500B54873 /* checkForUpdates-pressed.PNG */,
				A9C7D5F420A086D500B54873 /* checkForUpdates.png */,
				A9C7D5F520A086D500B54873 /* checkForUpdatesPressed.png */,
				A9C7D5F620A086D500B54873 /* checkUpdates-bg.png */,
				A9C7D5F720A086D500B54873 /* close-download.png */,
				A9C7D5F820A086D500B54873 /* Deactivate-hover.png */,
				A9C7D5F920A086D500B54873 /* Deactivate-normal.png */,
				A9C7D5FA20A086D500B54873 /* Deactivate-normalNew.png */,
				A9C7D5FB20A086D500B54873 /* Deactivate-pressed.png */,
				A9C7D5FC20A086D500B54873 /* Deactivate-pressedNew.png */,
				A9C7D5FD20A086D500B54873 /* Default_landscape.png */,
				A9C7D5FE20A086D500B54873 /* Default_portrait.png */,
				A9C7D5FF20A086D500B54873 /* details.png */,
				A9C7D60020A086D500B54873 /* DialogDownload.png */,
				A9C7D60120A086D500B54873 /* DialogNewDownload.png */,
				A9C7D60220A086D500B54873 /* download-bg.png */,
				A9C7D60320A086D500B54873 /* download.png */,
				A9C7D60420A086D500B54873 /* downloadPRessed.png */,
				A9C7D60520A086D500B54873 /* downloadprogressFilled.png */,
				A9C7D60620A086D500B54873 /* downloadProgressNormal.png */,
				A9C7D60720A086D500B54873 /* downloadUpdates-normal.PNG */,
				A9C7D60820A086D500B54873 /* downloadUpdates-pressed.PNG */,
				A9C7D60920A086D500B54873 /* dwnloadDialog.png */,
				A9C7D60A20A086D500B54873 /* Line.png */,
				A9C7D60B20A086D500B54873 /* Line1.png */,
				A9C7D60C20A086D500B54873 /* Line2.png */,
				A9C7D60D20A086D500B54873 /* login-normal.png */,
				A9C7D60E20A086D500B54873 /* login-pressed.png */,
				A9C7D60F20A086D500B54873 /* menuPopup.png */,
				A9C7D61020A086D500B54873 /* more.png */,
				A9C7D61120A086D500B54873 /* pauseNormal.png */,
				A9C7D61220A086D500B54873 /* pausePressed.png */,
				A9C7D61320A086D500B54873 /* products.png */,
				A9C7D61420A086D500B54873 /* products1.png */,
				A9C7D61520A086D500B54873 /* progressbarFilled.png */,
				A9C7D61620A086D500B54873 /* progressbarNormal.png */,
				A9C7D61720A086D500B54873 /* PurchasePrice-normal.png */,
				A9C7D61820A086D500B54873 /* PurchasePrice-pressed.png */,
				A9C7D61920A086D500B54873 /* Register-normal.PNG */,
				A9C7D61A20A086D500B54873 /* Register-pressed.PNG */,
				A9C7D61B20A086D500B54873 /* register.png */,
				A9C7D61C20A086D500B54873 /* register1.png */,
				A9C7D61D20A086D500B54873 /* restore.png */,
				A9C7D61E20A086D500B54873 /* speed.png */,
				A9C7D61F20A086D500B54873 /* speedKnob-Normal.png */,
				A9C7D62020A086D500B54873 /* speedKnob-Pressed.png */,
				A9C7D62120A086D500B54873 /* speedSliderNew.png */,
				A9C7D62220A086D500B54873 /* speedslidernewimg.png */,
				A9C7D62320A086D500B54873 /* SpeedThumbNormal.png */,
				A9C7D62420A086D500B54873 /* SpeedThumbSelected.png */,
				02891E172928327B00ABC609 /* SpeedThumbDisabled.png */,
				A9C7D62520A086D500B54873 /* updates-bg.png */,
				A9C7D62620A086D500B54873 /* updates.PNG */,
				A9C7D62720A086D500B54873 /* updatesNew.PNG */,
				A9C7D62820A086D500B54873 /* X.png */,
			);
			path = NewImages;
			sourceTree = "<group>";
		};
		A9C7D62F20A086D500B54873 /* SegmentImages */ = {
			isa = PBXGroup;
			children = (
				A9C7D63020A086D500B54873 /* Active_Animation.png */,
				A9C7D63120A086D500B54873 /* Active_Pictures.png */,
				A9C7D63220A086D500B54873 /* Active_Presentations.png */,
				A9C7D63320A086D500B54873 /* btnHide.png */,
				A9C7D63420A086D500B54873 /* btnUnHide.png */,
				A9C7D63520A086D500B54873 /* Default_Animation.png */,
				A9C7D63620A086D500B54873 /* Default_Pictures.png */,
				A9C7D63720A086D500B54873 /* Default_Presentations.png */,
			);
			path = SegmentImages;
			sourceTree = "<group>";
		};
		A9C7D64220A086D500B54873 /* In-App Classes */ = {
			isa = PBXGroup;
			children = (
				A9C7D64320A086D500B54873 /* InAppPurchase.h */,
				A9C7D64420A086D500B54873 /* InAppPurchase.m */,
				A9D7F4A1242B285F007EBAE2 /* IAPManager.h */,
				A9D7F4A2242B285F007EBAE2 /* IAPManager.m */,
			);
			path = "In-App Classes";
			sourceTree = "<group>";
		};
		A9C7D64A20A086D500B54873 /* InfoWebView */ = {
			isa = PBXGroup;
			children = (
				A9C7D64B20A086D500B54873 /* InfoWebView.h */,
				A9C7D64C20A086D500B54873 /* InfoWebView.m */,
				A9C7D64D20A086D500B54873 /* InfoWebView.xib */,
			);
			path = InfoWebView;
			sourceTree = "<group>";
		};
		A9C7D64E20A086D500B54873 /* JSON */ = {
			isa = PBXGroup;
			children = (
				A9C7D64F20A086D500B54873 /* JSON.h */,
				A9C7D65020A086D500B54873 /* NSObject+SBJSON.h */,
				A9C7D65120A086D500B54873 /* NSObject+SBJSON.m */,
				A9C7D65220A086D500B54873 /* NSString+SBJSON.h */,
				A9C7D65320A086D500B54873 /* NSString+SBJSON.m */,
				A9C7D65420A086D500B54873 /* SBJSON.h */,
				A9C7D65520A086D500B54873 /* SBJSON.m */,
				A9C7D65620A086D500B54873 /* SBJsonBase.h */,
				A9C7D65720A086D500B54873 /* SBJsonBase.m */,
				A9C7D65820A086D500B54873 /* SBJsonParser.h */,
				A9C7D65920A086D500B54873 /* SBJsonParser.m */,
				A9C7D65A20A086D500B54873 /* SBJsonWriter.h */,
				A9C7D65B20A086D500B54873 /* SBJsonWriter.m */,
			);
			path = JSON;
			sourceTree = "<group>";
		};
		A9C7D65E20A086D500B54873 /* MainScreenVC */ = {
			isa = PBXGroup;
			children = (
				A9C7D65F20A086D500B54873 /* AnimationView.h */,
				A9C7D66020A086D500B54873 /* AnimationView.m */,
				A9C7D66120A086D500B54873 /* AnimationView.xib */,
				A9C7D66220A086D500B54873 /* BiteFXMainScreenVC.h */,
				A9C7D66320A086D500B54873 /* BiteFXMainScreenVC.m */,
				A9C7D66420A086D500B54873 /* BiteFXMainScreenVC.xib */,
			);
			path = MainScreenVC;
			sourceTree = "<group>";
		};
		A9C7D66520A086D500B54873 /* More Details */ = {
			isa = PBXGroup;
			children = (
				A9C7D66620A086D500B54873 /* InAppPurchaseProductDescriptions */,
			);
			path = "More Details";
			sourceTree = "<group>";
		};
		A9C7D66620A086D500B54873 /* InAppPurchaseProductDescriptions */ = {
			isa = PBXGroup;
			children = (
				A9DD7F2627DF0D9F00C5B864 /* dt1.css */,
				A9DD7F1F27DF0D9100C5B864 /* BiteFXiPad-Privacy.html */,
				A9DD7F1E27DF0D9100C5B864 /* BiteFXiPad-Subscription.html */,
				A9DD7F2027DF0D9100C5B864 /* BiteFXiPad-Support.html */,
				A9DD7F2127DF0D9100C5B864 /* BiteFXiPad-Terms-and-Conditions.html */,
				A99F59D8246C07DA00F58C42 /* images */,
			);
			path = InAppPurchaseProductDescriptions;
			sourceTree = "<group>";
		};
		A9C7D69F20A086D500B54873 /* NetworkReachability */ = {
			isa = PBXGroup;
			children = (
				A9C7D6A020A086D500B54873 /* NetworkReachability.h */,
				A9C7D6A120A086D500B54873 /* NetworkReachability.m */,
			);
			path = NetworkReachability;
			sourceTree = "<group>";
		};
		A9C7D6A320A086D500B54873 /* PlayerView */ = {
			isa = PBXGroup;
			children = (
				A9C7D6A420A086D500B54873 /* PlayerView.h */,
				A9C7D6A520A086D500B54873 /* PlayerView.m */,
			);
			path = PlayerView;
			sourceTree = "<group>";
		};
		A9C7D6A720A086D500B54873 /* Reachability */ = {
			isa = PBXGroup;
			children = (
				A9C7D6A820A086D500B54873 /* Reachability.h */,
				A9C7D6A920A086D500B54873 /* Reachability.m */,
			);
			path = Reachability;
			sourceTree = "<group>";
		};
		A9C7D6AB20A086D500B54873 /* UIDevice+Addition */ = {
			isa = PBXGroup;
			children = (
				A9C7D6AC20A086D500B54873 /* NSString+MD5Addition.h */,
				A9C7D6AD20A086D500B54873 /* NSString+MD5Addition.m */,
				A9C7D6AE20A086D500B54873 /* UIDevice+IdentifierAddition.h */,
				A9C7D6AF20A086D500B54873 /* UIDevice+IdentifierAddition.m */,
			);
			path = "UIDevice+Addition";
			sourceTree = "<group>";
		};
		A9C7D6B120A086D500B54873 /* Utility */ = {
			isa = PBXGroup;
			children = (
				A9C7D6B220A086D500B54873 /* Utility.h */,
				A9C7D6B320A086D500B54873 /* Utility.m */,
			);
			path = Utility;
			sourceTree = "<group>";
		};
		A9C7D6B420A086D500B54873 /* Videos */ = {
			isa = PBXGroup;
			children = (
				A9C7D6B520A086D500B54873 /* AddMoreImage.png */,
				A9C7D6BF20A086D500B54873 /* hiddenpic.png */,
			);
			path = Videos;
			sourceTree = "<group>";
		};
		A9C7D6C520A086D500B54873 /* WebServiceClasses */ = {
			isa = PBXGroup;
			children = (
				A970030120A1720D009E14BE /* CallWebService.h */,
				A970030220A1720D009E14BE /* CallWebService.m */,
				A9C7D6C820A086D500B54873 /* DownloadFile.h */,
				A9C7D6C920A086D500B54873 /* DownloadFile.m */,
				A9A1B7D820F7585800FE39E3 /* WebService.h */,
				A9A1B7D920F7585800FE39E3 /* WebService.m */,
			);
			path = WebServiceClasses;
			sourceTree = "<group>";
		};
		A9C7D6CA20A086D500B54873 /* XML Parser */ = {
			isa = PBXGroup;
			children = (
				A95D0090274E5715004DA043 /* Features.MVI */,
				A91D28CE20AAE4DF000AFC20 /* BiteFxUpdate.MVI */,
				A9C7D6CB20A086D500B54873 /* BiteFXiPadBasicInventory.MVI */,
				A9C7D6CC20A086D500B54873 /* BiteFXiPadFullInventory.mvi */,
				A9C7D6CD20A086D500B54873 /* BiteFXiPadFullInventoryOld.mvi */,
				A9C7D6CE20A086D500B54873 /* XMLParser.h */,
				A9C7D6CF20A086D500B54873 /* XMLParser.m */,
				A9C7D6D020A086D500B54873 /* XMLParserUpdate.h */,
				A9C7D6D120A086D500B54873 /* XMLParserUpdate.m */,
				A98BA955274D2770008B4230 /* XMLParserFeature.h */,
				A98BA956274D2770008B4230 /* XMLParserFeature.m */,
			);
			path = "XML Parser";
			sourceTree = "<group>";
		};
		A9CC3AB1264EAAB2004D5E8E /* H265 */ = {
			isa = PBXGroup;
			children = (
				A9537EC126A1A1F900D48CB0 /* SlideNoMuscle.mp4 */,
				A9537EB626A1A1C500D48CB0 /* BadGoodAntMolarChip.mp4 */,
				A9537EB526A1A1C500D48CB0 /* BadGoodContactFront.mp4 */,
				A9537EB726A1A1C500D48CB0 /* BadGrindSideMusclesCU.mp4 */,
				A9537EB426A1A1C400D48CB0 /* GoodAntRotateSideFront.mp4 */,
				A9537EB926A1A1C500D48CB0 /* GoodContactAllQtr4Views.mp4 */,
				A9537EB826A1A1C500D48CB0 /* GoodOpenCloseOCSide.mp4 */,
				A95200F82690315F0026648A /* JAMSS-8KF.mp4 */,
				A95200F22690315E0026648A /* JAMSS-16KF.mp4 */,
				A95200F32690315E0026648A /* JAMSS-32KF.mp4 */,
				A95200F72690315E0026648A /* JAMSS-63KF.mp4 */,
				A95200F62690315E0026648A /* JAMSS-119KF.mp4 */,
				A95200F52690315E0026648A /* JAMSS.mp4 */,
			);
			path = H265;
			sourceTree = "<group>";
		};
		A9F7C98B2760DDD500BD7A30 /* FeatureSets */ = {
			isa = PBXGroup;
			children = (
				A9F7C98C2760DE7200BD7A30 /* FeatureSetVC.h */,
				A9F7C98D2760DE7200BD7A30 /* FeatureSetVC.m */,
				A9F7C98E2760DE7200BD7A30 /* FeatureSetVC.xib */,
				A99F2B912768D8B80086E9A6 /* FeatureSetDropDownView.h */,
				A99F2B922768D8B80086E9A6 /* FeatureSetDropDownView.m */,
				A99F2B952768D8D40086E9A6 /* FeatureSetDropDownView.xib */,
			);
			path = FeatureSets;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		81A27D531497483F00C75021 /* BIteFXproject */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 81A27D871497483F00C75021 /* Build configuration list for PBXNativeTarget "BIteFXproject" */;
			buildPhases = (
				1A3CCDF36F75768FDFD99D46 /* [CP] Check Pods Manifest.lock */,
				81A27D501497483F00C75021 /* Sources */,
				81A27D511497483F00C75021 /* Frameworks */,
				81A27D521497483F00C75021 /* Resources */,
				FCF99CDCDFB43E785253110A /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = BIteFXproject;
			productName = BIteFXproject;
			productReference = 81A27D541497483F00C75021 /* BiteFX.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		81A27D4B1497483E00C75021 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastTestingUpgradeCheck = 0710;
				LastUpgradeCheck = 1430;
				TargetAttributes = {
					81A27D531497483F00C75021 = {
						LastSwiftMigration = 1600;
						SystemCapabilities = {
							com.apple.InAppPurchase = {
								enabled = 1;
							};
							com.apple.Push = {
								enabled = 0;
							};
							com.apple.WAC = {
								enabled = 1;
							};
							com.apple.iCloud = {
								enabled = 1;
							};
						};
					};
				};
			};
			buildConfigurationList = 81A27D4E1497483E00C75021 /* Build configuration list for PBXProject "BIteFXproject" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 81A27D491497483E00C75021;
			productRefGroup = 81A27D551497483F00C75021 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				81A27D531497483F00C75021 /* BIteFXproject */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		81A27D521497483F00C75021 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A9A25A08247AE45700F9E361 /* GoodOpenCloseOCSide.jpg in Resources */,
				A9C7D75020A086D500B54873 /* SliderSpeed.png in Resources */,
				A9C7D79220A086D500B54873 /* restore.png in Resources */,
				81A27D631497483F00C75021 /* InfoPlist.strings in Resources */,
				A9C7D74420A086D500B54873 /* NextButtonPressedImg.png in Resources */,
				A9C7D73820A086D500B54873 /* NextButtonImg.png in Resources */,
				A9C7D7A520A086D500B54873 /* Active_Pictures.png in Resources */,
				A9C7D77D20A086D500B54873 /* downloadUpdates-pressed.PNG in Resources */,
				A9A25A04247AE45700F9E361 /* BadGoodContactFront.jpg in Resources */,
				A9A25A03247AE45700F9E361 /* BadGoodAntMolarChip.jpg in Resources */,
				A9DD7F3927DF0DF700C5B864 /* BiteFX-on-iPad-3-1-Issues-and-Treatments-Callout.png in Resources */,
				A9C7D6E220A086D500B54873 /* AnimationPanelVC.xib in Resources */,
				A9DD7F4427DF0DF700C5B864 /* BiteFX-on-iPad-3-1-Staff-Training-Callout.png in Resources */,
				A9DD7F4327DF0DF700C5B864 /* Chris_Toomey_191x256.png in Resources */,
				A9537EBA26A1A1C500D48CB0 /* GoodAntRotateSideFront.mp4 in Resources */,
				A9C7D72620A086D500B54873 /* btnInfoSelected.png in Resources */,
				A9C7D79C20A086D500B54873 /* updatesNew.PNG in Resources */,
				A9C7D71B20A086D500B54873 /* RoundiPad.png in Resources */,
				A9C7D73A20A086D500B54873 /* PreviousButton.png in Resources */,
				A9C7D72C20A086D500B54873 /* BiteFXMainHelpPlayArea.png in Resources */,
				A9C7D73520A086D500B54873 /* locked.png in Resources */,
				A9A25A1F247AE45700F9E361 /* Menu-Button.png in Resources */,
				A9C7D6FD20A086D500B54873 /* skipCell.xib in Resources */,
				A95200FC2690315F0026648A /* JAMSS.mp4 in Resources */,
				A9C7D71720A086D500B54873 /* PlayIMAGE.png in Resources */,
				A9C7D78420A086D500B54873 /* menuPopup.png in Resources */,
				A9C7D76D20A086D500B54873 /* Deactivate-hover.png in Resources */,
				A9C7D7A820A086D500B54873 /* btnUnHide.png in Resources */,
				A9DD7F3B27DF0DF700C5B864 /* BiteFX-on-iPad-Presentation-Sets.PNG in Resources */,
				A9C7D78D20A086D500B54873 /* PurchasePrice-pressed.png in Resources */,
				A9537EBE26A1A1C500D48CB0 /* GoodOpenCloseOCSide.mp4 in Resources */,
				A9C7D75720A086D500B54873 /* gray.png in Resources */,
				A9DD7F2427DF0D9100C5B864 /* BiteFXiPad-Support.html in Resources */,
				A9C7D72220A086D500B54873 /* btnDeleteImage.png in Resources */,
				A9DD7F3D27DF0DF700C5B864 /* BiteFX-on-iPad-2-5-18-Picture-Panel.png in Resources */,
				A9C7D76720A086D500B54873 /* checkForUpdates-normal.PNG in Resources */,
				A9C7D75220A086D500B54873 /* SpeedIndicator.png in Resources */,
				A9C7D73920A086D500B54873 /* PlayButtonImg.png in Resources */,
				A96963552DE89D3100AE6CDD /* searchbar_base.png in Resources */,
				A9DD7F2527DF0D9100C5B864 /* BiteFXiPad-Terms-and-Conditions.html in Resources */,
				A9C7D73E20A086D500B54873 /* AnimationPressed.png in Resources */,
				A9C7D75520A086D500B54873 /* toolbar.png in Resources */,
				A9C7D79A20A086D500B54873 /* updates-bg.png in Resources */,
				A9C7D78120A086D500B54873 /* Line2.png in Resources */,
				A9C7D78820A086D500B54873 /* products.png in Resources */,
				A9DD7F2227DF0D9100C5B864 /* BiteFXiPad-Subscription.html in Resources */,
				A9DD7F4527DF0DF700C5B864 /* BiteFX-on-iPad-3-1-Hygienist-Callout.png in Resources */,
				A929E1D52DE5F49200DB8085 /* rightFlip.png in Resources */,
				A929E1D62DE5F49200DB8085 /* leftFlip.png in Resources */,
				A9C7D79920A086D500B54873 /* SpeedThumbSelected.png in Resources */,
				A9C7D73420A086D500B54873 /* LockButton.png in Resources */,
				A9C7D79620A086D500B54873 /* speedSliderNew.png in Resources */,
				A9C7D72820A086D500B54873 /* <EMAIL> in Resources */,
				A9C7D78320A086D500B54873 /* login-pressed.png in Resources */,
				A95914722E0D0D18009E23DF /* leftCornerNew.png in Resources */,
				A9C7D7A720A086D500B54873 /* btnHide.png in Resources */,
				A9C7D72720A086D500B54873 /* btnInfoSelected1.png in Resources */,
				A9DD7F3727DF0DF700C5B864 /* Animation Panel.png in Resources */,
				A9C7D7C320A086D500B54873 /* AnimationView.xib in Resources */,
				A9C7D76220A086D500B54873 /* About.png in Resources */,
				A9BABE91249C901D006933FB /* Play-Button-84x58.png in Resources */,
				A9537EBC26A1A1C500D48CB0 /* BadGoodAntMolarChip.mp4 in Resources */,
				A9C7D77C20A086D500B54873 /* downloadUpdates-normal.PNG in Resources */,
				A95200FF2690315F0026648A /* JAMSS-8KF.mp4 in Resources */,
				A9C7D76E20A086D500B54873 /* Deactivate-normal.png in Resources */,
				A9C7D71220A086D500B54873 /* Images.xcassets in Resources */,
				A9B8963E20A1B622009DC519 /* Launch Screen.storyboard in Resources */,
				A9C7D74E20A086D500B54873 /* SliderContainer.png in Resources */,
				A9BABE90249C901D006933FB /* Menu-Button_110x59.png in Resources */,
				A9C7D74520A086D500B54873 /* PlayButtonPressed.png in Resources */,
				A9C7D75D20A086D500B54873 /* Selection_panel_with_presentation_templates_tab_selected.png in Resources */,
				A9A25A24247AE45700F9E361 /* MIPvsCR2.jpg in Resources */,
				A95D0091274E5715004DA043 /* Features.MVI in Resources */,
				A9C7D77220A086D500B54873 /* Default_landscape.png in Resources */,
				A9C7D7A120A086D500B54873 /* PlayButtonNew.png in Resources */,
				A9A25A19247AE45700F9E361 /* SlideNoMuscle.html in Resources */,
				A944A4272DEDC1100078FB4A /* fav.png in Resources */,
				A944A4282DEDC1100078FB4A /* unFav.png in Resources */,
				A9A25A05247AE45700F9E361 /* BadGrindSideMusclesCU.jpg in Resources */,
				A9C7D7A920A086D500B54873 /* Default_Animation.png in Resources */,
				A9C7D73320A086D500B54873 /* Info.png in Resources */,
				A9C7D7B420A086D500B54873 /* <EMAIL> in Resources */,
				A9A25A0F247AE45700F9E361 /* BadGoodContactFront.html in Resources */,
				A9C7D74620A086D500B54873 /* PreviousPressed.png in Resources */,
				A9C7D74720A086D500B54873 /* SliderKnobPressed.png in Resources */,
				A9F7773A2E05653900FFB765 /* unFavSmall.png in Resources */,
				A9F7773B2E05653900FFB765 /* favSmall.png in Resources */,
				A9C7D76420A086D500B54873 /* badge.png in Resources */,
				A9537EBB26A1A1C500D48CB0 /* BadGoodContactFront.mp4 in Resources */,
				A9C7D75F20A086D500B54873 /* LoopButtonNew.png in Resources */,
				A9C7D72520A086D500B54873 /* <EMAIL> in Resources */,
				A9C7D71E20A086D500B54873 /* btnCopy.png in Resources */,
				A9C7D77520A086D500B54873 /* DialogDownload.png in Resources */,
				A9A25A13247AE45700F9E361 /* GoodAntRotateSideFront.html in Resources */,
				A9DD7F3C27DF0DF700C5B864 /* BiteFX-on-iPad-3-1-Dawson-Starter-Callout.png in Resources */,
				A9C7D79520A086D500B54873 /* speedKnob-Pressed.png in Resources */,
				A9A25A11247AE45700F9E361 /* BadGrindSideMusclesCU.html in Resources */,
				A9A25A20247AE45700F9E361 /* NextButton.png in Resources */,
				A9C7D75620A086D500B54873 /* toolbar1.png in Resources */,
				A9C7D72420A086D500B54873 /* btnInfo.png in Resources */,
				A95200FA2690315F0026648A /* JAMSS-32KF.mp4 in Resources */,
				A9C7D78A20A086D500B54873 /* progressbarFilled.png in Resources */,
				A9C7D71D20A086D500B54873 /* <EMAIL> in Resources */,
				A9C7D73720A086D500B54873 /* Menu.png in Resources */,
				A9A25A1E247AE45700F9E361 /* ExamplePresentationTemplate.html in Resources */,
				A9C7D81520A086D500B54873 /* BiteFXiPadBasicInventory.MVI in Resources */,
				A9C7D76C20A086D500B54873 /* close-download.png in Resources */,
				A9C7D71620A086D500B54873 /* nextFrame.png in Resources */,
				A9C7D73120A086D500B54873 /* AnimationButton.png in Resources */,
				A9C7D6FB20A086D500B54873 /* PurchaseOptionCustomCell.xib in Resources */,
				A9C7D78620A086D500B54873 /* pauseNormal.png in Resources */,
				A9C7D79320A086D500B54873 /* speed.png in Resources */,
				A9A25A21247AE45700F9E361 /* PlayButton.png in Resources */,
				022F327D28E3BAF800630C8E /* SlideNoMuscle.mp4 in Resources */,
				A95914752E0D5961009E23DF /* favMedium.png in Resources */,
				A95914762E0D5961009E23DF /* unFavMedium.png in Resources */,
				A9C7D78F20A086D500B54873 /* Register-pressed.PNG in Resources */,
				A9C7D71A20A086D500B54873 /* roundImg.png in Resources */,
				A9A25A0D247AE45700F9E361 /* BadGoodAntMolarChip.html in Resources */,
				A9C7D77320A086D500B54873 /* Default_portrait.png in Resources */,
				A9C7D7C020A086D500B54873 /* leftCorner.png in Resources */,
				022F328328E3BB6500630C8E /* BadGrindSideMusclesCU.mp4 in Resources */,
				A9C7D74A20A086D500B54873 /* resume-download.png in Resources */,
				A9DD7F4127DF0DF700C5B864 /* BiteFX-on-iPad-2-5-18-Hygienist-Presentation-Templates.png in Resources */,
				A95200FE2690315F0026648A /* JAMSS-63KF.mp4 in Resources */,
				A9BABE8F249C901D006933FB /* Next-Button-84x60.png in Resources */,
				A9C7D76F20A086D500B54873 /* Deactivate-normalNew.png in Resources */,
				A9A25A17247AE45700F9E361 /* GoodOpenCloseOCSide.html in Resources */,
				A9A25A06247AE45700F9E361 /* GoodAntRotateSideFront.jpg in Resources */,
				A9C7D76020A086D500B54873 /* LoopButtonPressed.png in Resources */,
				A9C7D74D20A086D500B54873 /* SliderArea.png in Resources */,
				A9C7D75820A086D500B54873 /* <EMAIL> in Resources */,
				A9C7D78C20A086D500B54873 /* PurchasePrice-normal.png in Resources */,
				A9C7D70020A086D500B54873 /* UpdateCell.xib in Resources */,
				A9C7D76620A086D500B54873 /* btnDownloadslt.png in Resources */,
				A9C7D70520A086D500B54873 /* Devider_image.png in Resources */,
				A9C7D76920A086D500B54873 /* checkForUpdates.png in Resources */,
				A9C7D74F20A086D500B54873 /* SliderFilled.png in Resources */,
				A9C7D71320A086D500B54873 /* 02.png in Resources */,
				A9F7C9902760DE7200BD7A30 /* FeatureSetVC.xib in Resources */,
				A9C7D75420A086D500B54873 /* SpeedSliderKnob.png in Resources */,
				A9C7D80D20A086D500B54873 /* hiddenpic.png in Resources */,
				A9DD7F4027DF0DF700C5B864 /* BiteFX-on-iPad-3-1-Detailed-Presentations-Callout.png in Resources */,
				A9C7D77A20A086D500B54873 /* downloadprogressFilled.png in Resources */,
				A9C7D7AA20A086D500B54873 /* Default_Pictures.png in Resources */,
				A9C7D75320A086D500B54873 /* SpeedSlider.png in Resources */,
				A9C7D74220A086D500B54873 /* LoopButtonPressedImg.png in Resources */,
				A9DD7F3A27DF0DF700C5B864 /* Hal-Stewart-on-Patient-Education-Detail-2.jpg in Resources */,
				A9A25A15247AE45700F9E361 /* GoodContactAllQtr4Views.html in Resources */,
				A9C7D7C520A086D500B54873 /* BiteFXMainScreenVC.xib in Resources */,
				A9A25A1D247AE45700F9E361 /* dt.css in Resources */,
				A9C7D77620A086D500B54873 /* DialogNewDownload.png in Resources */,
				A9C7D79820A086D500B54873 /* SpeedThumbNormal.png in Resources */,
				A9537EBF26A1A1C500D48CB0 /* GoodContactAllQtr4Views.mp4 in Resources */,
				A9C7D78220A086D500B54873 /* login-normal.png in Resources */,
				A9C7D77820A086D500B54873 /* download.png in Resources */,
				A9BABE8E249C901D006933FB /* Slider-Control-109x60.png in Resources */,
				A9C7D77E20A086D500B54873 /* dwnloadDialog.png in Resources */,
				02891E182928327B00ABC609 /* SpeedThumbDisabled.png in Resources */,
				A9C7D76A20A086D500B54873 /* checkForUpdatesPressed.png in Resources */,
				A9C7D78E20A086D500B54873 /* Register-normal.PNG in Resources */,
				A9C7D76320A086D500B54873 /* badge-big.png in Resources */,
				A9C7D72D20A086D500B54873 /* BiteFXMainHelpPlayArea2.png in Resources */,
				A9C7D71920A086D500B54873 /* previousFrame.png in Resources */,
				A944A42A2DEDC51C0078FB4A /* clearSearch.png in Resources */,
				A9C7D77120A086D500B54873 /* Deactivate-pressedNew.png in Resources */,
				A9C7D7A220A086D500B54873 /* Red.png in Resources */,
				A99F2B962768D8D40086E9A6 /* FeatureSetDropDownView.xib in Resources */,
				A9C7D7A620A086D500B54873 /* Active_Presentations.png in Resources */,
				A95200F92690315F0026648A /* JAMSS-16KF.mp4 in Resources */,
				A922728A2DE99911008133EA /* searchBtn.png in Resources */,
				A9C7D79720A086D500B54873 /* speedslidernewimg.png in Resources */,
				A9C7D74820A086D500B54873 /* StopButtonPressed.png in Resources */,
				A9C7D77720A086D500B54873 /* download-bg.png in Resources */,
				A9C7D75C20A086D500B54873 /* Selection_panel_with_pictures_tab_selected.png in Resources */,
				A9A25A09247AE45700F9E361 /* SlideNoMuscle.jpg in Resources */,
				A9C7D75920A086D500B54873 /* Main_screen_with_animation_displayed.png in Resources */,
				A9C7D75B20A086D500B54873 /* Selection_panel_with animations_tab_selected.png in Resources */,
				A9C7D71C20A086D500B54873 /* btnClose.png in Resources */,
				A9C7D73D20A086D500B54873 /* unlocked.png in Resources */,
				A9C7D7B320A086D500B54873 /* Yellow.png in Resources */,
				A9C7D7B920A086D500B54873 /* InfoWebView.xib in Resources */,
				A9C7D77020A086D500B54873 /* Deactivate-pressed.png in Resources */,
				A9C7D72E20A086D500B54873 /* BiteFXMainHelpPlayArea3.png in Resources */,
				A9C7D73620A086D500B54873 /* LoopButtonImg.png in Resources */,
				A9DD7F2327DF0D9100C5B864 /* BiteFXiPad-Privacy.html in Resources */,
				A9C7D79020A086D500B54873 /* register.png in Resources */,
				A9C7D74920A086D500B54873 /* unlockedPresses.png in Resources */,
				A9DD7F4227DF0DF700C5B864 /* BiteFX-on-iPad-2-5-18-Presentation-Templates.png in Resources */,
				A9C7D79D20A086D500B54873 /* X.png in Resources */,
				A9C7D71820A086D500B54873 /* playimg.png in Resources */,
				A9C7D72020A086D500B54873 /* btnDeleteAlbum.png in Resources */,
				A9C7D72920A086D500B54873 /* <EMAIL> in Resources */,
				A9C7D78520A086D500B54873 /* more.png in Resources */,
				A9A25A25247AE45700F9E361 /* WearFacets.jpg in Resources */,
				A9C7D76B20A086D500B54873 /* checkUpdates-bg.png in Resources */,
				A9DD7F2727DF0D9F00C5B864 /* dt1.css in Resources */,
				A9C7D77F20A086D500B54873 /* Line.png in Resources */,
				A9C7D79120A086D500B54873 /* register1.png in Resources */,
				A9C7D6F720A086D500B54873 /* BiteFXWelcomeAndHelp.html in Resources */,
				A9C7D7A420A086D500B54873 /* Active_Animation.png in Resources */,
				A9C7D72120A086D500B54873 /* <EMAIL> in Resources */,
				A9C7D73220A086D500B54873 /* Help.png in Resources */,
				A9C7D79B20A086D500B54873 /* updates.PNG in Resources */,
				A9DD7F3827DF0DF700C5B864 /* AnimationSelectionTab.png in Resources */,
				A9C7D72320A086D500B54873 /* <EMAIL> in Resources */,
				A9A25A22247AE45700F9E361 /* Slider-Control.png in Resources */,
				A9C7D78B20A086D500B54873 /* progressbarNormal.png in Resources */,
				A9DD7F3F27DF0DF700C5B864 /* Rick-Rogers-Detail-140x177.png in Resources */,
				A9DD7F3E27DF0DF700C5B864 /* Dr TJ Bolt 2020 Detail 148x200.png in Resources */,
				A9C7D79420A086D500B54873 /* speedKnob-Normal.png in Resources */,
				A9BABE92249C901D006933FB /* Presentation-Info-Button.png in Resources */,
				A9C7D74320A086D500B54873 /* MenuPressed.png in Resources */,
				A9C7D73020A086D500B54873 /* FrameCounter.png in Resources */,
				A9C7D77420A086D500B54873 /* details.png in Resources */,
				A9C7D76820A086D500B54873 /* checkForUpdates-pressed.PNG in Resources */,
				A9C7D71420A086D500B54873 /* 03.png in Resources */,
				A9C7D72B20A086D500B54873 /* BiteFXLogoSmall.png in Resources */,
				A9C7D76520A086D500B54873 /* btnDownloadNrml.png in Resources */,
				A9C7D81620A086D500B54873 /* BiteFXiPadFullInventory.mvi in Resources */,
				A9C7D78720A086D500B54873 /* pausePressed.png in Resources */,
				A91D28CF20AAE4E0000AFC20 /* BiteFxUpdate.MVI in Resources */,
				A9C7D75E20A086D500B54873 /* LoopButton.png in Resources */,
				A922728C2DE9992C008133EA /* closeBtn.png in Resources */,
				A9C7D7A320A086D500B54873 /* <EMAIL> in Resources */,
				A9C7D74120A086D500B54873 /* LockButtonPressed.png in Resources */,
				A9A2056A20A2C0C9007D5386 /* BITEFXDatabase.sqlite in Resources */,
				A9C7D72A20A086D500B54873 /* BiteFX_Help.png in Resources */,
				A9C7D78020A086D500B54873 /* Line1.png in Resources */,
				A9C7D76120A086D500B54873 /* LoopButtonPressedNew.png in Resources */,
				A9C7D73B20A086D500B54873 /* SliderKnob.png in Resources */,
				A9C7D75A20A086D500B54873 /* Main_screen_with_picture_displayed.png in Resources */,
				A9C7D77920A086D500B54873 /* downloadPRessed.png in Resources */,
				A9C7D75120A086D500B54873 /* SliderThumb.png in Resources */,
				A9C7D7AB20A086D500B54873 /* Default_Presentations.png in Resources */,
				A9F3FF9F24DD977A00CC61D0 /* Active Tab Presentations v4.png in Resources */,
				A9C7D78920A086D500B54873 /* products1.png in Resources */,
				A9A25A07247AE45700F9E361 /* GoodContactAllQtr4Views.jpg in Resources */,
				A9C7D72F20A086D500B54873 /* BiteFXMainHelpSelection1.png in Resources */,
				A9C7D71F20A086D500B54873 /* <EMAIL> in Resources */,
				A9C7D80320A086D500B54873 /* AddMoreImage.png in Resources */,
				A9E3E7A02E0ADA47000E4956 /* crossSelected.png in Resources */,
				A929E1CE2DE5E62400DB8085 /* PrivacyInfo.xcprivacy in Resources */,
				A9C7D73F20A086D500B54873 /* HelpPressed.png in Resources */,
				A9A25A1B247AE45700F9E361 /* Animation Panel 512x384.png in Resources */,
				A9C7D79F20A086D500B54873 /* NextButtonNew.png in Resources */,
				A9C7D81720A086D500B54873 /* BiteFXiPadFullInventoryOld.mvi in Resources */,
				A969634D2DE8995300AE6CDD /* SearchBar.xib in Resources */,
				A95200FD2690315F0026648A /* JAMSS-119KF.mp4 in Resources */,
				A9A25A23247AE45700F9E361 /* CanineGd.jpg in Resources */,
				A9C7D73C20A086D500B54873 /* StopButton.png in Resources */,
				A9C7D77B20A086D500B54873 /* downloadProgressNormal.png in Resources */,
				A9C7D74020A086D500B54873 /* InfoPressed.png in Resources */,
				A9C7D71520A086D500B54873 /* buttoniPad.png in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		1A3CCDF36F75768FDFD99D46 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-BIteFXproject-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		FCF99CDCDFB43E785253110A /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-BIteFXproject/Pods-BIteFXproject-resources.sh",
				"${PODS_CONFIGURATION_BUILD_DIR}/QBImagePickerController/QBImagePicker.bundle",
				"${PODS_ROOT}/SVProgressHUD/SVProgressHUD/SVProgressHUD.bundle",
			);
			name = "[CP] Copy Pods Resources";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/QBImagePicker.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/SVProgressHUD.bundle",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-BIteFXproject/Pods-BIteFXproject-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		81A27D501497483F00C75021 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A98E9C7C20A0970A0027541A /* DownloadFile.m in Sources */,
				A98E9C7D20A0970A0027541A /* XMLParser.m in Sources */,
				A98E9C7E20A0970A0027541A /* XMLParserUpdate.m in Sources */,
				A98E9C7220A096F40027541A /* main.m in Sources */,
				A98E9C5420A096F40027541A /* AFTableViewCell.m in Sources */,
				A98E9C5520A096F40027541A /* AnimationPanelVC.m in Sources */,
				A98E9C5620A096F40027541A /* AppDelegate.m in Sources */,
				A98E9C5820A096F40027541A /* PurchaseOptionCustomCell.m in Sources */,
				A98E9C5920A096F40027541A /* skipCell.m in Sources */,
				A98E9C5A20A096F40027541A /* UITableViewCell+NIB.m in Sources */,
				A9F7C98F2760DE7200BD7A30 /* FeatureSetVC.m in Sources */,
				A9F6A18520A1798000FE3D57 /* DownloadHelper.m in Sources */,
				A98E9C5B20A096F40027541A /* UpdateCell.m in Sources */,
				A9A1B7DA20F7585800FE39E3 /* WebService.m in Sources */,
				A9A2056920A2C0C9007D5386 /* Database.m in Sources */,
				A98E9C5C20A096F40027541A /* CustomLabel.m in Sources */,
				A98E9C6220A096F40027541A /* Download.m in Sources */,
				A98E9C6320A096F40027541A /* DownloadManager.m in Sources */,
				A98E9C6420A096F40027541A /* AlwaysOpaqueImageView.m in Sources */,
				A98E9C6520A096F40027541A /* UIImageView+ForScrollView.m in Sources */,
				A98E9C6720A096F40027541A /* ICloud.m in Sources */,
				A98E9C6820A096F40027541A /* iCloudDoc.m in Sources */,
				A98E9C6920A096F40027541A /* InAppPurchase.m in Sources */,
				A98E9C6B20A096F40027541A /* InfoWebView.m in Sources */,
				A98E9C6C20A096F40027541A /* NSObject+SBJSON.m in Sources */,
				A98E9C6D20A096F40027541A /* NSString+SBJSON.m in Sources */,
				A98E9C6E20A096F40027541A /* SBJSON.m in Sources */,
				A98E9C6F20A096F40027541A /* SBJsonBase.m in Sources */,
				A98E9C7020A096F40027541A /* SBJsonParser.m in Sources */,
				A98E9C7120A096F40027541A /* SBJsonWriter.m in Sources */,
				A98E9C7320A096F40027541A /* AnimationView.m in Sources */,
				A98E9C7420A096F40027541A /* BiteFXMainScreenVC.m in Sources */,
				A99F2B932768D8B80086E9A6 /* FeatureSetDropDownView.m in Sources */,
				A94606512DEF1A0A001B7F91 /* SearchBar.m in Sources */,
				A98E9C7520A096F40027541A /* NetworkReachability.m in Sources */,
				A98E9C7620A096F40027541A /* PlayerView.m in Sources */,
				A98E9C7720A096F40027541A /* Reachability.m in Sources */,
				A98E9C7820A096F40027541A /* NSString+MD5Addition.m in Sources */,
				023EC4A42A3263C200F165A1 /* ErrorFormatter.m in Sources */,
				A98E9C7920A096F40027541A /* UIDevice+IdentifierAddition.m in Sources */,
				A9D7F4A3242B285F007EBAE2 /* IAPManager.m in Sources */,
				A98BA957274D2770008B4230 /* XMLParserFeature.m in Sources */,
				A9A2057020A2ED2C007D5386 /* UIAlertController+QMAlertControl.m in Sources */,
				A970030320A1720D009E14BE /* CallWebService.m in Sources */,
				A98E9C7A20A096F40027541A /* Utility.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		81A27D611497483F00C75021 /* InfoPlist.strings */ = {
			isa = PBXVariantGroup;
			children = (
				81A27D621497483F00C75021 /* en */,
			);
			name = InfoPlist.strings;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		81A27D851497483F00C75021 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_ENTITLEMENTS = "";
				CODE_SIGN_IDENTITY = "Apple Distribution: D2EFFECTS LLC (U7LVMUJSUB)";
				COPY_PHASE_STRIP = NO;
				DEVELOPMENT_TEAM = U7LVMUJSUB;
				ENABLE_BITCODE = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_VERSION = com.apple.compilers.llvm.clang.1_0;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_MISSING_PROTOTYPES = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				LD_MAP_FILE_PATH = "$(TARGET_TEMP_DIR)/$(PRODUCT_NAME)-LinkMap-$(CURRENT_VARIANT)-$(CURRENT_ARCH).txt";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				ONLY_ACTIVE_ARCH = YES;
				PROVISIONING_PROFILE = "912d56f2-c5b2-4b69-ae64-2f91d1d8f747";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = 2;
				USER_HEADER_SEARCH_PATHS = "";
			};
			name = Debug;
		};
		81A27D861497483F00C75021 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_ENTITLEMENTS = "";
				CODE_SIGN_IDENTITY = "Apple Distribution: D2EFFECTS LLC (U7LVMUJSUB)";
				COPY_PHASE_STRIP = YES;
				DEVELOPMENT_TEAM = U7LVMUJSUB;
				ENABLE_BITCODE = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = NO;
				GCC_VERSION = com.apple.compilers.llvm.clang.1_0;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_MISSING_PROTOTYPES = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				LD_MAP_FILE_PATH = "$(TARGET_TEMP_DIR)/$(PRODUCT_NAME)-LinkMap-$(CURRENT_VARIANT)-$(CURRENT_ARCH).txt";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CFLAGS = "-DNS_BLOCK_ASSERTIONS=1";
				PROVISIONING_PROFILE = "912d56f2-c5b2-4b69-ae64-2f91d1d8f747";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = 2;
				USER_HEADER_SEARCH_PATHS = "";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		81A27D881497483F00C75021 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C7729F0D0AD1A3D0CD520BF2 /* Pods-BIteFXproject.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = LaunchImage;
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CODE_SIGN_ENTITLEMENTS = BIteFXproject/BIteFXproject.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development: Dipesh Khandhar (6WH3CR7L2Q)";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 3.1.35;
				FRAMEWORK_SEARCH_PATHS = "$(inherited)";
				GCC_NO_COMMON_BLOCKS = NO;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "BIteFXproject/BIteFXproject-Prefix.pch";
				GCC_VERSION = com.apple.compilers.llvm.clang.1_0;
				INFOPLIST_FILE = "BIteFXproject/BIteFXproject-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = BiteFX;
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = "$(inherited)";
				MARKETING_VERSION = 3.1.35;
				PRODUCT_BUNDLE_IDENTIFIER = com.bitefx.bitefx;
				PRODUCT_NAME = BiteFX;
				PROVISIONING_PROFILE = "";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "BiteFX Development";
				SDKROOT = iphoneos;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 6.0;
				TARGETED_DEVICE_FAMILY = 2;
				USER_HEADER_SEARCH_PATHS = "";
				WRAPPER_EXTENSION = app;
			};
			name = Debug;
		};
		81A27D891497483F00C75021 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1A26A5FD725A603889CBA235 /* Pods-BIteFXproject.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = LaunchImage;
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CODE_SIGN_ENTITLEMENTS = BIteFXproject/BIteFXproject.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development: Dipesh Khandhar (6WH3CR7L2Q)";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 3.1.35;
				FRAMEWORK_SEARCH_PATHS = "$(inherited)";
				GCC_NO_COMMON_BLOCKS = NO;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "BIteFXproject/BIteFXproject-Prefix.pch";
				GCC_VERSION = com.apple.compilers.llvm.clang.1_0;
				INFOPLIST_FILE = "BIteFXproject/BIteFXproject-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = BiteFX;
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = "$(inherited)";
				MARKETING_VERSION = 3.1.35;
				PRODUCT_BUNDLE_IDENTIFIER = com.bitefx.bitefx;
				PRODUCT_NAME = BiteFX;
				PROVISIONING_PROFILE = "";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "BiteFX Development";
				SDKROOT = iphoneos;
				SWIFT_VERSION = 6.0;
				TARGETED_DEVICE_FAMILY = 2;
				USER_HEADER_SEARCH_PATHS = "";
				WRAPPER_EXTENSION = app;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		81A27D4E1497483E00C75021 /* Build configuration list for PBXProject "BIteFXproject" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				81A27D851497483F00C75021 /* Debug */,
				81A27D861497483F00C75021 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		81A27D871497483F00C75021 /* Build configuration list for PBXNativeTarget "BIteFXproject" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				81A27D881497483F00C75021 /* Debug */,
				81A27D891497483F00C75021 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 81A27D4B1497483E00C75021 /* Project object */;
}
