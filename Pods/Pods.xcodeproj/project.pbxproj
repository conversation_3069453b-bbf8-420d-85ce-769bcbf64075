// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		02B77E123A1E87AB29A91300E929E03C /* SVIndefiniteAnimatedView.h in Headers */ = {isa = PBXBuildFile; fileRef = 8EAB41BB2F44F6CF3303BE7B2B436564 /* SVIndefiniteAnimatedView.h */; settings = {ATTRIBUTES = (Project, ); }; };
		062F026E5D3028788A6AFE24368D26F4 /* QBAssetCell.m in Sources */ = {isa = PBXBuildFile; fileRef = F521C5277E75D2C7C7DDA3F47D7888DC /* QBAssetCell.m */; };
		0867826199CEA5D4377F9CC8FE133329 /* QBAlbumsViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 8AFA7ECF136EDA6F3CEDE86818017959 /* QBAlbumsViewController.m */; };
		0947112334F897C8C36FD9ECD98E5382 /* QBSlomoIconView.h in Headers */ = {isa = PBXBuildFile; fileRef = EB404645CCFDE8C8E3DBD2D562CC8D14 /* QBSlomoIconView.h */; settings = {ATTRIBUTES = (Project, ); }; };
		199D7CFC080E06DA4F0E9BE1EB22B984 /* Pods-BIteFXproject-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 755F0A1214C26FD968938C4AC8353F19 /* Pods-BIteFXproject-dummy.m */; };
		1F4809FBC153EF68443926782765D766 /* QBVideoIndicatorView.m in Sources */ = {isa = PBXBuildFile; fileRef = 2D2EDBA3F76E38344C64137BFA148F98 /* QBVideoIndicatorView.m */; };
		2592B56DF5B3A48B37885CD0054B6F87 /* QBVideoIndicatorView.h in Headers */ = {isa = PBXBuildFile; fileRef = 21F7240F61C0A8FDEC845637BA93B5BE /* QBVideoIndicatorView.h */; settings = {ATTRIBUTES = (Project, ); }; };
		276077A5354C567FC7889EDDF4AB0B5F /* QBImagePickerController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0958474769055234D8715779097107AD /* QBImagePickerController.m */; };
		28EDFE419A18E6309661E37874B17388 /* QBVideoIconView.h in Headers */ = {isa = PBXBuildFile; fileRef = AF00E2AE1DC1215DE89929C377D53979 /* QBVideoIconView.h */; settings = {ATTRIBUTES = (Project, ); }; };
		2FC26BCDE0CC09576EA017B662B0F120 /* QBAlbumsViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 042AAF887B03D9C2AF55B64E40D69895 /* QBAlbumsViewController.h */; settings = {ATTRIBUTES = (Project, ); }; };
		307E6DD73554CE9DF64AD861DFD1A2C7 /* QBVideoIconView.m in Sources */ = {isa = PBXBuildFile; fileRef = 3B7A69B92A8F75E2C9AC867586F1152E /* QBVideoIconView.m */; };
		3ACD20B11A890C63F19DCA4798561FE4 /* SVProgressHUD.m in Sources */ = {isa = PBXBuildFile; fileRef = 14D0E6FDC3C31F0DB98745AEC2B3E9D0 /* SVProgressHUD.m */; };
		4EA904AF2FA7311AD294BB8D201C17E0 /* QBImagePickerController-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 7EDE8D276799808BF9AB229302D23C22 /* QBImagePickerController-dummy.m */; };
		587BCAD817EE38ACCC546D6CA44012C7 /* QBAlbumCell.h in Headers */ = {isa = PBXBuildFile; fileRef = 6C44EDBB33595D6867F6C512C6630A4D /* QBAlbumCell.h */; settings = {ATTRIBUTES = (Project, ); }; };
		5AECC1EE09F1EB890A9370597214CDB3 /* QBSlomoIconView.m in Sources */ = {isa = PBXBuildFile; fileRef = 1B48D8725EA2BD29FABE265DC4ECE0D8 /* QBSlomoIconView.m */; };
		6A8816A9BD17085FB1B6E96584B591DB /* SVRadialGradientLayer.m in Sources */ = {isa = PBXBuildFile; fileRef = 31997A694716A3F1B4A5FD197BE7DE07 /* SVRadialGradientLayer.m */; };
		8AA96D93F9D4D0B723ED966E27EF49B2 /* QBAssetsViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 5FD8E052603BEFDD19D8113CABD72A83 /* QBAssetsViewController.h */; settings = {ATTRIBUTES = (Project, ); }; };
		8F4BA3AF55B76A80D901E0E9CFB4622A /* QBImagePickerController.h in Headers */ = {isa = PBXBuildFile; fileRef = 0C93063EE2C3A58C68685112E4A12FC4 /* QBImagePickerController.h */; settings = {ATTRIBUTES = (Project, ); }; };
		A518053ADD09786ABB1AE74C9593A994 /* QBCheckmarkView.m in Sources */ = {isa = PBXBuildFile; fileRef = 3EB78ABC331BBF71F9833156813E114F /* QBCheckmarkView.m */; };
		A929AFBBFBC5FE8DA06BF4C63FC2BD22 /* SVProgressAnimatedView.h in Headers */ = {isa = PBXBuildFile; fileRef = 4005501E205D4052459946DD12B40C41 /* SVProgressAnimatedView.h */; settings = {ATTRIBUTES = (Project, ); }; };
		B04462809F79D82FDE8FC5ED2AD9D305 /* ja.lproj in Resources */ = {isa = PBXBuildFile; fileRef = 9F1081AAFF15E697E36D5E438076047E /* ja.lproj */; };
		B29B7406781C991F7C9A91247CAC8EE6 /* de.lproj in Resources */ = {isa = PBXBuildFile; fileRef = 8FBCCB510EF8C5EBFC2412D41B5B2B73 /* de.lproj */; };
		B6788349594F49503001BDEF1A2C6868 /* SVRadialGradientLayer.h in Headers */ = {isa = PBXBuildFile; fileRef = 53EFF7D00CEC2C5CF87DC951B0788859 /* SVRadialGradientLayer.h */; settings = {ATTRIBUTES = (Project, ); }; };
		B905F6AC70EC566E033F8D17ACB125A2 /* es.lproj in Resources */ = {isa = PBXBuildFile; fileRef = 9499BE7B991D2B88602D5F25F3D22B3C /* es.lproj */; };
		C58FCD7ECB0C7D14E5E5FAB76E22EEA3 /* SVProgressAnimatedView.m in Sources */ = {isa = PBXBuildFile; fileRef = 61FA1D078FF0F0788658CD6B61411642 /* SVProgressAnimatedView.m */; };
		CD5D8DD9C28DF50A87C8CF559E20F630 /* zh-Hans.lproj in Resources */ = {isa = PBXBuildFile; fileRef = 7833AE790556EED2C9FDFBC734490D23 /* zh-Hans.lproj */; };
		D2C0D1462D9DC700A1CE710370C1D5A7 /* QBCheckmarkView.h in Headers */ = {isa = PBXBuildFile; fileRef = DD8080CBE40A592C0AE1E412B35DC07A /* QBCheckmarkView.h */; settings = {ATTRIBUTES = (Project, ); }; };
		D3D158BD760C961CD6B9AF74F10CBC7A /* QBAlbumCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 0AC835FAFA8DC1F214466CF0B7B46E23 /* QBAlbumCell.m */; };
		D93401D8D642EFE12E55C2A8712A0C59 /* QBImagePicker.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = C7329D1370BDA1A1EC9F8C35E68E7073 /* QBImagePicker.storyboard */; };
		DC345F2C44137670426AE94D25A22AE4 /* QBAssetCell.h in Headers */ = {isa = PBXBuildFile; fileRef = 5D9D1A31E28E4F2E2A949DF2C9FF4331 /* QBAssetCell.h */; settings = {ATTRIBUTES = (Project, ); }; };
		E3845D5004DD62EA80E7E8E6A50A2EEA /* QBAssetsViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = CB655040716AFE1F5CE063BF059A79B0 /* QBAssetsViewController.m */; };
		E506133289A05B48855763E3B030C9F7 /* en.lproj in Resources */ = {isa = PBXBuildFile; fileRef = 2B814EB07C06F0C849C99A9BEABF065E /* en.lproj */; };
		E7A44EBAC62F50F2AF3EBC40EF5DE945 /* SVProgressHUD-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 32983E1ED538D00824514FC78557D512 /* SVProgressHUD-dummy.m */; };
		F36626F21159F178A5B8644784709F8F /* SVIndefiniteAnimatedView.m in Sources */ = {isa = PBXBuildFile; fileRef = D4FC3E2C6E0A18247C37F40E723B7FBC /* SVIndefiniteAnimatedView.m */; };
		F5C20D3DBE10D0CBDBDE571A4E4D8D11 /* SVProgressHUD.h in Headers */ = {isa = PBXBuildFile; fileRef = 0B9134612685E94AEA0C10253EDB5ACE /* SVProgressHUD.h */; settings = {ATTRIBUTES = (Project, ); }; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		04DC57709935EB151916B5B9A5F2D073 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = EAEBAE5C8257AED94B3BED47A0E0E3FA;
			remoteInfo = "QBImagePickerController-QBImagePicker";
		};
		1C11C7CC53D997F4A90EEA766B4AF6E0 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1C8D67D8B72D6BA42CCEDB648537A340;
			remoteInfo = SVProgressHUD;
		};
		C9222A8444F095D43434F40286C9A370 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = C49345AFA49B098B0384C4FA864A4868;
			remoteInfo = QBImagePickerController;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		042AAF887B03D9C2AF55B64E40D69895 /* QBAlbumsViewController.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = QBAlbumsViewController.h; path = QBImagePicker/QBAlbumsViewController.h; sourceTree = "<group>"; };
		0958474769055234D8715779097107AD /* QBImagePickerController.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = QBImagePickerController.m; path = QBImagePicker/QBImagePickerController.m; sourceTree = "<group>"; };
		0AC835FAFA8DC1F214466CF0B7B46E23 /* QBAlbumCell.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = QBAlbumCell.m; path = QBImagePicker/QBAlbumCell.m; sourceTree = "<group>"; };
		0B9134612685E94AEA0C10253EDB5ACE /* SVProgressHUD.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SVProgressHUD.h; path = SVProgressHUD/SVProgressHUD.h; sourceTree = "<group>"; };
		0C93063EE2C3A58C68685112E4A12FC4 /* QBImagePickerController.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = QBImagePickerController.h; path = QBImagePicker/QBImagePickerController.h; sourceTree = "<group>"; };
		0FC011CA91E84A6C5E0B14E7567CFF3F /* Pods-BIteFXproject.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-BIteFXproject.debug.xcconfig"; sourceTree = "<group>"; };
		14D0E6FDC3C31F0DB98745AEC2B3E9D0 /* SVProgressHUD.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SVProgressHUD.m; path = SVProgressHUD/SVProgressHUD.m; sourceTree = "<group>"; };
		1B48D8725EA2BD29FABE265DC4ECE0D8 /* QBSlomoIconView.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = QBSlomoIconView.m; path = QBImagePicker/QBSlomoIconView.m; sourceTree = "<group>"; };
		20D73B80DBEDC9935666EB05B502A6E8 /* QBImagePickerController */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; name = QBImagePickerController; path = libQBImagePickerController.a; sourceTree = BUILT_PRODUCTS_DIR; };
		21F7240F61C0A8FDEC845637BA93B5BE /* QBVideoIndicatorView.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = QBVideoIndicatorView.h; path = QBImagePicker/QBVideoIndicatorView.h; sourceTree = "<group>"; };
		2B814EB07C06F0C849C99A9BEABF065E /* en.lproj */ = {isa = PBXFileReference; includeInIndex = 1; name = en.lproj; path = QBImagePicker/en.lproj; sourceTree = "<group>"; };
		2D2EDBA3F76E38344C64137BFA148F98 /* QBVideoIndicatorView.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = QBVideoIndicatorView.m; path = QBImagePicker/QBVideoIndicatorView.m; sourceTree = "<group>"; };
		2DDDE42B0C44F136F82F8401F2EBD33A /* Pods-BIteFXproject-acknowledgements.markdown */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = "Pods-BIteFXproject-acknowledgements.markdown"; sourceTree = "<group>"; };
		2F21B3DE984AE3E9A3187936ABCE1C61 /* SVProgressHUD.bundle */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = "wrapper.plug-in"; name = SVProgressHUD.bundle; path = SVProgressHUD/SVProgressHUD.bundle; sourceTree = "<group>"; };
		31997A694716A3F1B4A5FD197BE7DE07 /* SVRadialGradientLayer.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SVRadialGradientLayer.m; path = SVProgressHUD/SVRadialGradientLayer.m; sourceTree = "<group>"; };
		32983E1ED538D00824514FC78557D512 /* SVProgressHUD-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "SVProgressHUD-dummy.m"; sourceTree = "<group>"; };
		3B7A69B92A8F75E2C9AC867586F1152E /* QBVideoIconView.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = QBVideoIconView.m; path = QBImagePicker/QBVideoIconView.m; sourceTree = "<group>"; };
		3EB78ABC331BBF71F9833156813E114F /* QBCheckmarkView.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = QBCheckmarkView.m; path = QBImagePicker/QBCheckmarkView.m; sourceTree = "<group>"; };
		4005501E205D4052459946DD12B40C41 /* SVProgressAnimatedView.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SVProgressAnimatedView.h; path = SVProgressHUD/SVProgressAnimatedView.h; sourceTree = "<group>"; };
		53EFF7D00CEC2C5CF87DC951B0788859 /* SVRadialGradientLayer.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SVRadialGradientLayer.h; path = SVProgressHUD/SVRadialGradientLayer.h; sourceTree = "<group>"; };
		5D9D1A31E28E4F2E2A949DF2C9FF4331 /* QBAssetCell.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = QBAssetCell.h; path = QBImagePicker/QBAssetCell.h; sourceTree = "<group>"; };
		5FD8E052603BEFDD19D8113CABD72A83 /* QBAssetsViewController.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = QBAssetsViewController.h; path = QBImagePicker/QBAssetsViewController.h; sourceTree = "<group>"; };
		61FA1D078FF0F0788658CD6B61411642 /* SVProgressAnimatedView.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SVProgressAnimatedView.m; path = SVProgressHUD/SVProgressAnimatedView.m; sourceTree = "<group>"; };
		67EB15C3C5081081246D761C07C43BC2 /* SVProgressHUD.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = SVProgressHUD.release.xcconfig; sourceTree = "<group>"; };
		6C44EDBB33595D6867F6C512C6630A4D /* QBAlbumCell.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = QBAlbumCell.h; path = QBImagePicker/QBAlbumCell.h; sourceTree = "<group>"; };
		755F0A1214C26FD968938C4AC8353F19 /* Pods-BIteFXproject-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Pods-BIteFXproject-dummy.m"; sourceTree = "<group>"; };
		7833AE790556EED2C9FDFBC734490D23 /* zh-Hans.lproj */ = {isa = PBXFileReference; includeInIndex = 1; name = "zh-Hans.lproj"; path = "QBImagePicker/zh-Hans.lproj"; sourceTree = "<group>"; };
		7EDE8D276799808BF9AB229302D23C22 /* QBImagePickerController-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "QBImagePickerController-dummy.m"; sourceTree = "<group>"; };
		832ADC18B437D842689FB367E14B09FB /* Pods-BIteFXproject-acknowledgements.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-BIteFXproject-acknowledgements.plist"; sourceTree = "<group>"; };
		8AFA7ECF136EDA6F3CEDE86818017959 /* QBAlbumsViewController.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = QBAlbumsViewController.m; path = QBImagePicker/QBAlbumsViewController.m; sourceTree = "<group>"; };
		8EAB41BB2F44F6CF3303BE7B2B436564 /* SVIndefiniteAnimatedView.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SVIndefiniteAnimatedView.h; path = SVProgressHUD/SVIndefiniteAnimatedView.h; sourceTree = "<group>"; };
		8FBCCB510EF8C5EBFC2412D41B5B2B73 /* de.lproj */ = {isa = PBXFileReference; includeInIndex = 1; name = de.lproj; path = QBImagePicker/de.lproj; sourceTree = "<group>"; };
		9499BE7B991D2B88602D5F25F3D22B3C /* es.lproj */ = {isa = PBXFileReference; includeInIndex = 1; name = es.lproj; path = QBImagePicker/es.lproj; sourceTree = "<group>"; };
		9D940727FF8FB9C785EB98E56350EF41 /* Podfile */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = Podfile; path = ../Podfile; sourceTree = SOURCE_ROOT; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		9F1081AAFF15E697E36D5E438076047E /* ja.lproj */ = {isa = PBXFileReference; includeInIndex = 1; name = ja.lproj; path = QBImagePicker/ja.lproj; sourceTree = "<group>"; };
		A41E0A6F67E1EEEEF0B1F5824CD40038 /* QBImagePickerController.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = QBImagePickerController.release.xcconfig; sourceTree = "<group>"; };
		A4CD89A8B4AB8A4598CCE4390871641C /* ResourceBundle-QBImagePicker-QBImagePickerController-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-QBImagePicker-QBImagePickerController-Info.plist"; sourceTree = "<group>"; };
		AF00E2AE1DC1215DE89929C377D53979 /* QBVideoIconView.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = QBVideoIconView.h; path = QBImagePicker/QBVideoIconView.h; sourceTree = "<group>"; };
		C1CE542DFDDD265921031F08C32BB8DC /* QBImagePickerController-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "QBImagePickerController-prefix.pch"; sourceTree = "<group>"; };
		C5B5427F9BC01DEF4533C22B72AB7717 /* Pods-BIteFXproject */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; name = "Pods-BIteFXproject"; path = "libPods-BIteFXproject.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		C7329D1370BDA1A1EC9F8C35E68E7073 /* QBImagePicker.storyboard */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = file.storyboard; name = QBImagePicker.storyboard; path = QBImagePicker/QBImagePicker.storyboard; sourceTree = "<group>"; };
		C9708630F9D9C8C32EF0B3C91B9E469D /* QBImagePickerController-QBImagePicker */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "QBImagePickerController-QBImagePicker"; path = QBImagePicker.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		CB655040716AFE1F5CE063BF059A79B0 /* QBAssetsViewController.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = QBAssetsViewController.m; path = QBImagePicker/QBAssetsViewController.m; sourceTree = "<group>"; };
		D14BA42C7E5990DB6746426C4B8E472E /* QBImagePickerController.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = QBImagePickerController.debug.xcconfig; sourceTree = "<group>"; };
		D1610F85135B491E19152E45AD54C344 /* Pods-BIteFXproject-resources.sh */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.script.sh; path = "Pods-BIteFXproject-resources.sh"; sourceTree = "<group>"; };
		D4FC3E2C6E0A18247C37F40E723B7FBC /* SVIndefiniteAnimatedView.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SVIndefiniteAnimatedView.m; path = SVProgressHUD/SVIndefiniteAnimatedView.m; sourceTree = "<group>"; };
		D6450B483B3BCD268297137ED7E4BB41 /* Pods-BIteFXproject.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-BIteFXproject.release.xcconfig"; sourceTree = "<group>"; };
		DD8080CBE40A592C0AE1E412B35DC07A /* QBCheckmarkView.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = QBCheckmarkView.h; path = QBImagePicker/QBCheckmarkView.h; sourceTree = "<group>"; };
		E97D43C46A45EE515A4DA3AF94398441 /* SVProgressHUD */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; name = SVProgressHUD; path = libSVProgressHUD.a; sourceTree = BUILT_PRODUCTS_DIR; };
		EB404645CCFDE8C8E3DBD2D562CC8D14 /* QBSlomoIconView.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = QBSlomoIconView.h; path = QBImagePicker/QBSlomoIconView.h; sourceTree = "<group>"; };
		F0B8BBD48E8A540835269D5D7F8E3B7B /* SVProgressHUD.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = SVProgressHUD.debug.xcconfig; sourceTree = "<group>"; };
		F521C5277E75D2C7C7DDA3F47D7888DC /* QBAssetCell.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = QBAssetCell.m; path = QBImagePicker/QBAssetCell.m; sourceTree = "<group>"; };
		FD3FE778B21FB3C405AD2EB38C6C4F69 /* SVProgressHUD-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "SVProgressHUD-prefix.pch"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		0AF6A68BFD3CF40F3D98449DA6A48A2E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7240C761805D8240B70256F25EBFB5EA /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A791A81BF300936ECA0F57F67A6C4207 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A9F8C9DCD68B40EEFAB23E754B037AF9 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		20B497509BDF8A00E84BCCD81801AA80 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				7EDE8D276799808BF9AB229302D23C22 /* QBImagePickerController-dummy.m */,
				C1CE542DFDDD265921031F08C32BB8DC /* QBImagePickerController-prefix.pch */,
				D14BA42C7E5990DB6746426C4B8E472E /* QBImagePickerController.debug.xcconfig */,
				A41E0A6F67E1EEEEF0B1F5824CD40038 /* QBImagePickerController.release.xcconfig */,
				A4CD89A8B4AB8A4598CCE4390871641C /* ResourceBundle-QBImagePicker-QBImagePickerController-Info.plist */,
			);
			name = "Support Files";
			path = "../Target Support Files/QBImagePickerController";
			sourceTree = "<group>";
		};
		34B003FA83733A017400D14B8D5D1D0F /* Pods-BIteFXproject */ = {
			isa = PBXGroup;
			children = (
				2DDDE42B0C44F136F82F8401F2EBD33A /* Pods-BIteFXproject-acknowledgements.markdown */,
				832ADC18B437D842689FB367E14B09FB /* Pods-BIteFXproject-acknowledgements.plist */,
				755F0A1214C26FD968938C4AC8353F19 /* Pods-BIteFXproject-dummy.m */,
				D1610F85135B491E19152E45AD54C344 /* Pods-BIteFXproject-resources.sh */,
				0FC011CA91E84A6C5E0B14E7567CFF3F /* Pods-BIteFXproject.debug.xcconfig */,
				D6450B483B3BCD268297137ED7E4BB41 /* Pods-BIteFXproject.release.xcconfig */,
			);
			name = "Pods-BIteFXproject";
			path = "Target Support Files/Pods-BIteFXproject";
			sourceTree = "<group>";
		};
		62E6416BA668D1041379D47396257C60 /* Targets Support Files */ = {
			isa = PBXGroup;
			children = (
				34B003FA83733A017400D14B8D5D1D0F /* Pods-BIteFXproject */,
			);
			name = "Targets Support Files";
			sourceTree = "<group>";
		};
		742378D8DB196B77FB7DC99B018F76F5 /* SVProgressHUD */ = {
			isa = PBXGroup;
			children = (
				8EAB41BB2F44F6CF3303BE7B2B436564 /* SVIndefiniteAnimatedView.h */,
				D4FC3E2C6E0A18247C37F40E723B7FBC /* SVIndefiniteAnimatedView.m */,
				4005501E205D4052459946DD12B40C41 /* SVProgressAnimatedView.h */,
				61FA1D078FF0F0788658CD6B61411642 /* SVProgressAnimatedView.m */,
				0B9134612685E94AEA0C10253EDB5ACE /* SVProgressHUD.h */,
				14D0E6FDC3C31F0DB98745AEC2B3E9D0 /* SVProgressHUD.m */,
				53EFF7D00CEC2C5CF87DC951B0788859 /* SVRadialGradientLayer.h */,
				31997A694716A3F1B4A5FD197BE7DE07 /* SVRadialGradientLayer.m */,
				97AEA612306410D1A42723F2483E89BF /* Resources */,
				8FD12F7F583BB0CF9E57122B05C0220D /* Support Files */,
			);
			name = SVProgressHUD;
			path = SVProgressHUD;
			sourceTree = "<group>";
		};
		7603EE409249F9C214D0888051021CEE /* QBImagePickerController */ = {
			isa = PBXGroup;
			children = (
				6C44EDBB33595D6867F6C512C6630A4D /* QBAlbumCell.h */,
				0AC835FAFA8DC1F214466CF0B7B46E23 /* QBAlbumCell.m */,
				042AAF887B03D9C2AF55B64E40D69895 /* QBAlbumsViewController.h */,
				8AFA7ECF136EDA6F3CEDE86818017959 /* QBAlbumsViewController.m */,
				5D9D1A31E28E4F2E2A949DF2C9FF4331 /* QBAssetCell.h */,
				F521C5277E75D2C7C7DDA3F47D7888DC /* QBAssetCell.m */,
				5FD8E052603BEFDD19D8113CABD72A83 /* QBAssetsViewController.h */,
				CB655040716AFE1F5CE063BF059A79B0 /* QBAssetsViewController.m */,
				DD8080CBE40A592C0AE1E412B35DC07A /* QBCheckmarkView.h */,
				3EB78ABC331BBF71F9833156813E114F /* QBCheckmarkView.m */,
				0C93063EE2C3A58C68685112E4A12FC4 /* QBImagePickerController.h */,
				0958474769055234D8715779097107AD /* QBImagePickerController.m */,
				EB404645CCFDE8C8E3DBD2D562CC8D14 /* QBSlomoIconView.h */,
				1B48D8725EA2BD29FABE265DC4ECE0D8 /* QBSlomoIconView.m */,
				AF00E2AE1DC1215DE89929C377D53979 /* QBVideoIconView.h */,
				3B7A69B92A8F75E2C9AC867586F1152E /* QBVideoIconView.m */,
				21F7240F61C0A8FDEC845637BA93B5BE /* QBVideoIndicatorView.h */,
				2D2EDBA3F76E38344C64137BFA148F98 /* QBVideoIndicatorView.m */,
				AE039B9074A555D1F418C99057537A5F /* Resources */,
				20B497509BDF8A00E84BCCD81801AA80 /* Support Files */,
			);
			name = QBImagePickerController;
			path = QBImagePickerController;
			sourceTree = "<group>";
		};
		8FD12F7F583BB0CF9E57122B05C0220D /* Support Files */ = {
			isa = PBXGroup;
			children = (
				32983E1ED538D00824514FC78557D512 /* SVProgressHUD-dummy.m */,
				FD3FE778B21FB3C405AD2EB38C6C4F69 /* SVProgressHUD-prefix.pch */,
				F0B8BBD48E8A540835269D5D7F8E3B7B /* SVProgressHUD.debug.xcconfig */,
				67EB15C3C5081081246D761C07C43BC2 /* SVProgressHUD.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Target Support Files/SVProgressHUD";
			sourceTree = "<group>";
		};
		97AEA612306410D1A42723F2483E89BF /* Resources */ = {
			isa = PBXGroup;
			children = (
				2F21B3DE984AE3E9A3187936ABCE1C61 /* SVProgressHUD.bundle */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		AE039B9074A555D1F418C99057537A5F /* Resources */ = {
			isa = PBXGroup;
			children = (
				8FBCCB510EF8C5EBFC2412D41B5B2B73 /* de.lproj */,
				2B814EB07C06F0C849C99A9BEABF065E /* en.lproj */,
				9499BE7B991D2B88602D5F25F3D22B3C /* es.lproj */,
				9F1081AAFF15E697E36D5E438076047E /* ja.lproj */,
				C7329D1370BDA1A1EC9F8C35E68E7073 /* QBImagePicker.storyboard */,
				7833AE790556EED2C9FDFBC734490D23 /* zh-Hans.lproj */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		B154F35854013B071FC2E5837CD72F2B /* Pods */ = {
			isa = PBXGroup;
			children = (
				7603EE409249F9C214D0888051021CEE /* QBImagePickerController */,
				742378D8DB196B77FB7DC99B018F76F5 /* SVProgressHUD */,
			);
			name = Pods;
			sourceTree = "<group>";
		};
		CF1408CF629C7361332E53B88F7BD30C = {
			isa = PBXGroup;
			children = (
				9D940727FF8FB9C785EB98E56350EF41 /* Podfile */,
				D89477F20FB1DE18A04690586D7808C4 /* Frameworks */,
				B154F35854013B071FC2E5837CD72F2B /* Pods */,
				F5544A9346E8ABE1D6E29E42CCBEE35D /* Products */,
				62E6416BA668D1041379D47396257C60 /* Targets Support Files */,
			);
			sourceTree = "<group>";
		};
		D89477F20FB1DE18A04690586D7808C4 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		F5544A9346E8ABE1D6E29E42CCBEE35D /* Products */ = {
			isa = PBXGroup;
			children = (
				C5B5427F9BC01DEF4533C22B72AB7717 /* Pods-BIteFXproject */,
				20D73B80DBEDC9935666EB05B502A6E8 /* QBImagePickerController */,
				C9708630F9D9C8C32EF0B3C91B9E469D /* QBImagePickerController-QBImagePicker */,
				E97D43C46A45EE515A4DA3AF94398441 /* SVProgressHUD */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		05670BE1A472A543C12A9E49750527C8 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				587BCAD817EE38ACCC546D6CA44012C7 /* QBAlbumCell.h in Headers */,
				2FC26BCDE0CC09576EA017B662B0F120 /* QBAlbumsViewController.h in Headers */,
				DC345F2C44137670426AE94D25A22AE4 /* QBAssetCell.h in Headers */,
				8AA96D93F9D4D0B723ED966E27EF49B2 /* QBAssetsViewController.h in Headers */,
				D2C0D1462D9DC700A1CE710370C1D5A7 /* QBCheckmarkView.h in Headers */,
				8F4BA3AF55B76A80D901E0E9CFB4622A /* QBImagePickerController.h in Headers */,
				0947112334F897C8C36FD9ECD98E5382 /* QBSlomoIconView.h in Headers */,
				28EDFE419A18E6309661E37874B17388 /* QBVideoIconView.h in Headers */,
				2592B56DF5B3A48B37885CD0054B6F87 /* QBVideoIndicatorView.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2DE33C800B95BD3BB6BD330762561E77 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3BC9638C7DEA971224B2890ED4506A7F /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				02B77E123A1E87AB29A91300E929E03C /* SVIndefiniteAnimatedView.h in Headers */,
				A929AFBBFBC5FE8DA06BF4C63FC2BD22 /* SVProgressAnimatedView.h in Headers */,
				F5C20D3DBE10D0CBDBDE571A4E4D8D11 /* SVProgressHUD.h in Headers */,
				B6788349594F49503001BDEF1A2C6868 /* SVRadialGradientLayer.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		1C8D67D8B72D6BA42CCEDB648537A340 /* SVProgressHUD */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B6227E59F348F5EF643043775D84814C /* Build configuration list for PBXNativeTarget "SVProgressHUD" */;
			buildPhases = (
				3BC9638C7DEA971224B2890ED4506A7F /* Headers */,
				3D9754025AE15D104C458BBD059F9B1A /* Sources */,
				A791A81BF300936ECA0F57F67A6C4207 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = SVProgressHUD;
			productName = SVProgressHUD;
			productReference = E97D43C46A45EE515A4DA3AF94398441 /* SVProgressHUD */;
			productType = "com.apple.product-type.library.static";
		};
		C1E6645E239B950CFBC9AA5879D55D9C /* Pods-BIteFXproject */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 652FFF7CF51F1731666DF0E1E4F9A3DC /* Build configuration list for PBXNativeTarget "Pods-BIteFXproject" */;
			buildPhases = (
				2DE33C800B95BD3BB6BD330762561E77 /* Headers */,
				1BCE2DB4F6B8ADE35A456E7ABC16E19B /* Sources */,
				7240C761805D8240B70256F25EBFB5EA /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				BE016940AC5CAB0973A1E2AFCA73F3DB /* PBXTargetDependency */,
				1DD0277C150238989F6389C484C2D11C /* PBXTargetDependency */,
			);
			name = "Pods-BIteFXproject";
			productName = "Pods-BIteFXproject";
			productReference = C5B5427F9BC01DEF4533C22B72AB7717 /* Pods-BIteFXproject */;
			productType = "com.apple.product-type.library.static";
		};
		C49345AFA49B098B0384C4FA864A4868 /* QBImagePickerController */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 29BDF2C3FCB167E06173FB5B14B54B45 /* Build configuration list for PBXNativeTarget "QBImagePickerController" */;
			buildPhases = (
				05670BE1A472A543C12A9E49750527C8 /* Headers */,
				24109BAF3D40902366DCE829D4C312BB /* Sources */,
				0AF6A68BFD3CF40F3D98449DA6A48A2E /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				46C5919D3947E569F19F147AB687B09D /* PBXTargetDependency */,
			);
			name = QBImagePickerController;
			productName = QBImagePickerController;
			productReference = 20D73B80DBEDC9935666EB05B502A6E8 /* QBImagePickerController */;
			productType = "com.apple.product-type.library.static";
		};
		EAEBAE5C8257AED94B3BED47A0E0E3FA /* QBImagePickerController-QBImagePicker */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 31F7D6F21832115DD6BD3991A361A6CC /* Build configuration list for PBXNativeTarget "QBImagePickerController-QBImagePicker" */;
			buildPhases = (
				8CDAF9DB08F99F79FB01FF47FB31F793 /* Sources */,
				A9F8C9DCD68B40EEFAB23E754B037AF9 /* Frameworks */,
				8E20EFFC5A97D1D014078C0890AFB920 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "QBImagePickerController-QBImagePicker";
			productName = QBImagePicker;
			productReference = C9708630F9D9C8C32EF0B3C91B9E469D /* QBImagePickerController-QBImagePicker */;
			productType = "com.apple.product-type.bundle";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		BFDFE7DC352907FC980B868725387E98 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1600;
				LastUpgradeCheck = 1600;
			};
			buildConfigurationList = 4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */;
			compatibilityVersion = "Xcode 12.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				Base,
				de,
				en,
				es,
				ja,
				"zh-Hans",
			);
			mainGroup = CF1408CF629C7361332E53B88F7BD30C;
			minimizedProjectReferenceProxies = 0;
			preferredProjectObjectVersion = 77;
			productRefGroup = F5544A9346E8ABE1D6E29E42CCBEE35D /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				C1E6645E239B950CFBC9AA5879D55D9C /* Pods-BIteFXproject */,
				C49345AFA49B098B0384C4FA864A4868 /* QBImagePickerController */,
				EAEBAE5C8257AED94B3BED47A0E0E3FA /* QBImagePickerController-QBImagePicker */,
				1C8D67D8B72D6BA42CCEDB648537A340 /* SVProgressHUD */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		8E20EFFC5A97D1D014078C0890AFB920 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B29B7406781C991F7C9A91247CAC8EE6 /* de.lproj in Resources */,
				E506133289A05B48855763E3B030C9F7 /* en.lproj in Resources */,
				B905F6AC70EC566E033F8D17ACB125A2 /* es.lproj in Resources */,
				B04462809F79D82FDE8FC5ED2AD9D305 /* ja.lproj in Resources */,
				D93401D8D642EFE12E55C2A8712A0C59 /* QBImagePicker.storyboard in Resources */,
				CD5D8DD9C28DF50A87C8CF559E20F630 /* zh-Hans.lproj in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		1BCE2DB4F6B8ADE35A456E7ABC16E19B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				199D7CFC080E06DA4F0E9BE1EB22B984 /* Pods-BIteFXproject-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		24109BAF3D40902366DCE829D4C312BB /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				D3D158BD760C961CD6B9AF74F10CBC7A /* QBAlbumCell.m in Sources */,
				0867826199CEA5D4377F9CC8FE133329 /* QBAlbumsViewController.m in Sources */,
				062F026E5D3028788A6AFE24368D26F4 /* QBAssetCell.m in Sources */,
				E3845D5004DD62EA80E7E8E6A50A2EEA /* QBAssetsViewController.m in Sources */,
				A518053ADD09786ABB1AE74C9593A994 /* QBCheckmarkView.m in Sources */,
				276077A5354C567FC7889EDDF4AB0B5F /* QBImagePickerController.m in Sources */,
				4EA904AF2FA7311AD294BB8D201C17E0 /* QBImagePickerController-dummy.m in Sources */,
				5AECC1EE09F1EB890A9370597214CDB3 /* QBSlomoIconView.m in Sources */,
				307E6DD73554CE9DF64AD861DFD1A2C7 /* QBVideoIconView.m in Sources */,
				1F4809FBC153EF68443926782765D766 /* QBVideoIndicatorView.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3D9754025AE15D104C458BBD059F9B1A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F36626F21159F178A5B8644784709F8F /* SVIndefiniteAnimatedView.m in Sources */,
				C58FCD7ECB0C7D14E5E5FAB76E22EEA3 /* SVProgressAnimatedView.m in Sources */,
				3ACD20B11A890C63F19DCA4798561FE4 /* SVProgressHUD.m in Sources */,
				E7A44EBAC62F50F2AF3EBC40EF5DE945 /* SVProgressHUD-dummy.m in Sources */,
				6A8816A9BD17085FB1B6E96584B591DB /* SVRadialGradientLayer.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8CDAF9DB08F99F79FB01FF47FB31F793 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		1DD0277C150238989F6389C484C2D11C /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = SVProgressHUD;
			target = 1C8D67D8B72D6BA42CCEDB648537A340 /* SVProgressHUD */;
			targetProxy = 1C11C7CC53D997F4A90EEA766B4AF6E0 /* PBXContainerItemProxy */;
		};
		46C5919D3947E569F19F147AB687B09D /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "QBImagePickerController-QBImagePicker";
			target = EAEBAE5C8257AED94B3BED47A0E0E3FA /* QBImagePickerController-QBImagePicker */;
			targetProxy = 04DC57709935EB151916B5B9A5F2D073 /* PBXContainerItemProxy */;
		};
		BE016940AC5CAB0973A1E2AFCA73F3DB /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = QBImagePickerController;
			target = C49345AFA49B098B0384C4FA864A4868 /* QBImagePickerController */;
			targetProxy = C9222A8444F095D43434F40286C9A370 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		05A358194C4D627545FB3351618DDE86 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = D14BA42C7E5990DB6746426C4B8E472E /* QBImagePickerController.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/QBImagePickerController";
				IBSC_MODULE = QBImagePickerController;
				INFOPLIST_FILE = "Target Support Files/QBImagePickerController/ResourceBundle-QBImagePicker-QBImagePickerController-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				PRODUCT_NAME = QBImagePicker;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		30E0B9EFD9A5C45D0D351231E81B30B3 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_RELEASE=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Release;
		};
		6E1E2EE07ED8484DA0567731A4891247 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = D14BA42C7E5990DB6746426C4B8E472E /* QBImagePickerController.debug.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/QBImagePickerController/QBImagePickerController-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PRIVATE_HEADERS_FOLDER_PATH = "";
				PRODUCT_MODULE_NAME = QBImagePickerController;
				PRODUCT_NAME = QBImagePickerController;
				PUBLIC_HEADERS_FOLDER_PATH = "";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 6.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		822F5672ED529A2FB56BCFC9483FFB46 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F0B8BBD48E8A540835269D5D7F8E3B7B /* SVProgressHUD.debug.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/SVProgressHUD/SVProgressHUD-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PRIVATE_HEADERS_FOLDER_PATH = "";
				PRODUCT_MODULE_NAME = SVProgressHUD;
				PRODUCT_NAME = SVProgressHUD;
				PUBLIC_HEADERS_FOLDER_PATH = "";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 6.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		9410A7BFBDD81AB55E7F29051A34F5F7 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = A41E0A6F67E1EEEEF0B1F5824CD40038 /* QBImagePickerController.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/QBImagePickerController";
				IBSC_MODULE = QBImagePickerController;
				INFOPLIST_FILE = "Target Support Files/QBImagePickerController/ResourceBundle-QBImagePicker-QBImagePickerController-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				PRODUCT_NAME = QBImagePicker;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		95499B29FCF04160C727AFCAA1662A84 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = A41E0A6F67E1EEEEF0B1F5824CD40038 /* QBImagePickerController.release.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/QBImagePickerController/QBImagePickerController-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PRIVATE_HEADERS_FOLDER_PATH = "";
				PRODUCT_MODULE_NAME = QBImagePickerController;
				PRODUCT_NAME = QBImagePickerController;
				PUBLIC_HEADERS_FOLDER_PATH = "";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 6.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		9F73EC01A3B5D15DEA0C0DD3BD5937BD /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 0FC011CA91E84A6C5E0B14E7567CFF3F /* Pods-BIteFXproject.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				MACH_O_TYPE = staticlib;
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		BB8D2E9AC07594DACEA17C24630F6495 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = D6450B483B3BCD268297137ED7E4BB41 /* Pods-BIteFXproject.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				MACH_O_TYPE = staticlib;
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		D8AEE6EBA3F59991F5F2C8E4AF874B55 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 67EB15C3C5081081246D761C07C43BC2 /* SVProgressHUD.release.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/SVProgressHUD/SVProgressHUD-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PRIVATE_HEADERS_FOLDER_PATH = "";
				PRODUCT_MODULE_NAME = SVProgressHUD;
				PRODUCT_NAME = SVProgressHUD;
				PUBLIC_HEADERS_FOLDER_PATH = "";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 6.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		F4FF6A0D1970CA9705974E3CB2134802 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_DEBUG=1",
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Debug;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		29BDF2C3FCB167E06173FB5B14B54B45 /* Build configuration list for PBXNativeTarget "QBImagePickerController" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				6E1E2EE07ED8484DA0567731A4891247 /* Debug */,
				95499B29FCF04160C727AFCAA1662A84 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		31F7D6F21832115DD6BD3991A361A6CC /* Build configuration list for PBXNativeTarget "QBImagePickerController-QBImagePicker" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				05A358194C4D627545FB3351618DDE86 /* Debug */,
				9410A7BFBDD81AB55E7F29051A34F5F7 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F4FF6A0D1970CA9705974E3CB2134802 /* Debug */,
				30E0B9EFD9A5C45D0D351231E81B30B3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		652FFF7CF51F1731666DF0E1E4F9A3DC /* Build configuration list for PBXNativeTarget "Pods-BIteFXproject" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				9F73EC01A3B5D15DEA0C0DD3BD5937BD /* Debug */,
				BB8D2E9AC07594DACEA17C24630F6495 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B6227E59F348F5EF643043775D84814C /* Build configuration list for PBXNativeTarget "SVProgressHUD" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				822F5672ED529A2FB56BCFC9483FFB46 /* Debug */,
				D8AEE6EBA3F59991F5F2C8E4AF874B55 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = BFDFE7DC352907FC980B868725387E98 /* Project object */;
}
